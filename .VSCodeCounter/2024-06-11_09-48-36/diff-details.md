# Diff Details

Date : 2024-06-11 09:48:36

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 113 files,  19105 codes, 468 comments, 1983 blanks, all 21556 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [lib/app/app_config/bloc/app_config_bloc.dart](/lib/app/app_config/bloc/app_config_bloc.dart) | Dart | 28 | 0 | 4 | 32 |
| [lib/app/app_config/bloc/app_config_event.dart](/lib/app/app_config/bloc/app_config_event.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/app/app_config/bloc/app_config_state.freezed.dart](/lib/app/app_config/bloc/app_config_state.freezed.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/app/base/networking/logger_inteceptor.dart](/lib/app/base/networking/logger_inteceptor.dart) | Dart | 75 | 7 | 18 | 100 |
| [lib/app/base/networking/networking.dart](/lib/app/base/networking/networking.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/app/base/networking/workplace_auth_inteceptor.dart](/lib/app/base/networking/workplace_auth_inteceptor.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/app/constant/app_constant.dart](/lib/app/constant/app_constant.dart) | Dart | 7 | 0 | 2 | 9 |
| [lib/app/constant/constant.dart](/lib/app/constant/constant.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/app/constant/fb_wp_url.constants.dart](/lib/app/constant/fb_wp_url.constants.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/app/constant/gapo_url.constants.dart](/lib/app/constant/gapo_url.constants.dart) | Dart | 6 | 11 | 4 | 21 |
| [lib/app/features/crawler/bloc/bloc.dart](/lib/app/features/crawler/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/bloc/crawl_bloc.dart](/lib/app/features/crawler/bloc/crawl_bloc.dart) | Dart | 220 | 14 | 33 | 267 |
| [lib/app/features/crawler/bloc/crawl_event.dart](/lib/app/features/crawler/bloc/crawl_event.dart) | Dart | 11 | 0 | 4 | 15 |
| [lib/app/features/crawler/bloc/crawl_state.dart](/lib/app/features/crawler/bloc/crawl_state.dart) | Dart | 30 | 1 | 6 | 37 |
| [lib/app/features/crawler/bloc/crawl_state.freezed.dart](/lib/app/features/crawler/bloc/crawl_state.freezed.dart) | Dart | 256 | 23 | 44 | 323 |
| [lib/app/features/crawler/crawl.main.page.dart](/lib/app/features/crawler/crawl.main.page.dart) | Dart | 84 | 2 | 0 | 86 |
| [lib/app/features/crawler/crawler.dart](/lib/app/features/crawler/crawler.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/data_source/local/workplace_local.service.dart](/lib/data/data_source/local/workplace_local.service.dart) | Dart | 2 | 0 | -1 | 1 |
| [lib/data/data_source/remote/auth.service.dart](/lib/data/data_source/remote/auth.service.dart) | Dart | 21 | 9 | 5 | 35 |
| [lib/data/data_source/remote/auth.service.g.dart](/lib/data/data_source/remote/auth.service.g.dart) | Dart | 60 | 5 | 13 | 78 |
| [lib/data/data_source/remote/facebook.service.dart](/lib/data/data_source/remote/facebook.service.dart) | Dart | 20 | 10 | 6 | 36 |
| [lib/data/data_source/remote/facebook.service.g.dart](/lib/data/data_source/remote/facebook.service.g.dart) | Dart | 60 | 5 | 13 | 78 |
| [lib/data/data_source/remote/remote.dart](/lib/data/data_source/remote/remote.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/data/data_source/remote/workplace.service.dart](/lib/data/data_source/remote/workplace.service.dart) | Dart | 27 | 0 | 5 | 32 |
| [lib/data/data_source/remote/workplace.service.g.dart](/lib/data/data_source/remote/workplace.service.g.dart) | Dart | 166 | 0 | 5 | 171 |
| [lib/data/model/datetime_converter.dart](/lib/data/model/datetime_converter.dart) | Dart | 11 | 0 | 3 | 14 |
| [lib/data/model/facebook/community_response.dart](/lib/data/model/facebook/community_response.dart) | Dart | 16 | 0 | 5 | 21 |
| [lib/data/model/facebook/community_response.g.dart](/lib/data/model/facebook/community_response.g.dart) | Dart | 8 | 4 | 4 | 16 |
| [lib/data/model/facebook/facebook.dart](/lib/data/model/facebook/facebook.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/gpw/auth_reponse.dart](/lib/data/model/gpw/auth_reponse.dart) | Dart | 15 | 0 | 7 | 22 |
| [lib/data/model/gpw/auth_reponse.g.dart](/lib/data/model/gpw/auth_reponse.g.dart) | Dart | 6 | 4 | 4 | 14 |
| [lib/data/model/gpw/gpw.dart](/lib/data/model/gpw/gpw.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/model.dart](/lib/data/model/model.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/data/model/workplace/base/base.dart](/lib/data/model/workplace/base/base.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/model/workplace/base/workplace_generic_data_converter.dart](/lib/data/model/workplace/base/workplace_generic_data_converter.dart) | Dart | 12 | 0 | 0 | 12 |
| [lib/data/model/workplace/base/workplace_list_response.g.dart](/lib/data/model/workplace/base/workplace_list_response.g.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/model/workplace/base/workplace_paging_response.dart](/lib/data/model/workplace/base/workplace_paging_response.dart) | Dart | 4 | 0 | 0 | 4 |
| [lib/data/model/workplace/base/workplace_paging_response.g.dart](/lib/data/model/workplace/base/workplace_paging_response.g.dart) | Dart | 4 | 0 | 0 | 4 |
| [lib/data/model/workplace/base/workplace_user.dart](/lib/data/model/workplace/base/workplace_user.dart) | Dart | 31 | 5 | 11 | 47 |
| [lib/data/model/workplace/base/workplace_user.g.dart](/lib/data/model/workplace/base/workplace_user.g.dart) | Dart | 1,398 | 9 | 115 | 1,522 |
| [lib/data/model/workplace/community/community.dart](/lib/data/model/workplace/community/community.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/workplace/community/workplace_community_members_response.dart](/lib/data/model/workplace/community/workplace_community_members_response.dart) | Dart | 30 | 3 | 9 | 42 |
| [lib/data/model/workplace/community/workplace_community_members_response.g.dart](/lib/data/model/workplace/community/workplace_community_members_response.g.dart) | Dart | 15 | 4 | 4 | 23 |
| [lib/data/model/workplace/enums/enums.dart](/lib/data/model/workplace/enums/enums.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/workplace/enums/workplace_enums.dart](/lib/data/model/workplace/enums/workplace_enums.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/data/model/workplace/group/group.dart](/lib/data/model/workplace/group/group.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/data/model/workplace/group/workplace_group_feeds_response.dart](/lib/data/model/workplace/group/workplace_group_feeds_response.dart) | Dart | 29 | 2 | 7 | 38 |
| [lib/data/model/workplace/group/workplace_group_feeds_response.g.dart](/lib/data/model/workplace/group/workplace_group_feeds_response.g.dart) | Dart | 37 | 4 | 6 | 47 |
| [lib/data/model/workplace/group/workplace_group_members_response.dart](/lib/data/model/workplace/group/workplace_group_members_response.dart) | Dart | 29 | 3 | 9 | 41 |
| [lib/data/model/workplace/group/workplace_group_members_response.g.dart](/lib/data/model/workplace/group/workplace_group_members_response.g.dart) | Dart | 15 | 4 | 4 | 23 |
| [lib/data/model/workplace/group/workplace_group_response.dart](/lib/data/model/workplace/group/workplace_group_response.dart) | Dart | 40 | 0 | 3 | 43 |
| [lib/data/model/workplace/group/workplace_group_response.g.dart](/lib/data/model/workplace/group/workplace_group_response.g.dart) | Dart | 688 | 5 | 58 | 751 |
| [lib/data/model/workplace/post/post.dart](/lib/data/model/workplace/post/post.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.dart) | Dart | 58 | 0 | 12 | 70 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.g.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart) | Dart | 53 | 4 | 9 | 66 |
| [lib/data/model/workplace/post/workplace_post_comments_response.dart](/lib/data/model/workplace/post/workplace_post_comments_response.dart) | Dart | 19 | 0 | 5 | 24 |
| [lib/data/model/workplace/post/workplace_post_comments_response.g.dart](/lib/data/model/workplace/post/workplace_post_comments_response.g.dart) | Dart | 17 | 4 | 5 | 26 |
| [lib/data/model/workplace/workplace.dart](/lib/data/model/workplace/workplace.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/data/repository/facebook_repo_impl.dart](/lib/data/repository/facebook_repo_impl.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/data/repository/repository.dart](/lib/data/repository/repository.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/repository/workplace_repo_impl.dart](/lib/data/repository/workplace_repo_impl.dart) | Dart | 26 | 0 | 5 | 31 |
| [lib/di/component/app.component.config.dart](/lib/di/component/app.component.config.dart) | Dart | 124 | 0 | 1 | 125 |
| [lib/di/modules/auth.module.dart](/lib/di/modules/auth.module.dart) | Dart | 17 | 11 | 6 | 34 |
| [lib/di/modules/client.module.dart](/lib/di/modules/client.module.dart) | Dart | 10 | -9 | 2 | 3 |
| [lib/di/modules/database.module.dart](/lib/di/modules/database.module.dart) | Dart | 5 | 0 | 0 | 5 |
| [lib/di/modules/modules.dart](/lib/di/modules/modules.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/di/modules/url.module.dart](/lib/di/modules/url.module.dart) | Dart | 11 | 0 | 3 | 14 |
| [lib/domain/entity/base/app/app_config.entity.dart](/lib/domain/entity/base/app/app_config.entity.dart) | Dart | 11 | 0 | 1 | 12 |
| [lib/domain/entity/base/log/base_crawl_log.entity.g.dart](/lib/domain/entity/base/log/base_crawl_log.entity.g.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/domain/entity/entity.dart](/lib/domain/entity/entity.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/domain/entity/enums/base_crawl_type.dart](/lib/domain/entity/enums/base_crawl_type.dart) | Dart | 1 | 1 | 1 | 3 |
| [lib/domain/entity/gapo/gapo.dart](/lib/domain/entity/gapo/gapo.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/gapo/gp_auth.dart](/lib/domain/entity/gapo/gp_auth.dart) | Dart | 10 | 0 | 4 | 14 |
| [lib/domain/entity/gapo/gp_auth_params.dart](/lib/domain/entity/gapo/gp_auth_params.dart) | Dart | 17 | 1 | 4 | 22 |
| [lib/domain/entity/gapo/gp_auth_params.freezed.dart](/lib/domain/entity/gapo/gp_auth_params.freezed.dart) | Dart | 202 | 15 | 23 | 240 |
| [lib/domain/entity/gapo/gp_auth_params.g.dart](/lib/domain/entity/gapo/gp_auth_params.g.dart) | Dart | 9 | 4 | 4 | 17 |
| [lib/domain/entity/gapo/gpuser.dart](/lib/domain/entity/gapo/gpuser.dart) | Dart | 25 | 0 | 8 | 33 |
| [lib/domain/entity/gapo/gpuser.g.dart](/lib/domain/entity/gapo/gpuser.g.dart) | Dart | 1,350 | 9 | 147 | 1,506 |
| [lib/domain/entity/workplace/workplace.dart](/lib/domain/entity/workplace/workplace.dart) | Dart | 7 | 0 | 1 | 8 |
| [lib/domain/entity/workplace/workplace_attachment.entity.dart](/lib/domain/entity/workplace/workplace_attachment.entity.dart) | Dart | 46 | 12 | 18 | 76 |
| [lib/domain/entity/workplace/workplace_attachment.entity.g.dart](/lib/domain/entity/workplace/workplace_attachment.entity.g.dart) | Dart | 2,060 | 14 | 165 | 2,239 |
| [lib/domain/entity/workplace/workplace_comment.entity.dart](/lib/domain/entity/workplace/workplace_comment.entity.dart) | Dart | 16 | 9 | 5 | 30 |
| [lib/domain/entity/workplace/workplace_comment.entity.g.dart](/lib/domain/entity/workplace/workplace_comment.entity.g.dart) | Dart | 501 | 6 | 42 | 549 |
| [lib/domain/entity/workplace/workplace_community_member.entity.dart](/lib/domain/entity/workplace/workplace_community_member.entity.dart) | Dart | 28 | 12 | 10 | 50 |
| [lib/domain/entity/workplace/workplace_community_member.entity.g.dart](/lib/domain/entity/workplace/workplace_community_member.entity.g.dart) | Dart | 1,960 | 6 | 192 | 2,158 |
| [lib/domain/entity/workplace/workplace_group.entity.dart](/lib/domain/entity/workplace/workplace_group.entity.dart) | Dart | 40 | 9 | 7 | 56 |
| [lib/domain/entity/workplace/workplace_group.entity.g.dart](/lib/domain/entity/workplace/workplace_group.entity.g.dart) | Dart | 2,335 | 11 | 215 | 2,561 |
| [lib/domain/entity/workplace/workplace_group_feed.entity.dart](/lib/domain/entity/workplace/workplace_group_feed.entity.dart) | Dart | 30 | 10 | 8 | 48 |
| [lib/domain/entity/workplace/workplace_group_feed.entity.g.dart](/lib/domain/entity/workplace/workplace_group_feed.entity.g.dart) | Dart | 1,784 | 6 | 160 | 1,950 |
| [lib/domain/entity/workplace/workplace_group_member.entity.dart](/lib/domain/entity/workplace/workplace_group_member.entity.dart) | Dart | 30 | 12 | 10 | 52 |
| [lib/domain/entity/workplace/workplace_group_member.entity.g.dart](/lib/domain/entity/workplace/workplace_group_member.entity.g.dart) | Dart | 2,143 | 6 | 210 | 2,359 |
| [lib/domain/entity/workplace/workplace_user.entity.dart](/lib/domain/entity/workplace/workplace_user.entity.dart) | Dart | 27 | 12 | 9 | 48 |
| [lib/domain/entity/workplace/workplace_user.entity.g.dart](/lib/domain/entity/workplace/workplace_user.entity.g.dart) | Dart | 1,376 | 6 | 112 | 1,494 |
| [lib/domain/repository/facebook_repo.dart](/lib/domain/repository/facebook_repo.dart) | Dart | 4 | 1 | 2 | 7 |
| [lib/domain/repository/gapo_repo.dart](/lib/domain/repository/gapo_repo.dart) | Dart | 17 | 11 | 10 | 38 |
| [lib/domain/repository/repository.dart](/lib/domain/repository/repository.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/domain/repository/workplace_repo.dart](/lib/domain/repository/workplace_repo.dart) | Dart | 10 | 0 | 5 | 15 |
| [lib/domain/usecase/facebook_get_community.usecase.dart](/lib/domain/usecase/facebook_get_community.usecase.dart) | Dart | 19 | 10 | 7 | 36 |
| [lib/domain/usecase/usecase.dart](/lib/domain/usecase/usecase.dart) | Dart | 6 | 0 | 0 | 6 |
| [lib/domain/usecase/workplace_get_all_groups.usecase.dart](/lib/domain/usecase/workplace_get_all_groups.usecase.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/domain/usecase/workplace_get_community_members.usecase.dart](/lib/domain/usecase/workplace_get_community_members.usecase.dart) | Dart | 28 | 1 | 7 | 36 |
| [lib/domain/usecase/workplace_get_group_feeds.usecase.dart](/lib/domain/usecase/workplace_get_group_feeds.usecase.dart) | Dart | 28 | 1 | 7 | 36 |
| [lib/domain/usecase/workplace_get_group_members.usecase.dart](/lib/domain/usecase/workplace_get_group_members.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/domain/usecase/workplace_get_post_attachments.usecase.dart](/lib/domain/usecase/workplace_get_post_attachments.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/domain/usecase/workplace_get_post_comments.usecase.dart](/lib/domain/usecase/workplace_get_post_comments.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/l10n/app_en.arb](/lib/l10n/app_en.arb) | JSON | 1 | 0 | 0 | 1 |
| [lib/mapper/entity/entity.dart](/lib/mapper/entity/entity.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart](/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart) | Dart | 660 | 51 | 41 | 752 |
| [lib/mapper/entity/workplace_entity_mapper.dart](/lib/mapper/entity/workplace_entity_mapper.dart) | Dart | 101 | 12 | 12 | 125 |
| [lib/mapper/gp_mapper.auto_mappr.dart](/lib/mapper/gp_mapper.auto_mappr.dart) | Dart | 188 | 60 | 28 | 276 |
| [lib/mapper/gp_mapper.dart](/lib/mapper/gp_mapper.dart) | Dart | 61 | 13 | 10 | 84 |
| [lib/mapper/mapper.dart](/lib/mapper/mapper.dart) | Dart | 3 | 0 | 1 | 4 |
| [pubspec.yaml](/pubspec.yaml) | YAML | 2 | 0 | 0 | 2 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details