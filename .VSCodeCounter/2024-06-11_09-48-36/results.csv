"filename", "language", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Groovy", "Markdown", "C++", "Dart", "Java Properties", "Swift", "XML", "HTML", "<PERSON>tlin", "comment", "blank", "total"
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/README.md", "Markdown", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 12, 52
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/analysis_options.yaml", "YAML", 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 3, 28
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/build.gradle", "Groovy", 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 5, 12, 68
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/debug/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 4, 0, 11
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 32, 0, 0, 6, 0, 38
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/kotlin/vn/gapowork/crawler/MainActivity.kt", "Kotlin", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 3, 7
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/res/drawable-v21/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 7, 2, 13
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/res/drawable/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 7, 2, 13
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/res/values-night/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 9, 1, 19
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/res/values/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 9, 1, 19
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/build.gradle", "Groovy", 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 32
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/gradle.properties", "Java Properties", 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/gradle/wrapper/gradle-wrapper.properties", "Java Properties", 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 1, 6
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/settings.gradle", "Groovy", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 21
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/assets/images/splash_loading_1.json", "JSON", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/assets/images/splash_loading_2.json", "JSON", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 2, 14
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon-dev.appiconset/Contents.json", "JSON", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon-prod.appiconset/Contents.json", "JSON", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon-uat.appiconset/Contents.json", "JSON", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 123
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json", "JSON", 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md", "Markdown", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Base.lproj/LaunchScreen.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 36, 0, 0, 1, 1, 38
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Base.lproj/Main.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 1, 1, 27
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Runner-Bridging-Header.h", "C++", 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 2, 4, 13
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app.dart", "Dart", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 1, 8
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/app_config.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_bloc.dart", "Dart", 0, 0, 0, 0, 0, 73, 0, 0, 0, 0, 0, 0, 12, 85
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_event.dart", "Dart", 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 4, 16
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_state.dart", "Dart", 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 1, 5, 23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_state.freezed.dart", "Dart", 0, 0, 0, 0, 0, 118, 0, 0, 0, 0, 0, 15, 23, 156
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/bloc.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/widgets/app_config_body_page.dart", "Dart", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 3, 11
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/widgets/widgets.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/base.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/logger_inteceptor.dart", "Dart", 0, 0, 0, 0, 0, 75, 0, 0, 0, 0, 0, 7, 18, 100
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/networking.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/workplace_auth_inteceptor.dart", "Dart", 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 8, 37
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/app_constant.dart", "Dart", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 2, 9
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/constant.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/fb_wp_url.constants.dart", "Dart", 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 11, 5, 31
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/gapo_url.constants.dart", "Dart", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 11, 4, 21
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/bloc.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_bloc.dart", "Dart", 0, 0, 0, 0, 0, 220, 0, 0, 0, 0, 0, 14, 33, 267
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_event.dart", "Dart", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 4, 15
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.dart", "Dart", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 1, 6, 37
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.freezed.dart", "Dart", 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 23, 44, 323
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawl.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawl.main.page.dart", "Dart", 0, 0, 0, 0, 0, 101, 0, 0, 0, 0, 0, 2, 3, 106
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawler.dart", "Dart", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 1, 6
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/sync/sync.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/sync/sync.page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/unsync/unsync.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/unsync/unsync.page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/features.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/main.app.dart", "Dart", 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 4, 59
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/splash/splash.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/splash/splash.page.dart", "Dart", 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 3, 31
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/test_async.page.dart", "Dart", 0, 0, 0, 0, 0, 118, 0, 0, 0, 0, 0, 5, 26, 149
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/config/app_configs.dart", "Dart", 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 11, 40
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/config/bootstrap.dart", "Dart", 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 5, 23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/config/config.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/data_source.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/local/local.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/local/workplace_local.service.dart", "Dart", 0, 0, 0, 0, 0, 45, 0, 0, 0, 0, 0, 9, 13, 67
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/auth.service.dart", "Dart", 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 9, 5, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/auth.service.g.dart", "Dart", 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 5, 13, 78
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/facebook.service.dart", "Dart", 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 10, 6, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/facebook.service.g.dart", "Dart", 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 5, 13, 78
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/remote.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.dart", "Dart", 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 10, 11, 69
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.g.dart", "Dart", 0, 0, 0, 0, 0, 228, 0, 0, 0, 0, 0, 5, 18, 251
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/datetime_converter.dart", "Dart", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 3, 14
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/facebook/community_response.dart", "Dart", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 5, 21
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/facebook/community_response.g.dart", "Dart", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 4, 4, 16
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/facebook/facebook.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth_reponse.dart", "Dart", 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 7, 22
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth_reponse.g.dart", "Dart", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 4, 4, 14
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/gpw.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/model.dart", "Dart", 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/base.dart", "Dart", 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_generic_data_converter.dart", "Dart", 0, 0, 0, 0, 0, 42, 0, 0, 0, 0, 0, 0, 7, 49
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_list_response.dart", "Dart", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 6, 22
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_list_response.g.dart", "Dart", 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 4, 4, 20
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_paging_response.dart", "Dart", 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 7, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_paging_response.g.dart", "Dart", 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 4, 5, 26
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.dart", "Dart", 0, 0, 0, 0, 0, 31, 0, 0, 0, 0, 0, 5, 11, 47
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.g.dart", "Dart", 0, 0, 0, 0, 0, 1398, 0, 0, 0, 0, 0, 9, 115, 1522
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/community.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/workplace_community_members_response.dart", "Dart", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 3, 9, 42
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/workplace_community_members_response.g.dart", "Dart", 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 4, 4, 23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/enums/enums.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/enums/workplace_enums.dart", "Dart", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 4, 20
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/group.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_feeds_response.dart", "Dart", 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 2, 7, 38
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_feeds_response.g.dart", "Dart", 0, 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 4, 6, 47
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_members_response.dart", "Dart", 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 3, 9, 41
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_members_response.g.dart", "Dart", 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 4, 4, 23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.dart", "Dart", 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 7, 55
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.g.dart", "Dart", 0, 0, 0, 0, 0, 692, 0, 0, 0, 0, 0, 9, 62, 763
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/post.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.dart", "Dart", 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 12, 70
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart", "Dart", 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 4, 9, 66
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.dart", "Dart", 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 5, 24
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.g.dart", "Dart", 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 4, 5, 26
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/workplace.dart", "Dart", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 1, 6
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/facebook_repo_impl.dart", "Dart", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 4, 20
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/repository.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/workplace_repo_impl.dart", "Dart", 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 10, 12, 76
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/component/app.component.config.dart", "Dart", 0, 0, 0, 0, 0, 243, 0, 0, 0, 0, 0, 8, 12, 263
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/component/app.component.dart", "Dart", 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 9, 5, 32
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/component/component.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/di.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/app.module.dart", "Dart", 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 9, 6, 41
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/auth.module.dart", "Dart", 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 11, 6, 34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/client.module.dart", "Dart", 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 9, 8, 44
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/database.module.dart", "Dart", 0, 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 10, 5, 40
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/modules.dart", "Dart", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 1, 7
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/navigator.module.dart", "Dart", 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 10, 6, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/url.module.dart", "Dart", 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 9, 6, 34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/domain.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/app/app.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/app/app_config.entity.dart", "Dart", 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 9, 9, 47
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/app/app_config.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 701, 0, 0, 0, 0, 0, 6, 73, 780
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/app/locale_enum.dart", "Dart", 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 4, 13
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/base.dart", "Dart", 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/base_crawl.entity.dart", "Dart", 0, 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 10, 17, 122
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/log/base_crawl_log.entity.dart", "Dart", 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 9, 9, 40
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/log/base_crawl_log.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 1010, 0, 0, 0, 0, 0, 6, 87, 1103
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/log/log.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/status/base_crawl_status.entity.dart", "Dart", 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 12, 12, 81
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/status/base_crawl_status.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 895, 0, 0, 0, 0, 0, 8, 75, 978
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/status/status.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/entity.dart", "Dart", 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/base_crawl_status_enum.dart", "Dart", 0, 0, 0, 0, 0, 14, 0, 0, 0, 0, 0, 30, 11, 55
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/base_crawl_type.dart", "Dart", 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 16, 8, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/enums.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gapo.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth.dart", "Dart", 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 4, 14
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.dart", "Dart", 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 1, 4, 22
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.freezed.dart", "Dart", 0, 0, 0, 0, 0, 202, 0, 0, 0, 0, 0, 15, 23, 240
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.g.dart", "Dart", 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 4, 4, 17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.dart", "Dart", 0, 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 8, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.g.dart", "Dart", 0, 0, 0, 0, 0, 1350, 0, 0, 0, 0, 0, 9, 147, 1506
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace.dart", "Dart", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 1, 8
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_attachment.entity.dart", "Dart", 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 12, 18, 76
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_attachment.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 2060, 0, 0, 0, 0, 0, 14, 165, 2239
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_comment.entity.dart", "Dart", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 9, 5, 30
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_comment.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 501, 0, 0, 0, 0, 0, 6, 42, 549
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_community_member.entity.dart", "Dart", 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 12, 10, 50
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_community_member.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 1960, 0, 0, 0, 0, 0, 6, 192, 2158
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group.entity.dart", "Dart", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 9, 7, 56
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 2335, 0, 0, 0, 0, 0, 11, 215, 2561
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.dart", "Dart", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 10, 8, 48
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 1784, 0, 0, 0, 0, 0, 6, 160, 1950
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_member.entity.dart", "Dart", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 12, 10, 52
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_member.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 2143, 0, 0, 0, 0, 0, 6, 210, 2359
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user.entity.dart", "Dart", 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 12, 9, 48
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user.entity.g.dart", "Dart", 0, 0, 0, 0, 0, 1376, 0, 0, 0, 0, 0, 6, 112, 1494
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/facebook_repo.dart", "Dart", 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 1, 2, 7
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/gapo_repo.dart", "Dart", 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 11, 10, 38
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/repository.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/workplace_repo.dart", "Dart", 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 11, 10, 39
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/facebook_get_community.usecase.dart", "Dart", 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 10, 7, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/usecase.dart", "Dart", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 1, 9
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_all_groups.usecase.dart", "Dart", 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 10, 8, 45
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_app_config.usecase.dart", "Dart", 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 10, 7, 37
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_community_members.usecase.dart", "Dart", 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 1, 7, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_group_feeds.usecase.dart", "Dart", 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 1, 7, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_group_members.usecase.dart", "Dart", 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 1, 7, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_post_attachments.usecase.dart", "Dart", 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 1, 7, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_post_comments.usecase.dart", "Dart", 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 1, 7, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/flutter_gen/assets.gen.dart", "Dart", 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 10, 14, 92
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/flutter_gen/flutter_gen.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_en.arb", "JSON", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_localizations.dart", "Dart", 0, 0, 0, 0, 0, 50, 0, 0, 0, 0, 0, 75, 19, 144
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_localizations_en.dart", "Dart", 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 5, 7, 25
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_localizations_vi.dart", "Dart", 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 5, 7, 25
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_vi.arb", "JSON", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/l10n.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/main.production.dart", "Dart", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 3, 11
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/main.staging.dart", "Dart", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 3, 11
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/main.uat.dart", "Dart", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 3, 11
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/entity.dart", "Dart", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart", "Dart", 0, 0, 0, 0, 0, 660, 0, 0, 0, 0, 0, 51, 41, 752
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.dart", "Dart", 0, 0, 0, 0, 0, 101, 0, 0, 0, 0, 0, 12, 12, 125
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/gp_mapper.auto_mappr.dart", "Dart", 0, 0, 0, 0, 0, 188, 0, 0, 0, 0, 0, 60, 28, 276
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/gp_mapper.dart", "Dart", 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 13, 10, 84
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/mapper.dart", "Dart", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/route/go_router.route.dart", "Dart", 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 10, 8, 46
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/route/go_router.route.g.dart", "Dart", 0, 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 4, 18, 59
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/route/route.dart", "Dart", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/linux/main.cc", "C++", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/linux/my_application.cc", "C++", 0, 0, 0, 0, 74, 0, 0, 0, 0, 0, 0, 11, 20, 105
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/linux/my_application.h", "C++", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 7, 5, 19
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 2, 10
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/Runner/Base.lproj/MainMenu.xib", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 343, 0, 0, 0, 1, 344
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/Runner/MainFlutterWindow.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 4, 16
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 2, 4, 13
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/pubspec.yaml", "YAML", 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 1, 74
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/test/widget_test.dart", "Dart", 0, 0, 0, 0, 0, 14, 0, 0, 0, 0, 0, 10, 6, 30
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/web/index.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 0, 16, 17, 68
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/web/manifest.json", "JSON", 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/flutter_window.cpp", "C++", 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 7, 16, 72
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/flutter_window.h", "C++", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 5, 9, 34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/main.cpp", "C++", 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 0, 4, 10, 44
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/resource.h", "C++", 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 6, 2, 17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/utils.cpp", "C++", 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 2, 10, 66
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/utils.h", "C++", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 6, 6, 20
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/win32_window.cpp", "C++", 0, 0, 0, 0, 210, 0, 0, 0, 0, 0, 0, 24, 55, 289
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/win32_window.h", "C++", 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 31, 24, 103
"Total", "-", 274, 74, 94, 43, 515, 23282, 8, 46, 469, 35, 4, 1070, 2911, 28825