{"file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/assets/images/splash_loading_2.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/analysis_options.yaml": {"language": "YAML", "code": 7, "comment": 18, "blank": 3}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/build.gradle": {"language": "Groovy", "code": 27, "comment": 0, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/README.md": {"language": "<PERSON><PERSON>", "code": 40, "comment": 0, "blank": 12}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/pubspec.yaml": {"language": "YAML", "code": 67, "comment": 6, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/win32_window.cpp": {"language": "C++", "code": 210, "comment": 24, "blank": 55}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/main.staging.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 3}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/settings.gradle": {"language": "Groovy", "code": 16, "comment": 0, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/gradle.properties": {"language": "Java Properties", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/main.uat.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 3}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/app.module.dart": {"language": "Dart", "code": 26, "comment": 9, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/url.module.dart": {"language": "Dart", "code": 19, "comment": 9, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/client.module.dart": {"language": "Dart", "code": 27, "comment": 9, "blank": 8}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/navigator.module.dart": {"language": "Dart", "code": 17, "comment": 10, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/base.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/database.module.dart": {"language": "Dart", "code": 25, "comment": 10, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/main.production.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 3}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/component/app.component.dart": {"language": "Dart", "code": 18, "comment": 9, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/di.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/auth.module.dart": {"language": "Dart", "code": 17, "comment": 11, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/component/component.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/networking.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/modules.dart": {"language": "Dart", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/app_config.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/logger_inteceptor.dart": {"language": "Dart", "code": 75, "comment": 7, "blank": 18}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/main.app.dart": {"language": "Dart", "code": 55, "comment": 0, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/widgets/widgets.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/widgets/app_config_body_page.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 3}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/component/app.component.config.dart": {"language": "Dart", "code": 243, "comment": 8, "blank": 12}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_state.freezed.dart": {"language": "Dart", "code": 118, "comment": 15, "blank": 23}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/workplace_auth_inteceptor.dart": {"language": "Dart", "code": 29, "comment": 0, "blank": 8}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_all_groups.usecase.dart": {"language": "Dart", "code": 27, "comment": 10, "blank": 8}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/facebook_get_community.usecase.dart": {"language": "Dart", "code": 19, "comment": 10, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_event.dart": {"language": "Dart", "code": 12, "comment": 0, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/domain.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_group_members.usecase.dart": {"language": "Dart", "code": 27, "comment": 1, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_group_feeds.usecase.dart": {"language": "Dart", "code": 28, "comment": 1, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_bloc.dart": {"language": "Dart", "code": 73, "comment": 0, "blank": 12}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_state.dart": {"language": "Dart", "code": 17, "comment": 1, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_community_members.usecase.dart": {"language": "Dart", "code": 28, "comment": 1, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/bloc.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/usecase.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_post_attachments.usecase.dart": {"language": "Dart", "code": 27, "comment": 1, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_post_comments.usecase.dart": {"language": "Dart", "code": 27, "comment": 1, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_app_config.usecase.dart": {"language": "Dart", "code": 20, "comment": 10, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/workplace_repo.dart": {"language": "Dart", "code": 18, "comment": 11, "blank": 10}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/model.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/repository.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/gapo_repo.dart": {"language": "Dart", "code": 17, "comment": 11, "blank": 10}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/entity.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/facebook/community_response.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/facebook/facebook.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.dart": {"language": "Dart", "code": 58, "comment": 0, "blank": 12}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/post.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/facebook_repo.dart": {"language": "Dart", "code": 4, "comment": 1, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart": {"language": "Dart", "code": 53, "comment": 4, "blank": 9}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.g.dart": {"language": "Dart", "code": 17, "comment": 4, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_generic_data_converter.dart": {"language": "Dart", "code": 42, "comment": 0, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/workplace_community_members_response.dart": {"language": "Dart", "code": 30, "comment": 3, "blank": 9}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/facebook/community_response.g.dart": {"language": "Dart", "code": 8, "comment": 4, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_list_response.g.dart": {"language": "Dart", "code": 12, "comment": 4, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_list_response.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/workplace_community_members_response.g.dart": {"language": "Dart", "code": 15, "comment": 4, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.g.dart": {"language": "Dart", "code": 1398, "comment": 9, "blank": 115}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.dart": {"language": "Dart", "code": 31, "comment": 5, "blank": 11}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/base.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth_reponse.dart": {"language": "Dart", "code": 15, "comment": 0, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/community.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/enums/enums.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/workplace.dart": {"language": "Dart", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/enums/workplace_enums.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_paging_response.dart": {"language": "Dart", "code": 26, "comment": 0, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/datetime_converter.dart": {"language": "Dart", "code": 11, "comment": 0, "blank": 3}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_paging_response.g.dart": {"language": "Dart", "code": 17, "comment": 4, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/base_crawl_type.dart": {"language": "Dart", "code": 9, "comment": 16, "blank": 8}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_members_response.g.dart": {"language": "Dart", "code": 15, "comment": 4, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth_reponse.g.dart": {"language": "Dart", "code": 6, "comment": 4, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/enums.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.g.dart": {"language": "Dart", "code": 692, "comment": 9, "blank": 62}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.dart": {"language": "Dart", "code": 48, "comment": 0, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_feeds_response.g.dart": {"language": "Dart", "code": 37, "comment": 4, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/log/log.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/base_crawl_status_enum.dart": {"language": "Dart", "code": 14, "comment": 30, "blank": 11}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/base_crawl.entity.dart": {"language": "Dart", "code": 95, "comment": 10, "blank": 17}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/group.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/base.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/workplace_repo_impl.dart": {"language": "Dart", "code": 54, "comment": 10, "blank": 12}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_members_response.dart": {"language": "Dart", "code": 29, "comment": 3, "blank": 9}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/repository.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/log/base_crawl_log.entity.g.dart": {"language": "Dart", "code": 1010, "comment": 6, "blank": 87}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_feeds_response.dart": {"language": "Dart", "code": 29, "comment": 2, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/facebook_repo_impl.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/log/base_crawl_log.entity.dart": {"language": "Dart", "code": 22, "comment": 9, "blank": 9}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/local/workplace_local.service.dart": {"language": "Dart", "code": 45, "comment": 9, "blank": 13}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/data_source.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/facebook.service.dart": {"language": "Dart", "code": 20, "comment": 10, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/gpw.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/local/local.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/remote.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.g.dart": {"language": "Dart", "code": 228, "comment": 5, "blank": 18}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.dart": {"language": "Dart", "code": 48, "comment": 10, "blank": 11}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/auth.service.g.dart": {"language": "Dart", "code": 60, "comment": 5, "blank": 13}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/facebook.service.g.dart": {"language": "Dart", "code": 60, "comment": 5, "blank": 13}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/auth.service.dart": {"language": "Dart", "code": 21, "comment": 9, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/status/base_crawl_status.entity.g.dart": {"language": "Dart", "code": 895, "comment": 8, "blank": 75}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/app/app.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace.dart": {"language": "Dart", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/app/app_config.entity.g.dart": {"language": "Dart", "code": 701, "comment": 6, "blank": 73}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/assets/images/splash_loading_1.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/app/app_config.entity.dart": {"language": "Dart", "code": 29, "comment": 9, "blank": 9}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/app/locale_enum.dart": {"language": "Dart", "code": 9, "comment": 0, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.dart": {"language": "Dart", "code": 25, "comment": 0, "blank": 8}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gapo.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user.entity.dart": {"language": "Dart", "code": 27, "comment": 12, "blank": 9}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.g.dart": {"language": "Dart", "code": 1350, "comment": 9, "blank": 147}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_attachment.entity.dart": {"language": "Dart", "code": 46, "comment": 12, "blank": 18}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_attachment.entity.g.dart": {"language": "Dart", "code": 2060, "comment": 14, "blank": 165}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_member.entity.g.dart": {"language": "Dart", "code": 2143, "comment": 6, "blank": 210}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.dart": {"language": "Dart", "code": 17, "comment": 1, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/status/status.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_community_member.entity.dart": {"language": "Dart", "code": 28, "comment": 12, "blank": 10}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_comment.entity.dart": {"language": "Dart", "code": 16, "comment": 9, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.dart": {"language": "Dart", "code": 30, "comment": 10, "blank": 8}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user.entity.g.dart": {"language": "Dart", "code": 1376, "comment": 6, "blank": 112}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_member.entity.dart": {"language": "Dart", "code": 30, "comment": 12, "blank": 10}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group.entity.dart": {"language": "Dart", "code": 40, "comment": 9, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_community_member.entity.g.dart": {"language": "Dart", "code": 1960, "comment": 6, "blank": 192}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.freezed.dart": {"language": "Dart", "code": 202, "comment": 15, "blank": 23}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.g.dart": {"language": "Dart", "code": 9, "comment": 4, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.g.dart": {"language": "Dart", "code": 1784, "comment": 6, "blank": 160}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app.dart": {"language": "Dart", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group.entity.g.dart": {"language": "Dart", "code": 2335, "comment": 11, "blank": 215}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/config/app_configs.dart": {"language": "Dart", "code": 29, "comment": 0, "blank": 11}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/status/base_crawl_status.entity.dart": {"language": "Dart", "code": 57, "comment": 12, "blank": 12}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_comment.entity.g.dart": {"language": "Dart", "code": 501, "comment": 6, "blank": 42}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/fb_wp_url.constants.dart": {"language": "Dart", "code": 15, "comment": 11, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/constant.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/config/bootstrap.dart": {"language": "Dart", "code": 18, "comment": 0, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/app_constant.dart": {"language": "Dart", "code": 7, "comment": 0, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/test_async.page.dart": {"language": "Dart", "code": 118, "comment": 5, "blank": 26}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/config/config.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawl.main.page.dart": {"language": "Dart", "code": 101, "comment": 2, "blank": 3}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/gapo_url.constants.dart": {"language": "Dart", "code": 6, "comment": 11, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_event.dart": {"language": "Dart", "code": 11, "comment": 0, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawler.dart": {"language": "Dart", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/bloc.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_bloc.dart": {"language": "Dart", "code": 220, "comment": 14, "blank": 33}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/unsync/unsync.page.dart": {"language": "Dart", "code": 0, "comment": 0, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/sync/sync.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawl.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/unsync/unsync.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/features.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.dart": {"language": "Dart", "code": 30, "comment": 1, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/flutter_gen/flutter_gen.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.freezed.dart": {"language": "Dart", "code": 256, "comment": 23, "blank": 44}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/sync/sync.page.dart": {"language": "Dart", "code": 0, "comment": 0, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/flutter_window.h": {"language": "C++", "code": 20, "comment": 5, "blank": 9}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/flutter_gen/assets.gen.dart": {"language": "Dart", "code": 68, "comment": 10, "blank": 14}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/linux/my_application.h": {"language": "C++", "code": 7, "comment": 7, "blank": 5}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/linux/main.cc": {"language": "C++", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/gp_mapper.auto_mappr.dart": {"language": "Dart", "code": 188, "comment": 60, "blank": 28}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/route/route.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/route/go_router.route.dart": {"language": "Dart", "code": 28, "comment": 10, "blank": 8}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/gp_mapper.dart": {"language": "Dart", "code": 61, "comment": 13, "blank": 10}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/linux/my_application.cc": {"language": "C++", "code": 74, "comment": 11, "blank": 20}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/splash/splash.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/mapper.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/entity.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/route/go_router.route.g.dart": {"language": "Dart", "code": 37, "comment": 4, "blank": 18}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/splash/splash.page.dart": {"language": "Dart", "code": 28, "comment": 0, "blank": 3}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_localizations_en.dart": {"language": "Dart", "code": 13, "comment": 5, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/l10n.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_vi.arb": {"language": "JSON", "code": 17, "comment": 0, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_localizations_vi.dart": {"language": "Dart", "code": 13, "comment": 5, "blank": 7}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_en.arb": {"language": "JSON", "code": 4, "comment": 0, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart": {"language": "Dart", "code": 660, "comment": 51, "blank": 41}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_localizations.dart": {"language": "Dart", "code": 50, "comment": 75, "blank": 19}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/AppDelegate.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.dart": {"language": "Dart", "code": 101, "comment": 12, "blank": 12}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/main.cpp": {"language": "C++", "code": 30, "comment": 4, "blank": 10}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/resource.h": {"language": "C++", "code": 9, "comment": 6, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Base.lproj/Main.storyboard": {"language": "XML", "code": 25, "comment": 1, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"language": "XML", "code": 36, "comment": 1, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Runner-Bridging-Header.h": {"language": "C++", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json": {"language": "JSON", "code": 23, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon-uat.appiconset/Contents.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md": {"language": "<PERSON><PERSON>", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon-dev.appiconset/Contents.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 122, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/gradle/wrapper/gradle-wrapper.properties": {"language": "Java Properties", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/win32_window.h": {"language": "C++", "code": 48, "comment": 31, "blank": 24}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/AndroidManifest.xml": {"language": "XML", "code": 32, "comment": 6, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/res/drawable-v21/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/debug/AndroidManifest.xml": {"language": "XML", "code": 7, "comment": 4, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon-prod.appiconset/Contents.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/res/values/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/res/drawable/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/res/values-night/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/build.gradle": {"language": "Groovy", "code": 51, "comment": 5, "blank": 12}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/utils.cpp": {"language": "C++", "code": 54, "comment": 2, "blank": 10}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/utils.h": {"language": "C++", "code": 8, "comment": 6, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/web/manifest.json": {"language": "JSON", "code": 35, "comment": 0, "blank": 1}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/windows/runner/flutter_window.cpp": {"language": "C++", "code": 49, "comment": 7, "blank": 16}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/web/index.html": {"language": "HTML", "code": 35, "comment": 16, "blank": 17}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/test/widget_test.dart": {"language": "Dart", "code": 14, "comment": 10, "blank": 6}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/Runner/AppDelegate.swift": {"language": "Swift", "code": 8, "comment": 0, "blank": 2}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/android/app/src/main/kotlin/vn/gapowork/crawler/MainActivity.kt": {"language": "<PERSON><PERSON><PERSON>", "code": 4, "comment": 0, "blank": 3}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/Runner/MainFlutterWindow.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 4}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 68, "comment": 0, "blank": 0}, "file:///Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/macos/Runner/Base.lproj/MainMenu.xib": {"language": "XML", "code": 343, "comment": 0, "blank": 1}}