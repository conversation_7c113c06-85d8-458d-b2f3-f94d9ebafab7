# Summary

Date : 2024-06-11 09:48:36

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 215 files,  24844 codes, 1070 comments, 2911 blanks, all 28825 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 167 | 23,282 | 874 | 2,661 | 26,817 |
| C++ | 12 | 515 | 103 | 160 | 778 |
| XML | 9 | 469 | 44 | 9 | 522 |
| JSON | 11 | 274 | 0 | 3 | 277 |
| Groovy | 3 | 94 | 5 | 22 | 121 |
| YAML | 2 | 74 | 24 | 4 | 102 |
| Swift | 5 | 46 | 4 | 16 | 66 |
| Markdown | 2 | 43 | 0 | 14 | 57 |
| HTML | 1 | 35 | 16 | 17 | 68 |
| Java Properties | 2 | 8 | 0 | 2 | 10 |
| Kotlin | 1 | 4 | 0 | 3 | 7 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 215 | 24,844 | 1,070 | 2,911 | 28,825 |
| . (Files) | 3 | 114 | 24 | 16 | 154 |
| android | 12 | 171 | 47 | 33 | 251 |
| android (Files) | 3 | 46 | 0 | 11 | 57 |
| android/app | 8 | 120 | 47 | 21 | 188 |
| android/app (Files) | 1 | 51 | 5 | 12 | 68 |
| android/app/src | 7 | 69 | 42 | 9 | 120 |
| android/app/src/debug | 1 | 7 | 4 | 0 | 11 |
| android/app/src/main | 6 | 62 | 38 | 9 | 109 |
| android/app/src/main (Files) | 1 | 32 | 6 | 0 | 38 |
| android/app/src/main/kotlin | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/kotlin/vn | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/kotlin/vn/gapowork | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/kotlin/vn/gapowork/crawler | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/res | 4 | 26 | 32 | 6 | 64 |
| android/app/src/main/res/drawable | 1 | 4 | 7 | 2 | 13 |
| android/app/src/main/res/drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| android/app/src/main/res/values | 1 | 9 | 9 | 1 | 19 |
| android/app/src/main/res/values-night | 1 | 9 | 9 | 1 | 19 |
| android/gradle | 1 | 5 | 0 | 1 | 6 |
| android/gradle/wrapper | 1 | 5 | 0 | 1 | 6 |
| assets | 2 | 2 | 0 | 0 | 2 |
| assets/images | 2 | 2 | 0 | 0 | 2 |
| ios | 11 | 232 | 4 | 13 | 249 |
| ios/Runner | 10 | 225 | 2 | 9 | 236 |
| ios/Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| ios/Runner/Assets.xcassets | 6 | 151 | 0 | 4 | 155 |
| ios/Runner/Assets.xcassets/AppIcon-dev.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios/Runner/Assets.xcassets/AppIcon-prod.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios/Runner/Assets.xcassets/AppIcon-uat.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 122 | 0 | 1 | 123 |
| ios/Runner/Assets.xcassets/LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| ios/Runner/Base.lproj | 2 | 61 | 2 | 2 | 65 |
| ios/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| lib | 168 | 23,289 | 864 | 2,655 | 26,808 |
| lib (Files) | 3 | 24 | 0 | 9 | 33 |
| lib/app | 34 | 1,213 | 90 | 225 | 1,528 |
| lib/app (Files) | 3 | 180 | 5 | 31 | 216 |
| lib/app/app_config | 8 | 234 | 16 | 50 | 300 |
| lib/app/app_config (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/app/app_config/bloc | 5 | 223 | 16 | 45 | 284 |
| lib/app/app_config/widgets | 2 | 9 | 0 | 4 | 13 |
| lib/app/base | 4 | 107 | 7 | 28 | 142 |
| lib/app/base (Files) | 1 | 1 | 0 | 1 | 2 |
| lib/app/base/networking | 3 | 106 | 7 | 27 | 140 |
| lib/app/constant | 4 | 31 | 22 | 12 | 65 |
| lib/app/features | 13 | 632 | 40 | 100 | 772 |
| lib/app/features (Files) | 1 | 1 | 0 | 1 | 2 |
| lib/app/features/crawler | 12 | 631 | 40 | 99 | 770 |
| lib/app/features/crawler (Files) | 3 | 109 | 2 | 5 | 116 |
| lib/app/features/crawler/bloc | 5 | 520 | 38 | 88 | 646 |
| lib/app/features/crawler/sync | 2 | 1 | 0 | 3 | 4 |
| lib/app/features/crawler/unsync | 2 | 1 | 0 | 3 | 4 |
| lib/app/splash | 2 | 29 | 0 | 4 | 33 |
| lib/config | 3 | 49 | 0 | 17 | 66 |
| lib/data | 48 | 3,241 | 130 | 430 | 3,801 |
| lib/data (Files) | 1 | 3 | 0 | 1 | 4 |
| lib/data/data_source | 10 | 488 | 53 | 82 | 623 |
| lib/data/data_source (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/data/data_source/local | 2 | 46 | 9 | 14 | 69 |
| lib/data/data_source/remote | 7 | 440 | 44 | 67 | 551 |
| lib/data/model | 34 | 2,678 | 67 | 330 | 3,075 |
| lib/data/model (Files) | 2 | 15 | 0 | 4 | 19 |
| lib/data/model/facebook | 3 | 25 | 4 | 10 | 39 |
| lib/data/model/gpw | 3 | 22 | 4 | 12 | 38 |
| lib/data/model/workplace | 26 | 2,616 | 59 | 304 | 2,979 |
| lib/data/model/workplace (Files) | 1 | 5 | 0 | 1 | 6 |
| lib/data/model/workplace/base | 8 | 1,546 | 22 | 156 | 1,724 |
| lib/data/model/workplace/community | 3 | 46 | 7 | 14 | 67 |
| lib/data/model/workplace/enums | 2 | 17 | 0 | 5 | 22 |
| lib/data/model/workplace/group | 7 | 853 | 22 | 96 | 971 |
| lib/data/model/workplace/post | 5 | 149 | 8 | 32 | 189 |
| lib/data/repository | 3 | 72 | 10 | 17 | 99 |
| lib/di | 11 | 402 | 75 | 57 | 534 |
| lib/di (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/di/component | 3 | 263 | 17 | 18 | 298 |
| lib/di/modules | 7 | 137 | 58 | 38 | 233 |
| lib/domain | 52 | 17,110 | 324 | 1,748 | 19,182 |
| lib/domain (Files) | 1 | 3 | 0 | 1 | 4 |
| lib/domain/entity | 38 | 16,854 | 266 | 1,666 | 18,786 |
| lib/domain/entity (Files) | 1 | 4 | 0 | 1 | 5 |
| lib/domain/entity/base | 12 | 2,826 | 60 | 290 | 3,176 |
| lib/domain/entity/base (Files) | 2 | 99 | 10 | 18 | 127 |
| lib/domain/entity/base/app | 4 | 741 | 15 | 87 | 843 |
| lib/domain/entity/base/log | 3 | 1,033 | 15 | 97 | 1,145 |
| lib/domain/entity/base/status | 3 | 953 | 20 | 88 | 1,061 |
| lib/domain/entity/enums | 3 | 25 | 46 | 20 | 91 |
| lib/domain/entity/gapo | 7 | 1,616 | 29 | 191 | 1,836 |
| lib/domain/entity/workplace | 15 | 12,383 | 131 | 1,164 | 13,678 |
| lib/domain/repository | 4 | 42 | 23 | 23 | 88 |
| lib/domain/usecase | 9 | 211 | 35 | 58 | 304 |
| lib/flutter_gen | 2 | 69 | 10 | 15 | 94 |
| lib/l10n | 6 | 100 | 85 | 34 | 219 |
| lib/mapper | 6 | 1,015 | 136 | 93 | 1,244 |
| lib/mapper (Files) | 3 | 252 | 73 | 39 | 364 |
| lib/mapper/entity | 3 | 763 | 63 | 54 | 880 |
| lib/route | 3 | 66 | 14 | 27 | 107 |
| linux | 3 | 86 | 18 | 27 | 131 |
| macos | 5 | 438 | 2 | 11 | 451 |
| macos/Runner | 4 | 431 | 0 | 7 | 438 |
| macos/Runner (Files) | 2 | 20 | 0 | 6 | 26 |
| macos/Runner/Assets.xcassets | 1 | 68 | 0 | 0 | 68 |
| macos/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 68 | 0 | 0 | 68 |
| macos/Runner/Base.lproj | 1 | 343 | 0 | 1 | 344 |
| macos/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| test | 1 | 14 | 10 | 6 | 30 |
| web | 2 | 70 | 16 | 18 | 104 |
| windows | 8 | 428 | 85 | 132 | 645 |
| windows/runner | 8 | 428 | 85 | 132 | 645 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)