# Diff Details

Date : 2024-06-11 18:00:40

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 93 files,  5342 codes, 207 comments, 545 blanks, all 6094 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [build.yaml](/build.yaml) | YAML | 7 | 0 | 0 | 7 |
| [lib/app/app.dart](/lib/app/app.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/app/base/networking/gapo/authentication_interceptor.dart](/lib/app/base/networking/gapo/authentication_interceptor.dart) | Dart | 33 | 0 | 11 | 44 |
| [lib/app/base/networking/gapo/gapo.dart](/lib/app/base/networking/gapo/gapo.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/app/base/networking/gapo/gp_token_interceptor.dart](/lib/app/base/networking/gapo/gp_token_interceptor.dart) | Dart | 113 | 39 | 33 | 185 |
| [lib/app/base/networking/networking.dart](/lib/app/base/networking/networking.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/app/constant/fb_wp_url.constants.dart](/lib/app/constant/fb_wp_url.constants.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/app/constant/gapo_url.constants.dart](/lib/app/constant/gapo_url.constants.dart) | Dart | 7 | 2 | 1 | 10 |
| [lib/app/features/crawler/bloc/crawl_bloc.dart](/lib/app/features/crawler/bloc/crawl_bloc.dart) | Dart | 55 | 6 | 10 | 71 |
| [lib/app/features/crawler/crawl.main.page.dart](/lib/app/features/crawler/crawl.main.page.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/app/test_attachment.page.dart](/lib/app/test_attachment.page.dart) | Dart | 110 | 0 | 37 | 147 |
| [lib/data/data_source/remote/auth.service.dart](/lib/data/data_source/remote/auth.service.dart) | Dart | -21 | -9 | -5 | -35 |
| [lib/data/data_source/remote/auth.service.g.dart](/lib/data/data_source/remote/auth.service.g.dart) | Dart | -60 | -5 | -13 | -78 |
| [lib/data/data_source/remote/gapo/auth.service.dart](/lib/data/data_source/remote/gapo/auth.service.dart) | Dart | 25 | 9 | 6 | 40 |
| [lib/data/data_source/remote/gapo/auth.service.g.dart](/lib/data/data_source/remote/gapo/auth.service.g.dart) | Dart | 96 | 5 | 14 | 115 |
| [lib/data/data_source/remote/gapo/gapo.dart](/lib/data/data_source/remote/gapo/gapo.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/data_source/remote/gapo/upload.service.dart](/lib/data/data_source/remote/gapo/upload.service.dart) | Dart | 43 | 16 | 9 | 68 |
| [lib/data/data_source/remote/gapo/upload.service.g.dart](/lib/data/data_source/remote/gapo/upload.service.g.dart) | Dart | 164 | 5 | 15 | 184 |
| [lib/data/data_source/remote/workplace.service.dart](/lib/data/data_source/remote/workplace.service.dart) | Dart | 10 | 0 | 2 | 12 |
| [lib/data/data_source/remote/workplace.service.g.dart](/lib/data/data_source/remote/workplace.service.g.dart) | Dart | 53 | 0 | 2 | 55 |
| [lib/data/model/gpw/auth/auth.dart](/lib/data/model/gpw/auth/auth.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/gpw/auth/request/auth_check_email_request.dart](/lib/data/model/gpw/auth/request/auth_check_email_request.dart) | Dart | 16 | 9 | 8 | 33 |
| [lib/data/model/gpw/auth/request/auth_check_email_request.g.dart](/lib/data/model/gpw/auth/request/auth_check_email_request.g.dart) | Dart | 13 | 4 | 5 | 22 |
| [lib/data/model/gpw/auth/request/gp_auth_params.dart](/lib/data/model/gpw/auth/request/gp_auth_params.dart) | Dart | 18 | 1 | 4 | 23 |
| [lib/data/model/gpw/auth/request/gp_auth_params.freezed.dart](/lib/data/model/gpw/auth/request/gp_auth_params.freezed.dart) | Dart | 226 | 15 | 23 | 264 |
| [lib/data/model/gpw/auth/request/gp_auth_params.g.dart](/lib/data/model/gpw/auth/request/gp_auth_params.g.dart) | Dart | 10 | 4 | 4 | 18 |
| [lib/data/model/gpw/auth/request/request.dart](/lib/data/model/gpw/auth/request/request.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/gpw/auth/response/auth_check_mail_response.dart](/lib/data/model/gpw/auth/response/auth_check_mail_response.dart) | Dart | 18 | 9 | 8 | 35 |
| [lib/data/model/gpw/auth/response/auth_check_mail_response.g.dart](/lib/data/model/gpw/auth/response/auth_check_mail_response.g.dart) | Dart | 15 | 4 | 5 | 24 |
| [lib/data/model/gpw/auth/response/response.dart](/lib/data/model/gpw/auth/response/response.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/gpw/auth_reponse.dart](/lib/data/model/gpw/auth_reponse.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/gpw/gpw.dart](/lib/data/model/gpw/gpw.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/data/model/gpw/upload_file_response.dart](/lib/data/model/gpw/upload_file_response.dart) | Dart | 12 | 0 | 3 | 15 |
| [lib/data/model/workplace/base/workplace_generic_data_converter.dart](/lib/data/model/workplace/base/workplace_generic_data_converter.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/data/model/workplace/community/community.dart](/lib/data/model/workplace/community/community.dart) | Dart | -1 | 0 | -1 | -2 |
| [lib/data/model/workplace/community/workplace_community_members_response.dart](/lib/data/model/workplace/community/workplace_community_members_response.dart) | Dart | -30 | -3 | -9 | -42 |
| [lib/data/model/workplace/community/workplace_community_members_response.g.dart](/lib/data/model/workplace/community/workplace_community_members_response.g.dart) | Dart | -15 | -4 | -4 | -23 |
| [lib/data/model/workplace/conversation/conversation.dart](/lib/data/model/workplace/conversation/conversation.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart](/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart) | Dart | 56 | 0 | 8 | 64 |
| [lib/data/model/workplace/conversation/workplace_conversation_attachment_response.g.dart](/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.g.dart) | Dart | 34 | 4 | 6 | 44 |
| [lib/data/model/workplace/conversation/workplace_conversations_response.dart](/lib/data/model/workplace/conversation/workplace_conversations_response.dart) | Dart | 35 | 0 | 6 | 41 |
| [lib/data/model/workplace/conversation/workplace_conversations_response.g.dart](/lib/data/model/workplace/conversation/workplace_conversations_response.g.dart) | Dart | 27 | 4 | 5 | 36 |
| [lib/data/model/workplace/enums/workplace_enums.dart](/lib/data/model/workplace/enums/workplace_enums.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/data/model/workplace/group/group.dart](/lib/data/model/workplace/group/group.dart) | Dart | -1 | 0 | 0 | -1 |
| [lib/data/model/workplace/group/workplace_feeds_response.dart](/lib/data/model/workplace/group/workplace_feeds_response.dart) | Dart | 29 | 2 | 7 | 38 |
| [lib/data/model/workplace/group/workplace_feeds_response.g.dart](/lib/data/model/workplace/group/workplace_feeds_response.g.dart) | Dart | 37 | 4 | 6 | 47 |
| [lib/data/model/workplace/group/workplace_group_feeds_response.dart](/lib/data/model/workplace/group/workplace_group_feeds_response.dart) | Dart | -29 | -2 | -7 | -38 |
| [lib/data/model/workplace/group/workplace_group_feeds_response.g.dart](/lib/data/model/workplace/group/workplace_group_feeds_response.g.dart) | Dart | -37 | -4 | -6 | -47 |
| [lib/data/model/workplace/group/workplace_group_members_response.dart](/lib/data/model/workplace/group/workplace_group_members_response.dart) | Dart | -29 | -3 | -9 | -41 |
| [lib/data/model/workplace/group/workplace_group_members_response.g.dart](/lib/data/model/workplace/group/workplace_group_members_response.g.dart) | Dart | -15 | -4 | -4 | -23 |
| [lib/data/model/workplace/group/workplace_group_response.dart](/lib/data/model/workplace/group/workplace_group_response.dart) | Dart | -1 | 0 | 0 | -1 |
| [lib/data/model/workplace/group/workplace_group_response.g.dart](/lib/data/model/workplace/group/workplace_group_response.g.dart) | Dart | -651 | -5 | -56 | -712 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.g.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/repository/gapo_impl.dart](/lib/data/repository/gapo_impl.dart) | Dart | 56 | 10 | 9 | 75 |
| [lib/data/repository/repository.dart](/lib/data/repository/repository.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/repository/workplace_repo_impl.dart](/lib/data/repository/workplace_repo_impl.dart) | Dart | 7 | 0 | 2 | 9 |
| [lib/di/component/app.component.config.dart](/lib/di/component/app.component.config.dart) | Dart | 59 | 0 | 0 | 59 |
| [lib/di/modules/client.module.dart](/lib/di/modules/client.module.dart) | Dart | 15 | 0 | 4 | 19 |
| [lib/di/modules/database.module.dart](/lib/di/modules/database.module.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/di/modules/url.module.dart](/lib/di/modules/url.module.dart) | Dart | 7 | 3 | 2 | 12 |
| [lib/domain/entity/base/log/base_crawl_log.entity.g.dart](/lib/domain/entity/base/log/base_crawl_log.entity.g.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/domain/entity/enums/base_crawl_type.dart](/lib/domain/entity/enums/base_crawl_type.dart) | Dart | 1 | 1 | 1 | 3 |
| [lib/domain/entity/gapo/gapo.dart](/lib/domain/entity/gapo/gapo.dart) | Dart | -1 | 0 | 0 | -1 |
| [lib/domain/entity/gapo/gp_auth_params.dart](/lib/domain/entity/gapo/gp_auth_params.dart) | Dart | -17 | -1 | -4 | -22 |
| [lib/domain/entity/gapo/gp_auth_params.freezed.dart](/lib/domain/entity/gapo/gp_auth_params.freezed.dart) | Dart | -202 | -15 | -23 | -240 |
| [lib/domain/entity/gapo/gp_auth_params.g.dart](/lib/domain/entity/gapo/gp_auth_params.g.dart) | Dart | -9 | -4 | -4 | -17 |
| [lib/domain/entity/workplace/workplace.dart](/lib/domain/entity/workplace/workplace.dart) | Dart | 4 | 0 | 0 | 4 |
| [lib/domain/entity/workplace/workplace_attachment.entity.g.dart](/lib/domain/entity/workplace/workplace_attachment.entity.g.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/domain/entity/workplace/workplace_conversation_attachment.entity.dart](/lib/domain/entity/workplace/workplace_conversation_attachment.entity.dart) | Dart | 49 | 0 | 5 | 54 |
| [lib/domain/entity/workplace/workplace_conversation_attachment.entity.g.dart](/lib/domain/entity/workplace/workplace_conversation_attachment.entity.g.dart) | Dart | 1,930 | 10 | 149 | 2,089 |
| [lib/domain/entity/workplace/workplace_conversations.entity.dart](/lib/domain/entity/workplace/workplace_conversations.entity.dart) | Dart | 37 | 0 | 6 | 43 |
| [lib/domain/entity/workplace/workplace_conversations.entity.g.dart](/lib/domain/entity/workplace/workplace_conversations.entity.g.dart) | Dart | 1,725 | 13 | 136 | 1,874 |
| [lib/domain/entity/workplace/workplace_feed.entity.dart](/lib/domain/entity/workplace/workplace_feed.entity.dart) | Dart | 29 | 1 | 4 | 34 |
| [lib/domain/entity/workplace/workplace_feed.entity.g.dart](/lib/domain/entity/workplace/workplace_feed.entity.g.dart) | Dart | 1,331 | 6 | 100 | 1,437 |
| [lib/domain/entity/workplace/workplace_group_feed.entity.dart](/lib/domain/entity/workplace/workplace_group_feed.entity.dart) | Dart | -16 | -1 | -3 | -20 |
| [lib/domain/entity/workplace/workplace_group_feed.entity.g.dart](/lib/domain/entity/workplace/workplace_group_feed.entity.g.dart) | Dart | -1,201 | 0 | -105 | -1,306 |
| [lib/domain/entity/workplace/workplace_user_feed.entity.dart](/lib/domain/entity/workplace/workplace_user_feed.entity.dart) | Dart | 13 | 9 | 8 | 30 |
| [lib/domain/entity/workplace/workplace_user_feed.entity.g.dart](/lib/domain/entity/workplace/workplace_user_feed.entity.g.dart) | Dart | 602 | 6 | 57 | 665 |
| [lib/domain/repository/gapo_repo.dart](/lib/domain/repository/gapo_repo.dart) | Dart | -5 | -1 | -5 | -11 |
| [lib/domain/repository/workplace_repo.dart](/lib/domain/repository/workplace_repo.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/domain/usecase/gapo/gapo.dart](/lib/domain/usecase/gapo/gapo.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/usecase/gapo/gapo_auth.usecase.dart](/lib/domain/usecase/gapo/gapo_auth.usecase.dart) | Dart | 47 | 10 | 14 | 71 |
| [lib/domain/usecase/gapo/gapo_auth_check_mail.usecase.dart](/lib/domain/usecase/gapo/gapo_auth_check_mail.usecase.dart) | Dart | 17 | 9 | 5 | 31 |
| [lib/domain/usecase/gapo/gapo_upload.usecase.dart](/lib/domain/usecase/gapo/gapo_upload.usecase.dart) | Dart | 34 | 10 | 9 | 53 |
| [lib/domain/usecase/usecase.dart](/lib/domain/usecase/usecase.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/domain/usecase/workplace_get_all_groups.usecase.dart](/lib/domain/usecase/workplace_get_all_groups.usecase.dart) | Dart | -2 | 0 | 0 | -2 |
| [lib/domain/usecase/workplace_get_community_members.usecase.dart](/lib/domain/usecase/workplace_get_community_members.usecase.dart) | Dart | -2 | 0 | 0 | -2 |
| [lib/domain/usecase/workplace_get_conversations.usecase.dart](/lib/domain/usecase/workplace_get_conversations.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/domain/usecase/workplace_get_user_feeds.usecase.dart](/lib/domain/usecase/workplace_get_user_feeds.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/helpers/helpers.dart](/lib/helpers/helpers.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/helpers/jwt_token_decode.dart](/lib/helpers/jwt_token_decode.dart) | Dart | 58 | 31 | 10 | 99 |
| [lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart](/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart) | Dart | 192 | 5 | 6 | 203 |
| [lib/mapper/entity/workplace_entity_mapper.dart](/lib/mapper/entity/workplace_entity_mapper.dart) | Dart | 113 | 0 | 11 | 124 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details