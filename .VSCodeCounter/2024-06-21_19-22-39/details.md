# Details

Date : 2024-06-21 19:22:39

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 311 files,  34417 codes, 1560 comments, 4206 blanks, all 40183 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [README.md](/README.md) | Markdown | 40 | 0 | 12 | 52 |
| [analysis_options.yaml](/analysis_options.yaml) | YAML | 7 | 18 | 3 | 28 |
| [android/app/build.gradle](/android/app/build.gradle) | Groovy | 51 | 5 | 12 | 68 |
| [android/app/src/debug/AndroidManifest.xml](/android/app/src/debug/AndroidManifest.xml) | XML | 7 | 4 | 0 | 11 |
| [android/app/src/main/AndroidManifest.xml](/android/app/src/main/AndroidManifest.xml) | XML | 32 | 6 | 0 | 38 |
| [android/app/src/main/kotlin/vn/gapowork/crawler/MainActivity.kt](/android/app/src/main/kotlin/vn/gapowork/crawler/MainActivity.kt) | Kotlin | 4 | 0 | 3 | 7 |
| [android/app/src/main/res/drawable-v21/launch_background.xml](/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [android/app/src/main/res/drawable/launch_background.xml](/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [android/app/src/main/res/values-night/styles.xml](/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [android/app/src/main/res/values/styles.xml](/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [android/build.gradle](/android/build.gradle) | Groovy | 27 | 0 | 5 | 32 |
| [android/gradle.properties](/android/gradle.properties) | Java Properties | 3 | 0 | 1 | 4 |
| [android/gradle/wrapper/gradle-wrapper.properties](/android/gradle/wrapper/gradle-wrapper.properties) | Java Properties | 5 | 0 | 1 | 6 |
| [android/settings.gradle](/android/settings.gradle) | Groovy | 16 | 0 | 5 | 21 |
| [assets/images/splash_loading_1.json](/assets/images/splash_loading_1.json) | JSON | 1 | 0 | 0 | 1 |
| [assets/images/splash_loading_2.json](/assets/images/splash_loading_2.json) | JSON | 1 | 0 | 0 | 1 |
| [build.yaml](/build.yaml) | YAML | 7 | 0 | 0 | 7 |
| [ios/Runner/AppDelegate.swift](/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [ios/Runner/Assets.xcassets/AppIcon-dev.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon-dev.appiconset/Contents.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/Assets.xcassets/AppIcon-prod.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon-prod.appiconset/Contents.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/Assets.xcassets/AppIcon-uat.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon-uat.appiconset/Contents.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 122 | 0 | 1 | 123 |
| [ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [ios/Runner/Base.lproj/LaunchScreen.storyboard](/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [ios/Runner/Base.lproj/Main.storyboard](/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [ios/Runner/Runner-Bridging-Header.h](/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [ios/RunnerTests/RunnerTests.swift](/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [lib/app/app.dart](/lib/app/app.dart) | Dart | 8 | 0 | 1 | 9 |
| [lib/app/app_config/app_config.dart](/lib/app/app_config/app_config.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/app/app_config/bloc/app_config_bloc.dart](/lib/app/app_config/bloc/app_config_bloc.dart) | Dart | 86 | 1 | 14 | 101 |
| [lib/app/app_config/bloc/app_config_event.dart](/lib/app/app_config/bloc/app_config_event.dart) | Dart | 12 | 0 | 4 | 16 |
| [lib/app/app_config/bloc/app_config_state.dart](/lib/app/app_config/bloc/app_config_state.dart) | Dart | 17 | 1 | 5 | 23 |
| [lib/app/app_config/bloc/app_config_state.freezed.dart](/lib/app/app_config/bloc/app_config_state.freezed.dart) | Dart | 118 | 15 | 23 | 156 |
| [lib/app/app_config/bloc/bloc.dart](/lib/app/app_config/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/app_config/widgets/app_config_body_page.dart](/lib/app/app_config/widgets/app_config_body_page.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/app/app_config/widgets/widgets.dart](/lib/app/app_config/widgets/widgets.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/base/base.dart](/lib/app/base/base.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/base/networking/gapo/authentication_interceptor.dart](/lib/app/base/networking/gapo/authentication_interceptor.dart) | Dart | 33 | 0 | 11 | 44 |
| [lib/app/base/networking/gapo/gapo.dart](/lib/app/base/networking/gapo/gapo.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/app/base/networking/gapo/gp_token_interceptor.dart](/lib/app/base/networking/gapo/gp_token_interceptor.dart) | Dart | 113 | 39 | 33 | 185 |
| [lib/app/base/networking/logger_inteceptor.dart](/lib/app/base/networking/logger_inteceptor.dart) | Dart | 75 | 7 | 18 | 100 |
| [lib/app/base/networking/networking.dart](/lib/app/base/networking/networking.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/base/networking/workplace_auth_inteceptor.dart](/lib/app/base/networking/workplace_auth_inteceptor.dart) | Dart | 29 | 0 | 8 | 37 |
| [lib/app/constant/app_constant.dart](/lib/app/constant/app_constant.dart) | Dart | 7 | 0 | 2 | 9 |
| [lib/app/constant/constant.dart](/lib/app/constant/constant.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/constant/fb_wp_url.constants.dart](/lib/app/constant/fb_wp_url.constants.dart) | Dart | 16 | 11 | 5 | 32 |
| [lib/app/constant/gapo_url.constants.dart](/lib/app/constant/gapo_url.constants.dart) | Dart | 14 | 13 | 5 | 32 |
| [lib/app/features/crawler/bloc/bloc.dart](/lib/app/features/crawler/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/bloc/crawl_bloc.dart](/lib/app/features/crawler/bloc/crawl_bloc.dart) | Dart | 521 | 14 | 69 | 604 |
| [lib/app/features/crawler/bloc/crawl_event.dart](/lib/app/features/crawler/bloc/crawl_event.dart) | Dart | 12 | 0 | 4 | 16 |
| [lib/app/features/crawler/bloc/crawl_state.dart](/lib/app/features/crawler/bloc/crawl_state.dart) | Dart | 48 | 1 | 6 | 55 |
| [lib/app/features/crawler/bloc/crawl_state.freezed.dart](/lib/app/features/crawler/bloc/crawl_state.freezed.dart) | Dart | 549 | 31 | 78 | 658 |
| [lib/app/features/crawler/crawl.dart](/lib/app/features/crawler/crawl.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/crawl.main.page.dart](/lib/app/features/crawler/crawl.main.page.dart) | Dart | 87 | 0 | 8 | 95 |
| [lib/app/features/crawler/crawler.dart](/lib/app/features/crawler/crawler.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/app/features/crawler/sync/sync.dart](/lib/app/features/crawler/sync/sync.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/sync/sync.page.dart](/lib/app/features/crawler/sync/sync.page.dart) | Dart | 0 | 0 | 2 | 2 |
| [lib/app/features/crawler/unsync/unsync.dart](/lib/app/features/crawler/unsync/unsync.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/unsync/unsync.page.dart](/lib/app/features/crawler/unsync/unsync.page.dart) | Dart | 0 | 0 | 2 | 2 |
| [lib/app/features/features.dart](/lib/app/features/features.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/home/<USER>/bloc.dart](/lib/app/features/home/<USER>/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/home/<USER>/home_page_bloc.dart](/lib/app/features/home/<USER>/home_page_bloc.dart) | Dart | 27 | 0 | 4 | 31 |
| [lib/app/features/home/<USER>/home_page_event.dart](/lib/app/features/home/<USER>/home_page_event.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/app/features/home/<USER>/home_page_state.dart](/lib/app/features/home/<USER>/home_page_state.dart) | Dart | 15 | 0 | 3 | 18 |
| [lib/app/features/home/<USER>/home_page_state.freezed.dart](/lib/app/features/home/<USER>/home_page_state.freezed.dart) | Dart | 96 | 15 | 23 | 134 |
| [lib/app/features/home/<USER>/lib/app/features/home/<USER>
| [lib/app/features/home/<USER>/lib/app/features/home/<USER>
| [lib/app/main.app.dart](/lib/app/main.app.dart) | Dart | 55 | 0 | 4 | 59 |
| [lib/app/splash/splash.dart](/lib/app/splash/splash.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/splash/splash.page.dart](/lib/app/splash/splash.page.dart) | Dart | 28 | 0 | 3 | 31 |
| [lib/app/test_async.page.dart](/lib/app/test_async.page.dart) | Dart | 118 | 5 | 26 | 149 |
| [lib/app/test_attachment.page.dart](/lib/app/test_attachment.page.dart) | Dart | 165 | 1 | 24 | 190 |
| [lib/config/app_configs.dart](/lib/config/app_configs.dart) | Dart | 29 | 0 | 11 | 40 |
| [lib/config/bootstrap.dart](/lib/config/bootstrap.dart) | Dart | 18 | 0 | 5 | 23 |
| [lib/config/config.dart](/lib/config/config.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/data.dart](/lib/data/data.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/data/data_source/data_source.dart](/lib/data/data_source/data_source.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/data_source/local/local.dart](/lib/data/data_source/local/local.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/data_source/local/workplace_local.service.dart](/lib/data/data_source/local/workplace_local.service.dart) | Dart | 45 | 9 | 13 | 67 |
| [lib/data/data_source/remote/download/download.dart](/lib/data/data_source/remote/download/download.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/data_source/remote/download/download.service.dart](/lib/data/data_source/remote/download/download.service.dart) | Dart | 32 | 0 | 4 | 36 |
| [lib/data/data_source/remote/facebook.service.dart](/lib/data/data_source/remote/facebook.service.dart) | Dart | 20 | 10 | 6 | 36 |
| [lib/data/data_source/remote/facebook.service.g.dart](/lib/data/data_source/remote/facebook.service.g.dart) | Dart | 60 | 5 | 13 | 78 |
| [lib/data/data_source/remote/gapo/auth.service.dart](/lib/data/data_source/remote/gapo/auth.service.dart) | Dart | 29 | 9 | 7 | 45 |
| [lib/data/data_source/remote/gapo/auth.service.g.dart](/lib/data/data_source/remote/gapo/auth.service.g.dart) | Dart | 127 | 5 | 15 | 147 |
| [lib/data/data_source/remote/gapo/gapo.dart](/lib/data/data_source/remote/gapo/gapo.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/data_source/remote/gapo/upload.service.dart](/lib/data/data_source/remote/gapo/upload.service.dart) | Dart | 43 | 16 | 9 | 68 |
| [lib/data/data_source/remote/gapo/upload.service.g.dart](/lib/data/data_source/remote/gapo/upload.service.g.dart) | Dart | 162 | 5 | 15 | 182 |
| [lib/data/data_source/remote/remote.dart](/lib/data/data_source/remote/remote.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/data/data_source/remote/workplace.service.dart](/lib/data/data_source/remote/workplace.service.dart) | Dart | 67 | 10 | 14 | 91 |
| [lib/data/data_source/remote/workplace.service.g.dart](/lib/data/data_source/remote/workplace.service.g.dart) | Dart | 315 | 5 | 20 | 340 |
| [lib/data/model/datetime_converter.dart](/lib/data/model/datetime_converter.dart) | Dart | 11 | 0 | 3 | 14 |
| [lib/data/model/download/download.dart](/lib/data/model/download/download.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/download/download_params.dart](/lib/data/model/download/download_params.dart) | Dart | 13 | 0 | 2 | 15 |
| [lib/data/model/download/download_params.freezed.dart](/lib/data/model/download/download_params.freezed.dart) | Dart | 118 | 15 | 23 | 156 |
| [lib/data/model/facebook/community_response.dart](/lib/data/model/facebook/community_response.dart) | Dart | 16 | 0 | 5 | 21 |
| [lib/data/model/facebook/community_response.g.dart](/lib/data/model/facebook/community_response.g.dart) | Dart | 8 | 4 | 4 | 16 |
| [lib/data/model/facebook/facebook.dart](/lib/data/model/facebook/facebook.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/gpw/auth/auth.dart](/lib/data/model/gpw/auth/auth.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/gpw/auth/request/auth_check_email_request.dart](/lib/data/model/gpw/auth/request/auth_check_email_request.dart) | Dart | 16 | 9 | 8 | 33 |
| [lib/data/model/gpw/auth/request/auth_check_email_request.g.dart](/lib/data/model/gpw/auth/request/auth_check_email_request.g.dart) | Dart | 13 | 4 | 5 | 22 |
| [lib/data/model/gpw/auth/request/gp_auth_params.dart](/lib/data/model/gpw/auth/request/gp_auth_params.dart) | Dart | 18 | 1 | 4 | 23 |
| [lib/data/model/gpw/auth/request/gp_auth_params.freezed.dart](/lib/data/model/gpw/auth/request/gp_auth_params.freezed.dart) | Dart | 226 | 15 | 23 | 264 |
| [lib/data/model/gpw/auth/request/gp_auth_params.g.dart](/lib/data/model/gpw/auth/request/gp_auth_params.g.dart) | Dart | 10 | 4 | 4 | 18 |
| [lib/data/model/gpw/auth/request/gp_signup_params.dart](/lib/data/model/gpw/auth/request/gp_signup_params.dart) | Dart | 21 | 1 | 4 | 26 |
| [lib/data/model/gpw/auth/request/gp_signup_params.freezed.dart](/lib/data/model/gpw/auth/request/gp_signup_params.freezed.dart) | Dart | 205 | 15 | 23 | 243 |
| [lib/data/model/gpw/auth/request/gp_signup_params.g.dart](/lib/data/model/gpw/auth/request/gp_signup_params.g.dart) | Dart | 10 | 4 | 4 | 18 |
| [lib/data/model/gpw/auth/request/request.dart](/lib/data/model/gpw/auth/request/request.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/data/model/gpw/auth/response/auth_check_mail_response.dart](/lib/data/model/gpw/auth/response/auth_check_mail_response.dart) | Dart | 18 | 9 | 8 | 35 |
| [lib/data/model/gpw/auth/response/auth_check_mail_response.g.dart](/lib/data/model/gpw/auth/response/auth_check_mail_response.g.dart) | Dart | 15 | 4 | 5 | 24 |
| [lib/data/model/gpw/auth/response/response.dart](/lib/data/model/gpw/auth/response/response.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/gpw/auth_reponse.dart](/lib/data/model/gpw/auth_reponse.dart) | Dart | 17 | 0 | 8 | 25 |
| [lib/data/model/gpw/auth_reponse.g.dart](/lib/data/model/gpw/auth_reponse.g.dart) | Dart | 6 | 4 | 4 | 14 |
| [lib/data/model/gpw/gpw.dart](/lib/data/model/gpw/gpw.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/data/model/gpw/upload_file_response.dart](/lib/data/model/gpw/upload_file_response.dart) | Dart | 18 | 0 | 4 | 22 |
| [lib/data/model/model.dart](/lib/data/model/model.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/data/model/workplace/base/base.dart](/lib/data/model/workplace/base/base.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/data/model/workplace/base/workplace_generic_data_converter.dart](/lib/data/model/workplace/base/workplace_generic_data_converter.dart) | Dart | 44 | 0 | 7 | 51 |
| [lib/data/model/workplace/base/workplace_list_response.dart](/lib/data/model/workplace/base/workplace_list_response.dart) | Dart | 16 | 0 | 6 | 22 |
| [lib/data/model/workplace/base/workplace_list_response.g.dart](/lib/data/model/workplace/base/workplace_list_response.g.dart) | Dart | 12 | 4 | 4 | 20 |
| [lib/data/model/workplace/base/workplace_paging_response.dart](/lib/data/model/workplace/base/workplace_paging_response.dart) | Dart | 26 | 0 | 7 | 33 |
| [lib/data/model/workplace/base/workplace_paging_response.g.dart](/lib/data/model/workplace/base/workplace_paging_response.g.dart) | Dart | 17 | 4 | 5 | 26 |
| [lib/data/model/workplace/base/workplace_user.dart](/lib/data/model/workplace/base/workplace_user.dart) | Dart | 30 | 3 | 10 | 43 |
| [lib/data/model/workplace/base/workplace_user.g.dart](/lib/data/model/workplace/base/workplace_user.g.dart) | Dart | 27 | 4 | 5 | 36 |
| [lib/data/model/workplace/comment/comment.dart](/lib/data/model/workplace/comment/comment.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/comment/message_tags.dart](/lib/data/model/workplace/comment/message_tags.dart) | Dart | 21 | 0 | 5 | 26 |
| [lib/data/model/workplace/comment/message_tags.g.dart](/lib/data/model/workplace/comment/message_tags.g.dart) | Dart | 27 | 4 | 6 | 37 |
| [lib/data/model/workplace/comment/workplace_reactions.dart](/lib/data/model/workplace/comment/workplace_reactions.dart) | Dart | 19 | 0 | 7 | 26 |
| [lib/data/model/workplace/comment/workplace_reactions.g.dart](/lib/data/model/workplace/comment/workplace_reactions.g.dart) | Dart | 24 | 4 | 6 | 34 |
| [lib/data/model/workplace/conversation/conversation.dart](/lib/data/model/workplace/conversation/conversation.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart](/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart) | Dart | 56 | 0 | 12 | 68 |
| [lib/data/model/workplace/conversation/workplace_conversation_attachment_response.g.dart](/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.g.dart) | Dart | 34 | 4 | 6 | 44 |
| [lib/data/model/workplace/conversation/workplace_conversations_response.dart](/lib/data/model/workplace/conversation/workplace_conversations_response.dart) | Dart | 40 | 0 | 13 | 53 |
| [lib/data/model/workplace/conversation/workplace_conversations_response.g.dart](/lib/data/model/workplace/conversation/workplace_conversations_response.g.dart) | Dart | 35 | 4 | 6 | 45 |
| [lib/data/model/workplace/enums/enums.dart](/lib/data/model/workplace/enums/enums.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/workplace/enums/workplace_enums.dart](/lib/data/model/workplace/enums/workplace_enums.dart) | Dart | 48 | 0 | 16 | 64 |
| [lib/data/model/workplace/group/group.dart](/lib/data/model/workplace/group/group.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/group/workplace_feeds_response.dart](/lib/data/model/workplace/group/workplace_feeds_response.dart) | Dart | 29 | 2 | 7 | 38 |
| [lib/data/model/workplace/group/workplace_feeds_response.g.dart](/lib/data/model/workplace/group/workplace_feeds_response.g.dart) | Dart | 37 | 4 | 6 | 47 |
| [lib/data/model/workplace/group/workplace_group_response.dart](/lib/data/model/workplace/group/workplace_group_response.dart) | Dart | 46 | 0 | 11 | 57 |
| [lib/data/model/workplace/group/workplace_group_response.g.dart](/lib/data/model/workplace/group/workplace_group_response.g.dart) | Dart | 41 | 4 | 6 | 51 |
| [lib/data/model/workplace/post/post.dart](/lib/data/model/workplace/post/post.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.dart) | Dart | 58 | 0 | 12 | 70 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.g.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart) | Dart | 56 | 4 | 9 | 69 |
| [lib/data/model/workplace/post/workplace_post_comments_response.dart](/lib/data/model/workplace/post/workplace_post_comments_response.dart) | Dart | 29 | 0 | 7 | 36 |
| [lib/data/model/workplace/post/workplace_post_comments_response.g.dart](/lib/data/model/workplace/post/workplace_post_comments_response.g.dart) | Dart | 28 | 4 | 5 | 37 |
| [lib/data/model/workplace/workplace.dart](/lib/data/model/workplace/workplace.dart) | Dart | 6 | 0 | 1 | 7 |
| [lib/data/repository/download_impl.dart](/lib/data/repository/download_impl.dart) | Dart | 17 | 0 | 3 | 20 |
| [lib/data/repository/facebook_repo_impl.dart](/lib/data/repository/facebook_repo_impl.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/data/repository/gapo_impl.dart](/lib/data/repository/gapo_impl.dart) | Dart | 75 | 10 | 11 | 96 |
| [lib/data/repository/repository.dart](/lib/data/repository/repository.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/data/repository/workplace_repo_impl.dart](/lib/data/repository/workplace_repo_impl.dart) | Dart | 61 | 10 | 14 | 85 |
| [lib/di/component/app.component.config.dart](/lib/di/component/app.component.config.dart) | Dart | 334 | 8 | 12 | 354 |
| [lib/di/component/app.component.dart](/lib/di/component/app.component.dart) | Dart | 18 | 9 | 5 | 32 |
| [lib/di/component/component.dart](/lib/di/component/component.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/di/di.dart](/lib/di/di.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/di/modules/app.module.dart](/lib/di/modules/app.module.dart) | Dart | 26 | 9 | 6 | 41 |
| [lib/di/modules/auth.module.dart](/lib/di/modules/auth.module.dart) | Dart | 17 | 11 | 6 | 34 |
| [lib/di/modules/client.module.dart](/lib/di/modules/client.module.dart) | Dart | 44 | 9 | 12 | 65 |
| [lib/di/modules/database.module.dart](/lib/di/modules/database.module.dart) | Dart | 25 | 10 | 5 | 40 |
| [lib/di/modules/modules.dart](/lib/di/modules/modules.dart) | Dart | 6 | 0 | 1 | 7 |
| [lib/di/modules/navigator.module.dart](/lib/di/modules/navigator.module.dart) | Dart | 17 | 10 | 6 | 33 |
| [lib/di/modules/url.module.dart](/lib/di/modules/url.module.dart) | Dart | 26 | 12 | 8 | 46 |
| [lib/domain/domain.dart](/lib/domain/domain.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/base/app/app.dart](/lib/domain/entity/base/app/app.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/base/app/app_config.entity.dart](/lib/domain/entity/base/app/app_config.entity.dart) | Dart | 29 | 9 | 9 | 47 |
| [lib/domain/entity/base/app/app_config.entity.g.dart](/lib/domain/entity/base/app/app_config.entity.g.dart) | Dart | 701 | 6 | 73 | 780 |
| [lib/domain/entity/base/app/locale_enum.dart](/lib/domain/entity/base/app/locale_enum.dart) | Dart | 9 | 0 | 4 | 13 |
| [lib/domain/entity/base/base.dart](/lib/domain/entity/base/base.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/entity/base/base_crawl.entity.dart](/lib/domain/entity/base/base_crawl.entity.dart) | Dart | 96 | 10 | 17 | 123 |
| [lib/domain/entity/base/log/base_crawl_log.entity.dart](/lib/domain/entity/base/log/base_crawl_log.entity.dart) | Dart | 22 | 9 | 9 | 40 |
| [lib/domain/entity/base/log/base_crawl_log.entity.g.dart](/lib/domain/entity/base/log/base_crawl_log.entity.g.dart) | Dart | 1,012 | 6 | 87 | 1,105 |
| [lib/domain/entity/base/log/log.dart](/lib/domain/entity/base/log/log.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/base/status/base_crawl_status.entity.dart](/lib/domain/entity/base/status/base_crawl_status.entity.dart) | Dart | 60 | 12 | 13 | 85 |
| [lib/domain/entity/base/status/base_crawl_status.entity.g.dart](/lib/domain/entity/base/status/base_crawl_status.entity.g.dart) | Dart | 946 | 8 | 78 | 1,032 |
| [lib/domain/entity/base/status/status.dart](/lib/domain/entity/base/status/status.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/entity.dart](/lib/domain/entity/entity.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/entity/enums/base_crawl_status_enum.dart](/lib/domain/entity/enums/base_crawl_status_enum.dart) | Dart | 14 | 30 | 11 | 55 |
| [lib/domain/entity/enums/base_crawl_type.dart](/lib/domain/entity/enums/base_crawl_type.dart) | Dart | 10 | 17 | 9 | 36 |
| [lib/domain/entity/enums/enums.dart](/lib/domain/entity/enums/enums.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/entity/enums/gp_enums.dart](/lib/domain/entity/enums/gp_enums.dart) | Dart | 91 | 0 | 12 | 103 |
| [lib/domain/entity/enums/upload/gp_upload_status_enum.dart](/lib/domain/entity/enums/upload/gp_upload_status_enum.dart) | Dart | 12 | 0 | 5 | 17 |
| [lib/domain/entity/enums/upload/upload.dart](/lib/domain/entity/enums/upload/upload.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/gapo/gapo.dart](/lib/domain/entity/gapo/gapo.dart) | Dart | 9 | 0 | 1 | 10 |
| [lib/domain/entity/gapo/gp_attachement.dart](/lib/domain/entity/gapo/gp_attachement.dart) | Dart | 0 | 0 | 1 | 1 |
| [lib/domain/entity/gapo/gp_auth.dart](/lib/domain/entity/gapo/gp_auth.dart) | Dart | 10 | 0 | 4 | 14 |
| [lib/domain/entity/gapo/gp_comment.dart](/lib/domain/entity/gapo/gp_comment.dart) | Dart | 70 | 0 | 11 | 81 |
| [lib/domain/entity/gapo/gp_comment.g.dart](/lib/domain/entity/gapo/gp_comment.g.dart) | Dart | 85 | 4 | 15 | 104 |
| [lib/domain/entity/gapo/gp_group.dart](/lib/domain/entity/gapo/gp_group.dart) | Dart | 29 | 0 | 4 | 33 |
| [lib/domain/entity/gapo/gp_group.g.dart](/lib/domain/entity/gapo/gp_group.g.dart) | Dart | 39 | 4 | 7 | 50 |
| [lib/domain/entity/gapo/gp_message.dart](/lib/domain/entity/gapo/gp_message.dart) | Dart | 23 | 0 | 5 | 28 |
| [lib/domain/entity/gapo/gp_message.g.dart](/lib/domain/entity/gapo/gp_message.g.dart) | Dart | 23 | 4 | 6 | 33 |
| [lib/domain/entity/gapo/gp_post.dart](/lib/domain/entity/gapo/gp_post.dart) | Dart | 121 | 0 | 10 | 131 |
| [lib/domain/entity/gapo/gp_post.g.dart](/lib/domain/entity/gapo/gp_post.g.dart) | Dart | 140 | 4 | 16 | 160 |
| [lib/domain/entity/gapo/gp_thread.dart](/lib/domain/entity/gapo/gp_thread.dart) | Dart | 21 | 0 | 5 | 26 |
| [lib/domain/entity/gapo/gp_thread.g.dart](/lib/domain/entity/gapo/gp_thread.g.dart) | Dart | 21 | 4 | 6 | 31 |
| [lib/domain/entity/gapo/gpuser.dart](/lib/domain/entity/gapo/gpuser.dart) | Dart | 21 | 0 | 7 | 28 |
| [lib/domain/entity/gapo/gpuser.g.dart](/lib/domain/entity/gapo/gpuser.g.dart) | Dart | 19 | 4 | 5 | 28 |
| [lib/domain/entity/gapo/upload/callback/callback.dart](/lib/domain/entity/gapo/upload/callback/callback.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/gapo/upload/callback/gp_upload_callback.dart](/lib/domain/entity/gapo/upload/callback/gp_upload_callback.dart) | Dart | 46 | 8 | 13 | 67 |
| [lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart](/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart) | Dart | 17 | 12 | 5 | 34 |
| [lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.freezed.dart](/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.freezed.dart) | Dart | 123 | 15 | 23 | 161 |
| [lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.dart](/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.dart) | Dart | 13 | 14 | 7 | 34 |
| [lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.freezed.dart](/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.freezed.dart) | Dart | 183 | 27 | 37 | 247 |
| [lib/domain/entity/gapo/upload/params/gp_upload_params.dart](/lib/domain/entity/gapo/upload/params/gp_upload_params.dart) | Dart | 25 | 2 | 6 | 33 |
| [lib/domain/entity/gapo/upload/params/gp_upload_repo_params.dart](/lib/domain/entity/gapo/upload/params/gp_upload_repo_params.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/domain/entity/gapo/upload/params/params.dart](/lib/domain/entity/gapo/upload/params/params.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/gapo/upload/upload.dart](/lib/domain/entity/gapo/upload/upload.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/workplace/feed/feed.dart](/lib/domain/entity/workplace/feed/feed.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/workplace/feed/workplace_base_feed.entity.dart](/lib/domain/entity/workplace/feed/workplace_base_feed.entity.dart) | Dart | 44 | 10 | 14 | 68 |
| [lib/domain/entity/workplace/feed/workplace_base_feed.entity.g.dart](/lib/domain/entity/workplace/feed/workplace_base_feed.entity.g.dart) | Dart | 1,822 | 6 | 175 | 2,003 |
| [lib/domain/entity/workplace/feed/workplace_group_feed.entity.dart](/lib/domain/entity/workplace/feed/workplace_group_feed.entity.dart) | Dart | 18 | 9 | 6 | 33 |
| [lib/domain/entity/workplace/feed/workplace_group_feed.entity.g.dart](/lib/domain/entity/workplace/feed/workplace_group_feed.entity.g.dart) | Dart | 608 | 6 | 63 | 677 |
| [lib/domain/entity/workplace/feed/workplace_user_feed.entity.dart](/lib/domain/entity/workplace/feed/workplace_user_feed.entity.dart) | Dart | 19 | 9 | 7 | 35 |
| [lib/domain/entity/workplace/feed/workplace_user_feed.entity.g.dart](/lib/domain/entity/workplace/feed/workplace_user_feed.entity.g.dart) | Dart | 620 | 6 | 66 | 692 |
| [lib/domain/entity/workplace/group/group.dart](/lib/domain/entity/workplace/group/group.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/workplace/group/workplace_group.entity.dart](/lib/domain/entity/workplace/group/workplace_group.entity.dart) | Dart | 52 | 9 | 10 | 71 |
| [lib/domain/entity/workplace/group/workplace_group.entity.g.dart](/lib/domain/entity/workplace/group/workplace_group.entity.g.dart) | Dart | 2,575 | 11 | 245 | 2,831 |
| [lib/domain/entity/workplace/other/other.dart](/lib/domain/entity/workplace/other/other.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/workplace/other/workplace_attachment.entity.dart](/lib/domain/entity/workplace/other/workplace_attachment.entity.dart) | Dart | 66 | 11 | 24 | 101 |
| [lib/domain/entity/workplace/other/workplace_attachment.entity.g.dart](/lib/domain/entity/workplace/other/workplace_attachment.entity.g.dart) | Dart | 2,979 | 19 | 240 | 3,238 |
| [lib/domain/entity/workplace/other/workplace_comment.entity.dart](/lib/domain/entity/workplace/other/workplace_comment.entity.dart) | Dart | 62 | 9 | 16 | 87 |
| [lib/domain/entity/workplace/other/workplace_comment.entity.g.dart](/lib/domain/entity/workplace/other/workplace_comment.entity.g.dart) | Dart | 2,614 | 13 | 222 | 2,849 |
| [lib/domain/entity/workplace/thread/thread.dart](/lib/domain/entity/workplace/thread/thread.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.dart](/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.dart) | Dart | 61 | 9 | 13 | 83 |
| [lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.g.dart](/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.g.dart) | Dart | 2,909 | 13 | 247 | 3,169 |
| [lib/domain/entity/workplace/thread/workplace_conversations.entity.dart](/lib/domain/entity/workplace/thread/workplace_conversations.entity.dart) | Dart | 77 | 10 | 21 | 108 |
| [lib/domain/entity/workplace/thread/workplace_conversations.entity.g.dart](/lib/domain/entity/workplace/thread/workplace_conversations.entity.g.dart) | Dart | 2,897 | 10 | 284 | 3,191 |
| [lib/domain/entity/workplace/user/user.dart](/lib/domain/entity/workplace/user/user.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/workplace/user/workplace_community_member.entity.dart](/lib/domain/entity/workplace/user/workplace_community_member.entity.dart) | Dart | 32 | 12 | 11 | 55 |
| [lib/domain/entity/workplace/user/workplace_community_member.entity.g.dart](/lib/domain/entity/workplace/user/workplace_community_member.entity.g.dart) | Dart | 2,239 | 6 | 219 | 2,464 |
| [lib/domain/entity/workplace/workplace.dart](/lib/domain/entity/workplace/workplace.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/domain/repository/download_repo.dart](/lib/domain/repository/download_repo.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/repository/facebook_repo.dart](/lib/domain/repository/facebook_repo.dart) | Dart | 4 | 1 | 2 | 7 |
| [lib/domain/repository/gapo_repo.dart](/lib/domain/repository/gapo_repo.dart) | Dart | 15 | 10 | 6 | 31 |
| [lib/domain/repository/repository.dart](/lib/domain/repository/repository.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/repository/workplace_repo.dart](/lib/domain/repository/workplace_repo.dart) | Dart | 22 | 11 | 12 | 45 |
| [lib/domain/usecase/download_file.usecase.dart](/lib/domain/usecase/download_file.usecase.dart) | Dart | 31 | 0 | 9 | 40 |
| [lib/domain/usecase/download_then_upload.usecase.dart](/lib/domain/usecase/download_then_upload.usecase.dart) | Dart | 61 | 12 | 9 | 82 |
| [lib/domain/usecase/facebook_get_community.usecase.dart](/lib/domain/usecase/facebook_get_community.usecase.dart) | Dart | 19 | 10 | 7 | 36 |
| [lib/domain/usecase/feed_attachment.usecase.dart](/lib/domain/usecase/feed_attachment.usecase.dart) | Dart | 70 | 10 | 11 | 91 |
| [lib/domain/usecase/gapo/gapo.dart](/lib/domain/usecase/gapo/gapo.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/domain/usecase/gapo/gapo_auth.usecase.dart](/lib/domain/usecase/gapo/gapo_auth.usecase.dart) | Dart | 47 | 10 | 14 | 71 |
| [lib/domain/usecase/gapo/gapo_auth_check_mail.usecase.dart](/lib/domain/usecase/gapo/gapo_auth_check_mail.usecase.dart) | Dart | 17 | 9 | 5 | 31 |
| [lib/domain/usecase/gapo/gapo_create_user.usecase.dart](/lib/domain/usecase/gapo/gapo_create_user.usecase.dart) | Dart | 22 | 9 | 7 | 38 |
| [lib/domain/usecase/gapo/gapo_login.usecase.dart](/lib/domain/usecase/gapo/gapo_login.usecase.dart) | Dart | 68 | 0 | 13 | 81 |
| [lib/domain/usecase/gapo/gapo_upload.usecase.dart](/lib/domain/usecase/gapo/gapo_upload.usecase.dart) | Dart | 41 | 10 | 13 | 64 |
| [lib/domain/usecase/usecase.dart](/lib/domain/usecase/usecase.dart) | Dart | 14 | 0 | 1 | 15 |
| [lib/domain/usecase/workplace_get_all_groups.usecase.dart](/lib/domain/usecase/workplace_get_all_groups.usecase.dart) | Dart | 25 | 10 | 8 | 43 |
| [lib/domain/usecase/workplace_get_app_config.usecase.dart](/lib/domain/usecase/workplace_get_app_config.usecase.dart) | Dart | 20 | 10 | 7 | 37 |
| [lib/domain/usecase/workplace_get_community_members.usecase.dart](/lib/domain/usecase/workplace_get_community_members.usecase.dart) | Dart | 26 | 1 | 7 | 34 |
| [lib/domain/usecase/workplace_get_conversations.usecase.dart](/lib/domain/usecase/workplace_get_conversations.usecase.dart) | Dart | 33 | 10 | 8 | 51 |
| [lib/domain/usecase/workplace_get_group_feeds.usecase.dart](/lib/domain/usecase/workplace_get_group_feeds.usecase.dart) | Dart | 28 | 1 | 7 | 36 |
| [lib/domain/usecase/workplace_get_group_members.usecase.dart](/lib/domain/usecase/workplace_get_group_members.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/domain/usecase/workplace_get_post_attachments.usecase.dart](/lib/domain/usecase/workplace_get_post_attachments.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/domain/usecase/workplace_get_post_comments.usecase.dart](/lib/domain/usecase/workplace_get_post_comments.usecase.dart) | Dart | 27 | 10 | 8 | 45 |
| [lib/domain/usecase/workplace_get_user_feeds.usecase.dart](/lib/domain/usecase/workplace_get_user_feeds.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/flutter_gen/assets.gen.dart](/lib/flutter_gen/assets.gen.dart) | Dart | 68 | 10 | 14 | 92 |
| [lib/flutter_gen/flutter_gen.dart](/lib/flutter_gen/flutter_gen.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/helpers/csv.dart](/lib/helpers/csv.dart) | Dart | 32 | 0 | 5 | 37 |
| [lib/helpers/file_helper.dart](/lib/helpers/file_helper.dart) | Dart | 36 | 0 | 9 | 45 |
| [lib/helpers/gp_upload_management.dart](/lib/helpers/gp_upload_management.dart) | Dart | 143 | 18 | 41 | 202 |
| [lib/helpers/helpers.dart](/lib/helpers/helpers.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/helpers/jwt_token_decode.dart](/lib/helpers/jwt_token_decode.dart) | Dart | 58 | 31 | 10 | 99 |
| [lib/l10n/app_en.arb](/lib/l10n/app_en.arb) | JSON | 4 | 0 | 0 | 4 |
| [lib/l10n/app_localizations.dart](/lib/l10n/app_localizations.dart) | Dart | 50 | 75 | 19 | 144 |
| [lib/l10n/app_localizations_en.dart](/lib/l10n/app_localizations_en.dart) | Dart | 13 | 5 | 7 | 25 |
| [lib/l10n/app_localizations_vi.dart](/lib/l10n/app_localizations_vi.dart) | Dart | 13 | 5 | 7 | 25 |
| [lib/l10n/app_vi.arb](/lib/l10n/app_vi.arb) | JSON | 17 | 0 | 0 | 17 |
| [lib/l10n/l10n.dart](/lib/l10n/l10n.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/main.production.dart](/lib/main.production.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/main.staging.dart](/lib/main.staging.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/main.uat.dart](/lib/main.uat.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/mapper/entity/entity.dart](/lib/mapper/entity/entity.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/mapper/entity/gapo_entity_mapper.auto_mappr.dart](/lib/mapper/entity/gapo_entity_mapper.auto_mappr.dart) | Dart | 487 | 47 | 37 | 571 |
| [lib/mapper/entity/gapo_entity_mapper.dart](/lib/mapper/entity/gapo_entity_mapper.dart) | Dart | 219 | 18 | 36 | 273 |
| [lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart](/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart) | Dart | 860 | 56 | 47 | 963 |
| [lib/mapper/entity/workplace_entity_mapper.dart](/lib/mapper/entity/workplace_entity_mapper.dart) | Dart | 216 | 12 | 23 | 251 |
| [lib/mapper/gp_mapper.auto_mappr.dart](/lib/mapper/gp_mapper.auto_mappr.dart) | Dart | 191 | 60 | 28 | 279 |
| [lib/mapper/gp_mapper.dart](/lib/mapper/gp_mapper.dart) | Dart | 62 | 13 | 10 | 85 |
| [lib/mapper/mapper.dart](/lib/mapper/mapper.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/route/go_router.route.dart](/lib/route/go_router.route.dart) | Dart | 41 | 10 | 10 | 61 |
| [lib/route/go_router.route.g.dart](/lib/route/go_router.route.g.dart) | Dart | 53 | 4 | 25 | 82 |
| [lib/route/route.dart](/lib/route/route.dart) | Dart | 1 | 0 | 1 | 2 |
| [linux/main.cc](/linux/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [linux/my_application.cc](/linux/my_application.cc) | C++ | 74 | 11 | 20 | 105 |
| [linux/my_application.h](/linux/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [macos/Runner/AppDelegate.swift](/macos/Runner/AppDelegate.swift) | Swift | 8 | 0 | 2 | 10 |
| [macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 0 | 68 |
| [macos/Runner/Base.lproj/MainMenu.xib](/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [macos/Runner/MainFlutterWindow.swift](/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [macos/RunnerTests/RunnerTests.swift](/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [project_configs/flutter_launcher_icons-dev.yaml](/project_configs/flutter_launcher_icons-dev.yaml) | YAML | 17 | 0 | 0 | 17 |
| [project_configs/flutter_launcher_icons-prod.yaml](/project_configs/flutter_launcher_icons-prod.yaml) | YAML | 17 | 0 | 0 | 17 |
| [project_configs/flutter_launcher_icons-uat.yaml](/project_configs/flutter_launcher_icons-uat.yaml) | YAML | 17 | 0 | 0 | 17 |
| [project_configs/package_rename_config-dev.yaml](/project_configs/package_rename_config-dev.yaml) | YAML | 27 | 0 | 6 | 33 |
| [project_configs/package_rename_config-prod.yaml](/project_configs/package_rename_config-prod.yaml) | YAML | 27 | 0 | 6 | 33 |
| [project_configs/package_rename_config-uat.yaml](/project_configs/package_rename_config-uat.yaml) | YAML | 27 | 0 | 6 | 33 |
| [pubspec.yaml](/pubspec.yaml) | YAML | 68 | 6 | 1 | 75 |
| [test/widget_test.dart](/test/widget_test.dart) | Dart | 14 | 10 | 6 | 30 |
| [web/index.html](/web/index.html) | HTML | 35 | 16 | 17 | 68 |
| [web/manifest.json](/web/manifest.json) | JSON | 35 | 0 | 1 | 36 |
| [windows/runner/flutter_window.cpp](/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [windows/runner/flutter_window.h](/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [windows/runner/main.cpp](/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [windows/runner/resource.h](/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [windows/runner/utils.cpp](/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [windows/runner/utils.h](/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [windows/runner/win32_window.cpp](/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [windows/runner/win32_window.h](/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)