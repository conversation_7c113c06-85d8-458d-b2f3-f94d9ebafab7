# Diff Details

Date : 2024-06-21 19:22:39

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 164 files,  4231 codes, 283 comments, 750 blanks, all 5264 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [lib/app/app_config/bloc/app_config_bloc.dart](/lib/app/app_config/bloc/app_config_bloc.dart) | Dart | 13 | 1 | 2 | 16 |
| [lib/app/constant/gapo_url.constants.dart](/lib/app/constant/gapo_url.constants.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/app/features/crawler/bloc/crawl_bloc.dart](/lib/app/features/crawler/bloc/crawl_bloc.dart) | Dart | 246 | -6 | 26 | 266 |
| [lib/app/features/crawler/bloc/crawl_event.dart](/lib/app/features/crawler/bloc/crawl_event.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/app/features/crawler/bloc/crawl_state.dart](/lib/app/features/crawler/bloc/crawl_state.dart) | Dart | 18 | 0 | 0 | 18 |
| [lib/app/features/crawler/bloc/crawl_state.freezed.dart](/lib/app/features/crawler/bloc/crawl_state.freezed.dart) | Dart | 293 | 8 | 34 | 335 |
| [lib/app/features/crawler/crawl.main.page.dart](/lib/app/features/crawler/crawl.main.page.dart) | Dart | -18 | -2 | 3 | -17 |
| [lib/app/features/home/<USER>/bloc.dart](/lib/app/features/home/<USER>/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/home/<USER>/home_page_bloc.dart](/lib/app/features/home/<USER>/home_page_bloc.dart) | Dart | 27 | 0 | 4 | 31 |
| [lib/app/features/home/<USER>/home_page_event.dart](/lib/app/features/home/<USER>/home_page_event.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/app/features/home/<USER>/home_page_state.dart](/lib/app/features/home/<USER>/home_page_state.dart) | Dart | 15 | 0 | 3 | 18 |
| [lib/app/features/home/<USER>/home_page_state.freezed.dart](/lib/app/features/home/<USER>/home_page_state.freezed.dart) | Dart | 96 | 15 | 23 | 134 |
| [lib/app/features/home/<USER>/lib/app/features/home/<USER>
| [lib/app/features/home/<USER>/lib/app/features/home/<USER>
| [lib/app/test_attachment.page.dart](/lib/app/test_attachment.page.dart) | Dart | 55 | 1 | -13 | 43 |
| [lib/data/data_source/remote/download/download.dart](/lib/data/data_source/remote/download/download.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/data_source/remote/download/download.service.dart](/lib/data/data_source/remote/download/download.service.dart) | Dart | 32 | 0 | 4 | 36 |
| [lib/data/data_source/remote/gapo/auth.service.dart](/lib/data/data_source/remote/gapo/auth.service.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/data/data_source/remote/gapo/auth.service.g.dart](/lib/data/data_source/remote/gapo/auth.service.g.dart) | Dart | 31 | 0 | 1 | 32 |
| [lib/data/data_source/remote/gapo/upload.service.g.dart](/lib/data/data_source/remote/gapo/upload.service.g.dart) | Dart | -2 | 0 | 0 | -2 |
| [lib/data/data_source/remote/remote.dart](/lib/data/data_source/remote/remote.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/data_source/remote/workplace.service.dart](/lib/data/data_source/remote/workplace.service.dart) | Dart | 9 | 0 | 1 | 10 |
| [lib/data/data_source/remote/workplace.service.g.dart](/lib/data/data_source/remote/workplace.service.g.dart) | Dart | 34 | 0 | 0 | 34 |
| [lib/data/model/download/download.dart](/lib/data/model/download/download.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/download/download_params.dart](/lib/data/model/download/download_params.dart) | Dart | 13 | 0 | 2 | 15 |
| [lib/data/model/download/download_params.freezed.dart](/lib/data/model/download/download_params.freezed.dart) | Dart | 118 | 15 | 23 | 156 |
| [lib/data/model/gpw/auth/request/gp_signup_params.dart](/lib/data/model/gpw/auth/request/gp_signup_params.dart) | Dart | 21 | 1 | 4 | 26 |
| [lib/data/model/gpw/auth/request/gp_signup_params.freezed.dart](/lib/data/model/gpw/auth/request/gp_signup_params.freezed.dart) | Dart | 205 | 15 | 23 | 243 |
| [lib/data/model/gpw/auth/request/gp_signup_params.g.dart](/lib/data/model/gpw/auth/request/gp_signup_params.g.dart) | Dart | 10 | 4 | 4 | 18 |
| [lib/data/model/gpw/auth/request/request.dart](/lib/data/model/gpw/auth/request/request.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/model/gpw/upload_file_response.dart](/lib/data/model/gpw/upload_file_response.dart) | Dart | 6 | 0 | 1 | 7 |
| [lib/data/model/model.dart](/lib/data/model/model.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/model/workplace/base/workplace_user.dart](/lib/data/model/workplace/base/workplace_user.dart) | Dart | -1 | -2 | -1 | -4 |
| [lib/data/model/workplace/base/workplace_user.g.dart](/lib/data/model/workplace/base/workplace_user.g.dart) | Dart | -1,371 | -5 | -110 | -1,486 |
| [lib/data/model/workplace/comment/comment.dart](/lib/data/model/workplace/comment/comment.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/comment/message_tags.dart](/lib/data/model/workplace/comment/message_tags.dart) | Dart | 21 | 0 | 5 | 26 |
| [lib/data/model/workplace/comment/message_tags.g.dart](/lib/data/model/workplace/comment/message_tags.g.dart) | Dart | 27 | 4 | 6 | 37 |
| [lib/data/model/workplace/comment/workplace_reactions.dart](/lib/data/model/workplace/comment/workplace_reactions.dart) | Dart | 19 | 0 | 7 | 26 |
| [lib/data/model/workplace/comment/workplace_reactions.g.dart](/lib/data/model/workplace/comment/workplace_reactions.g.dart) | Dart | 24 | 4 | 6 | 34 |
| [lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart](/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart) | Dart | 0 | 0 | 4 | 4 |
| [lib/data/model/workplace/conversation/workplace_conversations_response.dart](/lib/data/model/workplace/conversation/workplace_conversations_response.dart) | Dart | 5 | 0 | 7 | 12 |
| [lib/data/model/workplace/conversation/workplace_conversations_response.g.dart](/lib/data/model/workplace/conversation/workplace_conversations_response.g.dart) | Dart | 8 | 0 | 1 | 9 |
| [lib/data/model/workplace/enums/workplace_enums.dart](/lib/data/model/workplace/enums/workplace_enums.dart) | Dart | 30 | 0 | 12 | 42 |
| [lib/data/model/workplace/group/workplace_group_response.dart](/lib/data/model/workplace/group/workplace_group_response.dart) | Dart | -1 | 0 | 4 | 3 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.g.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/data/model/workplace/post/workplace_post_comments_response.dart](/lib/data/model/workplace/post/workplace_post_comments_response.dart) | Dart | 10 | 0 | 2 | 12 |
| [lib/data/model/workplace/post/workplace_post_comments_response.g.dart](/lib/data/model/workplace/post/workplace_post_comments_response.g.dart) | Dart | 11 | 0 | 0 | 11 |
| [lib/data/model/workplace/workplace.dart](/lib/data/model/workplace/workplace.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/data/repository/download_impl.dart](/lib/data/repository/download_impl.dart) | Dart | 17 | 0 | 3 | 20 |
| [lib/data/repository/gapo_impl.dart](/lib/data/repository/gapo_impl.dart) | Dart | 19 | 0 | 2 | 21 |
| [lib/data/repository/repository.dart](/lib/data/repository/repository.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/di/component/app.component.config.dart](/lib/di/component/app.component.config.dart) | Dart | 32 | 0 | 0 | 32 |
| [lib/di/modules/client.module.dart](/lib/di/modules/client.module.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/di/modules/database.module.dart](/lib/di/modules/database.module.dart) | Dart | -2 | 0 | 0 | -2 |
| [lib/domain/entity/base/base_crawl.entity.dart](/lib/domain/entity/base/base_crawl.entity.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/domain/entity/base/status/base_crawl_status.entity.dart](/lib/domain/entity/base/status/base_crawl_status.entity.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/base/status/base_crawl_status.entity.g.dart](/lib/domain/entity/base/status/base_crawl_status.entity.g.dart) | Dart | 51 | 0 | 3 | 54 |
| [lib/domain/entity/enums/enums.dart](/lib/domain/entity/enums/enums.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/domain/entity/enums/gp_enums.dart](/lib/domain/entity/enums/gp_enums.dart) | Dart | 91 | 0 | 12 | 103 |
| [lib/domain/entity/enums/upload/gp_upload_status_enum.dart](/lib/domain/entity/enums/upload/gp_upload_status_enum.dart) | Dart | 12 | 0 | 5 | 17 |
| [lib/domain/entity/enums/upload/upload.dart](/lib/domain/entity/enums/upload/upload.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/gapo/gapo.dart](/lib/domain/entity/gapo/gapo.dart) | Dart | 7 | 0 | 0 | 7 |
| [lib/domain/entity/gapo/gp_attachement.dart](/lib/domain/entity/gapo/gp_attachement.dart) | Dart | 0 | 0 | 1 | 1 |
| [lib/domain/entity/gapo/gp_comment.dart](/lib/domain/entity/gapo/gp_comment.dart) | Dart | 70 | 0 | 11 | 81 |
| [lib/domain/entity/gapo/gp_comment.g.dart](/lib/domain/entity/gapo/gp_comment.g.dart) | Dart | 85 | 4 | 15 | 104 |
| [lib/domain/entity/gapo/gp_group.dart](/lib/domain/entity/gapo/gp_group.dart) | Dart | 29 | 0 | 4 | 33 |
| [lib/domain/entity/gapo/gp_group.g.dart](/lib/domain/entity/gapo/gp_group.g.dart) | Dart | 39 | 4 | 7 | 50 |
| [lib/domain/entity/gapo/gp_message.dart](/lib/domain/entity/gapo/gp_message.dart) | Dart | 23 | 0 | 5 | 28 |
| [lib/domain/entity/gapo/gp_message.g.dart](/lib/domain/entity/gapo/gp_message.g.dart) | Dart | 23 | 4 | 6 | 33 |
| [lib/domain/entity/gapo/gp_post.dart](/lib/domain/entity/gapo/gp_post.dart) | Dart | 121 | 0 | 10 | 131 |
| [lib/domain/entity/gapo/gp_post.g.dart](/lib/domain/entity/gapo/gp_post.g.dart) | Dart | 140 | 4 | 16 | 160 |
| [lib/domain/entity/gapo/gp_thread.dart](/lib/domain/entity/gapo/gp_thread.dart) | Dart | 21 | 0 | 5 | 26 |
| [lib/domain/entity/gapo/gp_thread.g.dart](/lib/domain/entity/gapo/gp_thread.g.dart) | Dart | 21 | 4 | 6 | 31 |
| [lib/domain/entity/gapo/gpuser.dart](/lib/domain/entity/gapo/gpuser.dart) | Dart | -4 | 0 | -1 | -5 |
| [lib/domain/entity/gapo/gpuser.g.dart](/lib/domain/entity/gapo/gpuser.g.dart) | Dart | -1,331 | -5 | -142 | -1,478 |
| [lib/domain/entity/gapo/upload/callback/callback.dart](/lib/domain/entity/gapo/upload/callback/callback.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/gapo/upload/callback/gp_upload_callback.dart](/lib/domain/entity/gapo/upload/callback/gp_upload_callback.dart) | Dart | 46 | 8 | 13 | 67 |
| [lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart](/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart) | Dart | 17 | 12 | 5 | 34 |
| [lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.freezed.dart](/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.freezed.dart) | Dart | 123 | 15 | 23 | 161 |
| [lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.dart](/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.dart) | Dart | 13 | 14 | 7 | 34 |
| [lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.freezed.dart](/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.freezed.dart) | Dart | 183 | 27 | 37 | 247 |
| [lib/domain/entity/gapo/upload/params/gp_upload_params.dart](/lib/domain/entity/gapo/upload/params/gp_upload_params.dart) | Dart | 25 | 2 | 6 | 33 |
| [lib/domain/entity/gapo/upload/params/gp_upload_repo_params.dart](/lib/domain/entity/gapo/upload/params/gp_upload_repo_params.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/domain/entity/gapo/upload/params/params.dart](/lib/domain/entity/gapo/upload/params/params.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/gapo/upload/upload.dart](/lib/domain/entity/gapo/upload/upload.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/workplace/feed/feed.dart](/lib/domain/entity/workplace/feed/feed.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/workplace/feed/workplace_base_feed.entity.dart](/lib/domain/entity/workplace/feed/workplace_base_feed.entity.dart) | Dart | 44 | 10 | 14 | 68 |
| [lib/domain/entity/workplace/feed/workplace_base_feed.entity.g.dart](/lib/domain/entity/workplace/feed/workplace_base_feed.entity.g.dart) | Dart | 1,822 | 6 | 175 | 2,003 |
| [lib/domain/entity/workplace/feed/workplace_group_feed.entity.dart](/lib/domain/entity/workplace/feed/workplace_group_feed.entity.dart) | Dart | 18 | 9 | 6 | 33 |
| [lib/domain/entity/workplace/feed/workplace_group_feed.entity.g.dart](/lib/domain/entity/workplace/feed/workplace_group_feed.entity.g.dart) | Dart | 608 | 6 | 63 | 677 |
| [lib/domain/entity/workplace/feed/workplace_user_feed.entity.dart](/lib/domain/entity/workplace/feed/workplace_user_feed.entity.dart) | Dart | 19 | 9 | 7 | 35 |
| [lib/domain/entity/workplace/feed/workplace_user_feed.entity.g.dart](/lib/domain/entity/workplace/feed/workplace_user_feed.entity.g.dart) | Dart | 620 | 6 | 66 | 692 |
| [lib/domain/entity/workplace/group/group.dart](/lib/domain/entity/workplace/group/group.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/workplace/group/workplace_group.entity.dart](/lib/domain/entity/workplace/group/workplace_group.entity.dart) | Dart | 52 | 9 | 10 | 71 |
| [lib/domain/entity/workplace/group/workplace_group.entity.g.dart](/lib/domain/entity/workplace/group/workplace_group.entity.g.dart) | Dart | 2,575 | 11 | 245 | 2,831 |
| [lib/domain/entity/workplace/other/other.dart](/lib/domain/entity/workplace/other/other.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/workplace/other/workplace_attachment.entity.dart](/lib/domain/entity/workplace/other/workplace_attachment.entity.dart) | Dart | 66 | 11 | 24 | 101 |
| [lib/domain/entity/workplace/other/workplace_attachment.entity.g.dart](/lib/domain/entity/workplace/other/workplace_attachment.entity.g.dart) | Dart | 2,979 | 19 | 240 | 3,238 |
| [lib/domain/entity/workplace/other/workplace_comment.entity.dart](/lib/domain/entity/workplace/other/workplace_comment.entity.dart) | Dart | 62 | 9 | 16 | 87 |
| [lib/domain/entity/workplace/other/workplace_comment.entity.g.dart](/lib/domain/entity/workplace/other/workplace_comment.entity.g.dart) | Dart | 2,614 | 13 | 222 | 2,849 |
| [lib/domain/entity/workplace/thread/thread.dart](/lib/domain/entity/workplace/thread/thread.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.dart](/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.dart) | Dart | 61 | 9 | 13 | 83 |
| [lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.g.dart](/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.g.dart) | Dart | 2,909 | 13 | 247 | 3,169 |
| [lib/domain/entity/workplace/thread/workplace_conversations.entity.dart](/lib/domain/entity/workplace/thread/workplace_conversations.entity.dart) | Dart | 77 | 10 | 21 | 108 |
| [lib/domain/entity/workplace/thread/workplace_conversations.entity.g.dart](/lib/domain/entity/workplace/thread/workplace_conversations.entity.g.dart) | Dart | 2,897 | 10 | 284 | 3,191 |
| [lib/domain/entity/workplace/user/user.dart](/lib/domain/entity/workplace/user/user.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/workplace/user/workplace_community_member.entity.dart](/lib/domain/entity/workplace/user/workplace_community_member.entity.dart) | Dart | 32 | 12 | 11 | 55 |
| [lib/domain/entity/workplace/user/workplace_community_member.entity.g.dart](/lib/domain/entity/workplace/user/workplace_community_member.entity.g.dart) | Dart | 2,239 | 6 | 219 | 2,464 |
| [lib/domain/entity/workplace/workplace.dart](/lib/domain/entity/workplace/workplace.dart) | Dart | -6 | 0 | 0 | -6 |
| [lib/domain/entity/workplace/workplace_attachment.entity.dart](/lib/domain/entity/workplace/workplace_attachment.entity.dart) | Dart | -46 | -12 | -18 | -76 |
| [lib/domain/entity/workplace/workplace_attachment.entity.g.dart](/lib/domain/entity/workplace/workplace_attachment.entity.g.dart) | Dart | -2,062 | -14 | -165 | -2,241 |
| [lib/domain/entity/workplace/workplace_comment.entity.dart](/lib/domain/entity/workplace/workplace_comment.entity.dart) | Dart | -16 | -9 | -5 | -30 |
| [lib/domain/entity/workplace/workplace_comment.entity.g.dart](/lib/domain/entity/workplace/workplace_comment.entity.g.dart) | Dart | -501 | -6 | -42 | -549 |
| [lib/domain/entity/workplace/workplace_community_member.entity.dart](/lib/domain/entity/workplace/workplace_community_member.entity.dart) | Dart | -28 | -12 | -10 | -50 |
| [lib/domain/entity/workplace/workplace_community_member.entity.g.dart](/lib/domain/entity/workplace/workplace_community_member.entity.g.dart) | Dart | -1,960 | -6 | -192 | -2,158 |
| [lib/domain/entity/workplace/workplace_conversation_attachment.entity.dart](/lib/domain/entity/workplace/workplace_conversation_attachment.entity.dart) | Dart | -49 | 0 | -5 | -54 |
| [lib/domain/entity/workplace/workplace_conversation_attachment.entity.g.dart](/lib/domain/entity/workplace/workplace_conversation_attachment.entity.g.dart) | Dart | -1,930 | -10 | -149 | -2,089 |
| [lib/domain/entity/workplace/workplace_conversations.entity.dart](/lib/domain/entity/workplace/workplace_conversations.entity.dart) | Dart | -37 | 0 | -6 | -43 |
| [lib/domain/entity/workplace/workplace_conversations.entity.g.dart](/lib/domain/entity/workplace/workplace_conversations.entity.g.dart) | Dart | -1,725 | -13 | -136 | -1,874 |
| [lib/domain/entity/workplace/workplace_feed.entity.dart](/lib/domain/entity/workplace/workplace_feed.entity.dart) | Dart | -29 | -1 | -4 | -34 |
| [lib/domain/entity/workplace/workplace_feed.entity.g.dart](/lib/domain/entity/workplace/workplace_feed.entity.g.dart) | Dart | -1,331 | -6 | -100 | -1,437 |
| [lib/domain/entity/workplace/workplace_group.entity.dart](/lib/domain/entity/workplace/workplace_group.entity.dart) | Dart | -40 | -9 | -7 | -56 |
| [lib/domain/entity/workplace/workplace_group.entity.g.dart](/lib/domain/entity/workplace/workplace_group.entity.g.dart) | Dart | -2,335 | -11 | -215 | -2,561 |
| [lib/domain/entity/workplace/workplace_group_feed.entity.dart](/lib/domain/entity/workplace/workplace_group_feed.entity.dart) | Dart | -14 | -9 | -5 | -28 |
| [lib/domain/entity/workplace/workplace_group_feed.entity.g.dart](/lib/domain/entity/workplace/workplace_group_feed.entity.g.dart) | Dart | -583 | -6 | -55 | -644 |
| [lib/domain/entity/workplace/workplace_group_member.entity.dart](/lib/domain/entity/workplace/workplace_group_member.entity.dart) | Dart | -30 | -12 | -10 | -52 |
| [lib/domain/entity/workplace/workplace_group_member.entity.g.dart](/lib/domain/entity/workplace/workplace_group_member.entity.g.dart) | Dart | -2,143 | -6 | -210 | -2,359 |
| [lib/domain/entity/workplace/workplace_user.entity.dart](/lib/domain/entity/workplace/workplace_user.entity.dart) | Dart | -27 | -12 | -9 | -48 |
| [lib/domain/entity/workplace/workplace_user.entity.g.dart](/lib/domain/entity/workplace/workplace_user.entity.g.dart) | Dart | -1,376 | -6 | -112 | -1,494 |
| [lib/domain/entity/workplace/workplace_user_feed.entity.dart](/lib/domain/entity/workplace/workplace_user_feed.entity.dart) | Dart | -13 | -9 | -8 | -30 |
| [lib/domain/entity/workplace/workplace_user_feed.entity.g.dart](/lib/domain/entity/workplace/workplace_user_feed.entity.g.dart) | Dart | -602 | -6 | -57 | -665 |
| [lib/domain/repository/download_repo.dart](/lib/domain/repository/download_repo.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/repository/gapo_repo.dart](/lib/domain/repository/gapo_repo.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/repository/repository.dart](/lib/domain/repository/repository.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/domain/usecase/download_file.usecase.dart](/lib/domain/usecase/download_file.usecase.dart) | Dart | 31 | 0 | 9 | 40 |
| [lib/domain/usecase/download_then_upload.usecase.dart](/lib/domain/usecase/download_then_upload.usecase.dart) | Dart | 61 | 12 | 9 | 82 |
| [lib/domain/usecase/feed_attachment.usecase.dart](/lib/domain/usecase/feed_attachment.usecase.dart) | Dart | 70 | 10 | 11 | 91 |
| [lib/domain/usecase/gapo/gapo.dart](/lib/domain/usecase/gapo/gapo.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/domain/usecase/gapo/gapo_create_user.usecase.dart](/lib/domain/usecase/gapo/gapo_create_user.usecase.dart) | Dart | 22 | 9 | 7 | 38 |
| [lib/domain/usecase/gapo/gapo_login.usecase.dart](/lib/domain/usecase/gapo/gapo_login.usecase.dart) | Dart | 68 | 0 | 13 | 81 |
| [lib/domain/usecase/gapo/gapo_upload.usecase.dart](/lib/domain/usecase/gapo/gapo_upload.usecase.dart) | Dart | 7 | 0 | 4 | 11 |
| [lib/domain/usecase/usecase.dart](/lib/domain/usecase/usecase.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/domain/usecase/workplace_get_conversations.usecase.dart](/lib/domain/usecase/workplace_get_conversations.usecase.dart) | Dart | 6 | 9 | 1 | 16 |
| [lib/domain/usecase/workplace_get_post_comments.usecase.dart](/lib/domain/usecase/workplace_get_post_comments.usecase.dart) | Dart | 0 | 9 | 1 | 10 |
| [lib/helpers/csv.dart](/lib/helpers/csv.dart) | Dart | 32 | 0 | 5 | 37 |
| [lib/helpers/file_helper.dart](/lib/helpers/file_helper.dart) | Dart | 36 | 0 | 9 | 45 |
| [lib/helpers/gp_upload_management.dart](/lib/helpers/gp_upload_management.dart) | Dart | 143 | 18 | 41 | 202 |
| [lib/helpers/helpers.dart](/lib/helpers/helpers.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/mapper/entity/entity.dart](/lib/mapper/entity/entity.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/mapper/entity/gapo_entity_mapper.auto_mappr.dart](/lib/mapper/entity/gapo_entity_mapper.auto_mappr.dart) | Dart | 487 | 47 | 37 | 571 |
| [lib/mapper/entity/gapo_entity_mapper.dart](/lib/mapper/entity/gapo_entity_mapper.dart) | Dart | 219 | 18 | 36 | 273 |
| [lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart](/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart) | Dart | 8 | 0 | 0 | 8 |
| [lib/mapper/entity/workplace_entity_mapper.dart](/lib/mapper/entity/workplace_entity_mapper.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/mapper/gp_mapper.auto_mappr.dart](/lib/mapper/gp_mapper.auto_mappr.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/mapper/gp_mapper.dart](/lib/mapper/gp_mapper.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/route/go_router.route.dart](/lib/route/go_router.route.dart) | Dart | 13 | 0 | 2 | 15 |
| [lib/route/go_router.route.g.dart](/lib/route/go_router.route.g.dart) | Dart | 16 | 0 | 7 | 23 |
| [project_configs/flutter_launcher_icons-dev.yaml](/project_configs/flutter_launcher_icons-dev.yaml) | YAML | 17 | 0 | 0 | 17 |
| [project_configs/flutter_launcher_icons-prod.yaml](/project_configs/flutter_launcher_icons-prod.yaml) | YAML | 17 | 0 | 0 | 17 |
| [project_configs/flutter_launcher_icons-uat.yaml](/project_configs/flutter_launcher_icons-uat.yaml) | YAML | 17 | 0 | 0 | 17 |
| [project_configs/package_rename_config-dev.yaml](/project_configs/package_rename_config-dev.yaml) | YAML | 27 | 0 | 6 | 33 |
| [project_configs/package_rename_config-prod.yaml](/project_configs/package_rename_config-prod.yaml) | YAML | 27 | 0 | 6 | 33 |
| [project_configs/package_rename_config-uat.yaml](/project_configs/package_rename_config-uat.yaml) | YAML | 27 | 0 | 6 | 33 |
| [pubspec.yaml](/pubspec.yaml) | YAML | 1 | 0 | 0 | 1 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details