"filename", "language", "Dar<PERSON>", "YAM<PERSON>", "comment", "blank", "total"
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_bloc.dart", "Dart", 13, 0, 1, 2, 16
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/gapo_url.constants.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_bloc.dart", "Dart", 246, 0, -6, 26, 266
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_event.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.dart", "Dar<PERSON>", 18, 0, 0, 0, 18
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.freezed.dart", "Dart", 293, 0, 8, 34, 335
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawl.main.page.dart", "Dart", -18, 0, -2, 3, -17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/home/<USER>/bloc.dart", "Dart", 3, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/home/<USER>/home_page_bloc.dart", "Dart", 27, 0, 0, 4, 31
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/home/<USER>/home_page_event.dart", "Dart", 8, 0, 0, 3, 11
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/home/<USER>/home_page_state.dart", "Dart", 15, 0, 0, 3, 18
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/home/<USER>/home_page_state.freezed.dart", "Dart", 96, 0, 15, 23, 134
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/home/<USER>", "Dart", 2, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/home/<USER>", "Dart", 98, 0, 2, 5, 105
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/test_attachment.page.dart", "Dart", 55, 0, 1, -13, 43
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/download/download.dart", "Dart", 1, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/download/download.service.dart", "Dart", 32, 0, 0, 4, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/gapo/auth.service.dart", "Dart", 4, 0, 0, 1, 5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/gapo/auth.service.g.dart", "Dart", 31, 0, 0, 1, 32
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/gapo/upload.service.g.dart", "Dart", -2, 0, 0, 0, -2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/remote.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.dart", "Dart", 9, 0, 0, 1, 10
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.g.dart", "Dart", 34, 0, 0, 0, 34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/download/download.dart", "Dart", 1, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/download/download_params.dart", "Dart", 13, 0, 0, 2, 15
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/download/download_params.freezed.dart", "Dart", 118, 0, 15, 23, 156
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_signup_params.dart", "Dart", 21, 0, 1, 4, 26
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_signup_params.freezed.dart", "Dart", 205, 0, 15, 23, 243
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_signup_params.g.dart", "Dart", 10, 0, 4, 4, 18
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/request.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/upload_file_response.dart", "Dart", 6, 0, 0, 1, 7
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/model.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.dart", "Dart", -1, 0, -2, -1, -4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.g.dart", "Dart", -1371, 0, -5, -110, -1486
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/comment/comment.dart", "Dart", 2, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/comment/message_tags.dart", "Dart", 21, 0, 0, 5, 26
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/comment/message_tags.g.dart", "Dart", 27, 0, 4, 6, 37
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/comment/workplace_reactions.dart", "Dart", 19, 0, 0, 7, 26
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/comment/workplace_reactions.g.dart", "Dart", 24, 0, 4, 6, 34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart", "Dart", 0, 0, 0, 4, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversations_response.dart", "Dart", 5, 0, 0, 7, 12
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversations_response.g.dart", "Dart", 8, 0, 0, 1, 9
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/enums/workplace_enums.dart", "Dart", 30, 0, 0, 12, 42
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.dart", "Dart", -1, 0, 0, 4, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart", "Dart", 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.dart", "Dart", 10, 0, 0, 2, 12
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.g.dart", "Dart", 11, 0, 0, 0, 11
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/workplace.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/download_impl.dart", "Dart", 17, 0, 0, 3, 20
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/gapo_impl.dart", "Dart", 19, 0, 0, 2, 21
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/repository.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/component/app.component.config.dart", "Dart", 32, 0, 0, 0, 32
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/client.module.dart", "Dart", 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/database.module.dart", "Dart", -2, 0, 0, 0, -2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/base_crawl.entity.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/status/base_crawl_status.entity.dart", "Dart", 3, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/status/base_crawl_status.entity.g.dart", "Dart", 51, 0, 0, 3, 54
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/enums.dart", "Dart", 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/gp_enums.dart", "Dart", 91, 0, 0, 12, 103
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/upload/gp_upload_status_enum.dart", "Dart", 12, 0, 0, 5, 17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/upload/upload.dart", "Dart", 1, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gapo.dart", "Dart", 7, 0, 0, 0, 7
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_attachement.dart", "Dart", 0, 0, 0, 1, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_comment.dart", "Dart", 70, 0, 0, 11, 81
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_comment.g.dart", "Dart", 85, 0, 4, 15, 104
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_group.dart", "Dart", 29, 0, 0, 4, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_group.g.dart", "Dart", 39, 0, 4, 7, 50
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_message.dart", "Dart", 23, 0, 0, 5, 28
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_message.g.dart", "Dart", 23, 0, 4, 6, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_post.dart", "Dart", 121, 0, 0, 10, 131
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_post.g.dart", "Dart", 140, 0, 4, 16, 160
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_thread.dart", "Dart", 21, 0, 0, 5, 26
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_thread.g.dart", "Dart", 21, 0, 4, 6, 31
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.dart", "Dart", -4, 0, 0, -1, -5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.g.dart", "Dart", -1331, 0, -5, -142, -1478
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/callback/callback.dart", "Dart", 2, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/callback/gp_upload_callback.dart", "Dart", 46, 0, 8, 13, 67
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart", "Dart", 17, 0, 12, 5, 34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.freezed.dart", "Dart", 123, 0, 15, 23, 161
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.dart", "Dart", 13, 0, 14, 7, 34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.freezed.dart", "Dart", 183, 0, 27, 37, 247
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/params/gp_upload_params.dart", "Dart", 25, 0, 2, 6, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/params/gp_upload_repo_params.dart", "Dart", 16, 0, 0, 4, 20
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/params/params.dart", "Dart", 2, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/upload/upload.dart", "Dart", 3, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/feed/feed.dart", "Dart", 3, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_base_feed.entity.dart", "Dart", 44, 0, 10, 14, 68
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_base_feed.entity.g.dart", "Dart", 1822, 0, 6, 175, 2003
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_group_feed.entity.dart", "Dart", 18, 0, 9, 6, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_group_feed.entity.g.dart", "Dart", 608, 0, 6, 63, 677
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_user_feed.entity.dart", "Dart", 19, 0, 9, 7, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_user_feed.entity.g.dart", "Dart", 620, 0, 6, 66, 692
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/group/group.dart", "Dart", 1, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/group/workplace_group.entity.dart", "Dart", 52, 0, 9, 10, 71
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/group/workplace_group.entity.g.dart", "Dart", 2575, 0, 11, 245, 2831
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/other/other.dart", "Dart", 2, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/other/workplace_attachment.entity.dart", "Dart", 66, 0, 11, 24, 101
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/other/workplace_attachment.entity.g.dart", "Dart", 2979, 0, 19, 240, 3238
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/other/workplace_comment.entity.dart", "Dart", 62, 0, 9, 16, 87
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/other/workplace_comment.entity.g.dart", "Dart", 2614, 0, 13, 222, 2849
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/thread/thread.dart", "Dart", 2, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.dart", "Dart", 61, 0, 9, 13, 83
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.g.dart", "Dart", 2909, 0, 13, 247, 3169
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/thread/workplace_conversations.entity.dart", "Dart", 77, 0, 10, 21, 108
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/thread/workplace_conversations.entity.g.dart", "Dart", 2897, 0, 10, 284, 3191
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/user/user.dart", "Dart", 1, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/user/workplace_community_member.entity.dart", "Dart", 32, 0, 12, 11, 55
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/user/workplace_community_member.entity.g.dart", "Dart", 2239, 0, 6, 219, 2464
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace.dart", "Dart", -6, 0, 0, 0, -6
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_attachment.entity.dart", "Dart", -46, 0, -12, -18, -76
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_attachment.entity.g.dart", "Dart", -2062, 0, -14, -165, -2241
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_comment.entity.dart", "Dart", -16, 0, -9, -5, -30
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_comment.entity.g.dart", "Dart", -501, 0, -6, -42, -549
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_community_member.entity.dart", "Dart", -28, 0, -12, -10, -50
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_community_member.entity.g.dart", "Dart", -1960, 0, -6, -192, -2158
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_conversation_attachment.entity.dart", "Dart", -49, 0, 0, -5, -54
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_conversation_attachment.entity.g.dart", "Dart", -1930, 0, -10, -149, -2089
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_conversations.entity.dart", "Dart", -37, 0, 0, -6, -43
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_conversations.entity.g.dart", "Dart", -1725, 0, -13, -136, -1874
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_feed.entity.dart", "Dart", -29, 0, -1, -4, -34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_feed.entity.g.dart", "Dart", -1331, 0, -6, -100, -1437
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group.entity.dart", "Dart", -40, 0, -9, -7, -56
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group.entity.g.dart", "Dart", -2335, 0, -11, -215, -2561
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.dart", "Dart", -14, 0, -9, -5, -28
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.g.dart", "Dart", -583, 0, -6, -55, -644
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_member.entity.dart", "Dart", -30, 0, -12, -10, -52
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_member.entity.g.dart", "Dart", -2143, 0, -6, -210, -2359
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user.entity.dart", "Dart", -27, 0, -12, -9, -48
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user.entity.g.dart", "Dart", -1376, 0, -6, -112, -1494
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user_feed.entity.dart", "Dart", -13, 0, -9, -8, -30
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user_feed.entity.g.dart", "Dart", -602, 0, -6, -57, -665
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/download_repo.dart", "Dart", 4, 0, 0, 1, 5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/gapo_repo.dart", "Dart", 3, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/repository.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/download_file.usecase.dart", "Dart", 31, 0, 0, 9, 40
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/download_then_upload.usecase.dart", "Dart", 61, 0, 12, 9, 82
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/feed_attachment.usecase.dart", "Dart", 70, 0, 10, 11, 91
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/gapo/gapo.dart", "Dart", 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_create_user.usecase.dart", "Dart", 22, 0, 9, 7, 38
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_login.usecase.dart", "Dart", 68, 0, 0, 13, 81
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_upload.usecase.dart", "Dart", 7, 0, 0, 4, 11
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/usecase.dart", "Dart", 3, 0, 0, 0, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_conversations.usecase.dart", "Dart", 6, 0, 9, 1, 16
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_post_comments.usecase.dart", "Dart", 0, 0, 9, 1, 10
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/helpers/csv.dart", "Dart", 32, 0, 0, 5, 37
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/helpers/file_helper.dart", "Dart", 36, 0, 0, 9, 45
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/helpers/gp_upload_management.dart", "Dart", 143, 0, 18, 41, 202
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/helpers/helpers.dart", "Dart", 3, 0, 0, 0, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/entity.dart", "Dart", 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/gapo_entity_mapper.auto_mappr.dart", "Dart", 487, 0, 47, 37, 571
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/gapo_entity_mapper.dart", "Dart", 219, 0, 18, 36, 273
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart", "Dart", 8, 0, 0, 0, 8
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.dart", "Dart", 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/gp_mapper.auto_mappr.dart", "Dart", 3, 0, 0, 0, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/gp_mapper.dart", "Dart", 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/route/go_router.route.dart", "Dart", 13, 0, 0, 2, 15
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/route/go_router.route.g.dart", "Dart", 16, 0, 0, 7, 23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/project_configs/flutter_launcher_icons-dev.yaml", "YAML", 0, 17, 0, 0, 17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/project_configs/flutter_launcher_icons-prod.yaml", "YAML", 0, 17, 0, 0, 17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/project_configs/flutter_launcher_icons-uat.yaml", "YAML", 0, 17, 0, 0, 17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/project_configs/package_rename_config-dev.yaml", "YAML", 0, 27, 0, 6, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/project_configs/package_rename_config-prod.yaml", "YAML", 0, 27, 0, 6, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/project_configs/package_rename_config-uat.yaml", "YAML", 0, 27, 0, 6, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/pubspec.yaml", "YAML", 0, 1, 0, 0, 1
"Total", "-", 4098, 133, 283, 750, 5264