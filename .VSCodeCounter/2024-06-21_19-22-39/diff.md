# Diff Summary

Date : 2024-06-21 19:22:39

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 164 files,  4231 codes, 283 comments, 750 blanks, all 5264 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 157 | 4,098 | 283 | 732 | 5,113 |
| YAML | 7 | 133 | 0 | 18 | 151 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 164 | 4,231 | 283 | 750 | 5,264 |
| . (Files) | 1 | 1 | 0 | 0 | 1 |
| lib | 157 | 4,098 | 283 | 732 | 5,113 |
| lib/app | 15 | 858 | 19 | 92 | 969 |
| lib/app (Files) | 1 | 55 | 1 | -13 | 43 |
| lib/app/app_config | 1 | 13 | 1 | 2 | 16 |
| lib/app/app_config/bloc | 1 | 13 | 1 | 2 | 16 |
| lib/app/constant | 1 | 1 | 0 | 0 | 1 |
| lib/app/features | 12 | 789 | 17 | 103 | 909 |
| lib/app/features/crawler | 5 | 540 | 0 | 63 | 603 |
| lib/app/features/crawler (Files) | 1 | -18 | -2 | 3 | -17 |
| lib/app/features/crawler/bloc | 4 | 558 | 2 | 60 | 620 |
| lib/app/features/home | 7 | 249 | 17 | 40 | 306 |
| lib/app/features/home (Files) | 2 | 100 | 2 | 6 | 108 |
| lib/app/features/home/<USER>
| lib/data | 36 | -690 | 36 | 15 | -639 |
| lib/data/data_source | 8 | 110 | 0 | 8 | 118 |
| lib/data/data_source/remote | 8 | 110 | 0 | 8 | 118 |
| lib/data/data_source/remote (Files) | 3 | 44 | 0 | 1 | 45 |
| lib/data/data_source/remote/download | 2 | 33 | 0 | 5 | 38 |
| lib/data/data_source/remote/gapo | 3 | 33 | 0 | 2 | 35 |
| lib/data/model | 25 | -837 | 36 | 2 | -799 |
| lib/data/model (Files) | 1 | 1 | 0 | 0 | 1 |
| lib/data/model/download | 3 | 132 | 15 | 26 | 173 |
| lib/data/model/gpw | 5 | 243 | 20 | 32 | 295 |
| lib/data/model/gpw (Files) | 1 | 6 | 0 | 1 | 7 |
| lib/data/model/gpw/auth | 4 | 237 | 20 | 31 | 288 |
| lib/data/model/gpw/auth/request | 4 | 237 | 20 | 31 | 288 |
| lib/data/model/workplace | 16 | -1,213 | 1 | -56 | -1,268 |
| lib/data/model/workplace (Files) | 1 | 1 | 0 | 0 | 1 |
| lib/data/model/workplace/base | 2 | -1,372 | -7 | -111 | -1,490 |
| lib/data/model/workplace/comment | 5 | 93 | 8 | 25 | 126 |
| lib/data/model/workplace/conversation | 3 | 13 | 0 | 12 | 25 |
| lib/data/model/workplace/enums | 1 | 30 | 0 | 12 | 42 |
| lib/data/model/workplace/group | 1 | -1 | 0 | 4 | 3 |
| lib/data/model/workplace/post | 3 | 23 | 0 | 2 | 25 |
| lib/data/repository | 3 | 37 | 0 | 5 | 42 |
| lib/di | 3 | 32 | 0 | 0 | 32 |
| lib/di/component | 1 | 32 | 0 | 0 | 32 |
| lib/di/modules | 2 | 0 | 0 | 0 | 0 |
| lib/domain | 90 | 2,933 | 145 | 488 | 3,566 |
| lib/domain/entity | 77 | 2,655 | 96 | 431 | 3,182 |
| lib/domain/entity/base | 3 | 55 | 0 | 4 | 59 |
| lib/domain/entity/base (Files) | 1 | 1 | 0 | 0 | 1 |
| lib/domain/entity/base/status | 2 | 54 | 0 | 4 | 58 |
| lib/domain/entity/enums | 4 | 106 | 0 | 18 | 124 |
| lib/domain/entity/enums (Files) | 2 | 93 | 0 | 12 | 105 |
| lib/domain/entity/enums/upload | 2 | 13 | 0 | 6 | 19 |
| lib/domain/entity/gapo | 24 | -326 | 93 | 41 | -192 |
| lib/domain/entity/gapo (Files) | 14 | -756 | 15 | -57 | -798 |
| lib/domain/entity/gapo/upload | 10 | 430 | 78 | 98 | 606 |
| lib/domain/entity/gapo/upload (Files) | 3 | 199 | 41 | 45 | 285 |
| lib/domain/entity/gapo/upload/callback | 4 | 188 | 35 | 42 | 265 |
| lib/domain/entity/gapo/upload/params | 3 | 43 | 2 | 11 | 56 |
| lib/domain/entity/workplace | 46 | 2,820 | 3 | 368 | 3,191 |
| lib/domain/entity/workplace (Files) | 23 | -16,883 | -175 | -1,520 | -18,578 |
| lib/domain/entity/workplace/feed | 7 | 3,134 | 46 | 332 | 3,512 |
| lib/domain/entity/workplace/group | 3 | 2,628 | 20 | 256 | 2,904 |
| lib/domain/entity/workplace/other | 5 | 5,723 | 52 | 503 | 6,278 |
| lib/domain/entity/workplace/thread | 5 | 5,946 | 42 | 566 | 6,554 |
| lib/domain/entity/workplace/user | 3 | 2,272 | 18 | 231 | 2,521 |
| lib/domain/repository | 3 | 8 | 0 | 2 | 10 |
| lib/domain/usecase | 10 | 270 | 49 | 55 | 374 |
| lib/domain/usecase (Files) | 6 | 171 | 40 | 31 | 242 |
| lib/domain/usecase/gapo | 4 | 99 | 9 | 24 | 132 |
| lib/helpers | 4 | 214 | 18 | 55 | 287 |
| lib/mapper | 7 | 722 | 65 | 73 | 860 |
| lib/mapper (Files) | 2 | 4 | 0 | 0 | 4 |
| lib/mapper/entity | 5 | 718 | 65 | 73 | 856 |
| lib/route | 2 | 29 | 0 | 9 | 38 |
| project_configs | 6 | 132 | 0 | 18 | 150 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)