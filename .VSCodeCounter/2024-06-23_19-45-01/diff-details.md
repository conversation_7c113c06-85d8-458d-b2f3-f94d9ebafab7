# Diff Details

Date : 2024-06-23 19:45:01

Directory c:\\Softwares\\Gapo\\gapoflutter-crawler

Total : 657 files,  -1935 codes, 1163 comments, 212 blanks, all -560 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\README.md](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5CREADME.md) | Markdown | -40 | 0 | -12 | -52 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\analysis_options.yaml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Canalysis_options.yaml) | YAML | -7 | -18 | -3 | -28 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\app\build.gradle](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Capp%5Cbuild.gradle) | Groovy | -51 | -5 | -12 | -68 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\app\src\debug\AndroidManifest.xml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Capp%5Csrc%5Cdebug%5CAndroidManifest.xml) | XML | -7 | -4 | 0 | -11 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\app\src\main\AndroidManifest.xml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Capp%5Csrc%5Cmain%5CAndroidManifest.xml) | XML | -32 | -6 | 0 | -38 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\app\src\main\kotlin\vn\gapowork\crawler\MainActivity.kt](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Capp%5Csrc%5Cmain%5Ckotlin%5Cvn%5Cgapowork%5Ccrawler%5CMainActivity.kt) | Kotlin | -4 | 0 | -3 | -7 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\app\src\main\res\drawable-v21\launch_background.xml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Capp%5Csrc%5Cmain%5Cres%5Cdrawable-v21%5Claunch_background.xml) | XML | -4 | -7 | -2 | -13 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\app\src\main\res\drawable\launch_background.xml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Capp%5Csrc%5Cmain%5Cres%5Cdrawable%5Claunch_background.xml) | XML | -4 | -7 | -2 | -13 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\app\src\main\res\values-night\styles.xml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Capp%5Csrc%5Cmain%5Cres%5Cvalues-night%5Cstyles.xml) | XML | -9 | -9 | -1 | -19 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\app\src\main\res\values\styles.xml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Capp%5Csrc%5Cmain%5Cres%5Cvalues%5Cstyles.xml) | XML | -9 | -9 | -1 | -19 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\build.gradle](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Cbuild.gradle) | Groovy | -27 | 0 | -5 | -32 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\gradle.properties](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Cgradle.properties) | Java Properties | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\gradle\wrapper\gradle-wrapper.properties](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Cgradle%5Cwrapper%5Cgradle-wrapper.properties) | Java Properties | -5 | 0 | -1 | -6 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\android\settings.gradle](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Candroid%5Csettings.gradle) | Groovy | -16 | 0 | -5 | -21 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\assets\images\splash_loading_1.json](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cassets%5Cimages%5Csplash_loading_1.json) | JSON | -1 | 0 | 0 | -1 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\assets\images\splash_loading_2.json](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cassets%5Cimages%5Csplash_loading_2.json) | JSON | -1 | 0 | 0 | -1 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\build.yaml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cbuild.yaml) | YAML | -7 | 0 | 0 | -7 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\RunnerTests\RunnerTests.swift](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunnerTests%5CRunnerTests.swift) | Swift | -7 | -2 | -4 | -13 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\AppDelegate.swift](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CAppDelegate.swift) | Swift | -12 | 0 | -2 | -14 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\Assets.xcassets\AppIcon-dev.appiconset\Contents.json](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CAssets.xcassets%5CAppIcon-dev.appiconset%5CContents.json) | JSON | -1 | 0 | 0 | -1 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\Assets.xcassets\AppIcon-prod.appiconset\Contents.json](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CAssets.xcassets%5CAppIcon-prod.appiconset%5CContents.json) | JSON | -1 | 0 | 0 | -1 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\Assets.xcassets\AppIcon-uat.appiconset\Contents.json](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CAssets.xcassets%5CAppIcon-uat.appiconset%5CContents.json) | JSON | -1 | 0 | 0 | -1 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\Assets.xcassets\AppIcon.appiconset\Contents.json](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CAssets.xcassets%5CAppIcon.appiconset%5CContents.json) | JSON | -122 | 0 | -1 | -123 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\Assets.xcassets\LaunchImage.imageset\Contents.json](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CAssets.xcassets%5CLaunchImage.imageset%5CContents.json) | JSON | -23 | 0 | -1 | -24 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\Assets.xcassets\LaunchImage.imageset\README.md](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CAssets.xcassets%5CLaunchImage.imageset%5CREADME.md) | Markdown | -3 | 0 | -2 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\Base.lproj\LaunchScreen.storyboard](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CBase.lproj%5CLaunchScreen.storyboard) | XML | -36 | -1 | -1 | -38 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\Base.lproj\Main.storyboard](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CBase.lproj%5CMain.storyboard) | XML | -25 | -1 | -1 | -27 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\ios\Runner\Runner-Bridging-Header.h](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cios%5CRunner%5CRunner-Bridging-Header.h) | C++ | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\app.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Capp.dart) | Dart | -8 | 0 | -1 | -9 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\app_config\app_config.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Capp_config%5Capp_config.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\app_config\bloc\app_config_bloc.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Capp_config%5Cbloc%5Capp_config_bloc.dart) | Dart | -86 | -1 | -14 | -101 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\app_config\bloc\app_config_event.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Capp_config%5Cbloc%5Capp_config_event.dart) | Dart | -12 | 0 | -4 | -16 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\app_config\bloc\app_config_state.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Capp_config%5Cbloc%5Capp_config_state.dart) | Dart | -17 | -1 | -5 | -23 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\app_config\bloc\app_config_state.freezed.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Capp_config%5Cbloc%5Capp_config_state.freezed.dart) | Dart | -118 | -15 | -23 | -156 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\app_config\bloc\bloc.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Capp_config%5Cbloc%5Cbloc.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\app_config\widgets\app_config_body_page.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Capp_config%5Cwidgets%5Capp_config_body_page.dart) | Dart | -8 | 0 | -3 | -11 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\app_config\widgets\widgets.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Capp_config%5Cwidgets%5Cwidgets.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\base\base.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cbase%5Cbase.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\base\networking\gapo\authentication_interceptor.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cbase%5Cnetworking%5Cgapo%5Cauthentication_interceptor.dart) | Dart | -33 | 0 | -11 | -44 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\base\networking\gapo\gapo.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cbase%5Cnetworking%5Cgapo%5Cgapo.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\base\networking\gapo\gp_token_interceptor.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cbase%5Cnetworking%5Cgapo%5Cgp_token_interceptor.dart) | Dart | -113 | -39 | -33 | -185 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\base\networking\logger_inteceptor.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cbase%5Cnetworking%5Clogger_inteceptor.dart) | Dart | -75 | -7 | -18 | -100 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\base\networking\networking.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cbase%5Cnetworking%5Cnetworking.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\base\networking\workplace_auth_inteceptor.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cbase%5Cnetworking%5Cworkplace_auth_inteceptor.dart) | Dart | -29 | 0 | -8 | -37 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\constant\app_constant.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cconstant%5Capp_constant.dart) | Dart | -7 | 0 | -2 | -9 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\constant\constant.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cconstant%5Cconstant.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\constant\fb_wp_url.constants.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cconstant%5Cfb_wp_url.constants.dart) | Dart | -16 | -11 | -5 | -32 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\constant\gapo_url.constants.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cconstant%5Cgapo_url.constants.dart) | Dart | -14 | -13 | -5 | -32 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\bloc\bloc.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Cbloc%5Cbloc.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_bloc.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Cbloc%5Ccrawl_bloc.dart) | Dart | -521 | -14 | -69 | -604 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_event.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Cbloc%5Ccrawl_event.dart) | Dart | -12 | 0 | -4 | -16 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_state.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Cbloc%5Ccrawl_state.dart) | Dart | -48 | -1 | -6 | -55 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_state.freezed.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Cbloc%5Ccrawl_state.freezed.dart) | Dart | -549 | -31 | -78 | -658 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\crawl.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Ccrawl.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\crawl.main.page.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Ccrawl.main.page.dart) | Dart | -87 | 0 | -8 | -95 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\crawler.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Ccrawler.dart) | Dart | -5 | 0 | -1 | -6 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\sync\sync.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Csync%5Csync.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\sync\sync.page.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Csync%5Csync.page.dart) | Dart | 0 | 0 | -2 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\unsync\unsync.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Cunsync%5Cunsync.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\crawler\unsync\unsync.page.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Ccrawler%5Cunsync%5Cunsync.page.dart) | Dart | 0 | 0 | -2 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\features.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Cfeatures.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\home\bloc\bloc.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Chome%5Cbloc%5Cbloc.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\home\bloc\home_page_bloc.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Chome%5Cbloc%5Chome_page_bloc.dart) | Dart | -27 | 0 | -4 | -31 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\home\bloc\home_page_event.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Chome%5Cbloc%5Chome_page_event.dart) | Dart | -8 | 0 | -3 | -11 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\home\bloc\home_page_state.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Chome%5Cbloc%5Chome_page_state.dart) | Dart | -15 | 0 | -3 | -18 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\home\bloc\home_page_state.freezed.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Chome%5Cbloc%5Chome_page_state.freezed.dart) | Dart | -96 | -15 | -23 | -134 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\home\home.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Chome%5Chome.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\features\home\home_page.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cfeatures%5Chome%5Chome_page.dart) | Dart | -98 | -2 | -5 | -105 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\main.app.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Cmain.app.dart) | Dart | -55 | 0 | -4 | -59 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\splash\splash.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Csplash%5Csplash.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\splash\splash.page.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Csplash%5Csplash.page.dart) | Dart | -28 | 0 | -3 | -31 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\test_async.page.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Ctest_async.page.dart) | Dart | -118 | -5 | -26 | -149 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\app\test_attachment.page.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Capp%5Ctest_attachment.page.dart) | Dart | -165 | -1 | -24 | -190 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\config\app_configs.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cconfig%5Capp_configs.dart) | Dart | -29 | 0 | -11 | -40 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\config\bootstrap.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cconfig%5Cbootstrap.dart) | Dart | -18 | 0 | -5 | -23 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\config\config.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cconfig%5Cconfig.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\data_source.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cdata_source.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\local\local.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Clocal%5Clocal.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\local\workplace_local.service.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Clocal%5Cworkplace_local.service.dart) | Dart | -45 | -9 | -13 | -67 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\download\download.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cdownload%5Cdownload.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\download\download.service.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cdownload%5Cdownload.service.dart) | Dart | -32 | 0 | -4 | -36 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\facebook.service.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cfacebook.service.dart) | Dart | -20 | -10 | -6 | -36 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\facebook.service.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cfacebook.service.g.dart) | Dart | -60 | -5 | -13 | -78 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\gapo\auth.service.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cgapo%5Cauth.service.dart) | Dart | -29 | -9 | -7 | -45 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\gapo\auth.service.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cgapo%5Cauth.service.g.dart) | Dart | -127 | -5 | -15 | -147 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\gapo\gapo.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cgapo%5Cgapo.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\gapo\upload.service.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cgapo%5Cupload.service.dart) | Dart | -43 | -16 | -9 | -68 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\gapo\upload.service.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cgapo%5Cupload.service.g.dart) | Dart | -162 | -5 | -15 | -182 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\remote.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cremote.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\workplace.service.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cworkplace.service.dart) | Dart | -67 | -10 | -14 | -91 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\data_source\remote\workplace.service.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cdata_source%5Cremote%5Cworkplace.service.g.dart) | Dart | -315 | -5 | -20 | -340 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\datetime_converter.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cdatetime_converter.dart) | Dart | -11 | 0 | -3 | -14 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\download\download.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cdownload%5Cdownload.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\download\download_params.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cdownload%5Cdownload_params.dart) | Dart | -13 | 0 | -2 | -15 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\download\download_params.freezed.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cdownload%5Cdownload_params.freezed.dart) | Dart | -118 | -15 | -23 | -156 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\facebook\community_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cfacebook%5Ccommunity_response.dart) | Dart | -16 | 0 | -5 | -21 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\facebook\community_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cfacebook%5Ccommunity_response.g.dart) | Dart | -8 | -4 | -4 | -16 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\facebook\facebook.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cfacebook%5Cfacebook.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\auth.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Cauth.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\request\auth_check_email_request.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Crequest%5Cauth_check_email_request.dart) | Dart | -16 | -9 | -8 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\request\auth_check_email_request.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Crequest%5Cauth_check_email_request.g.dart) | Dart | -13 | -4 | -5 | -22 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_auth_params.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Crequest%5Cgp_auth_params.dart) | Dart | -18 | -1 | -4 | -23 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_auth_params.freezed.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Crequest%5Cgp_auth_params.freezed.dart) | Dart | -226 | -15 | -23 | -264 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_auth_params.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Crequest%5Cgp_auth_params.g.dart) | Dart | -10 | -4 | -4 | -18 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_signup_params.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Crequest%5Cgp_signup_params.dart) | Dart | -21 | -1 | -4 | -26 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_signup_params.freezed.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Crequest%5Cgp_signup_params.freezed.dart) | Dart | -205 | -15 | -23 | -243 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_signup_params.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Crequest%5Cgp_signup_params.g.dart) | Dart | -10 | -4 | -4 | -18 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\request\request.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Crequest%5Crequest.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\response\auth_check_mail_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Cresponse%5Cauth_check_mail_response.dart) | Dart | -18 | -9 | -8 | -35 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\response\auth_check_mail_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Cresponse%5Cauth_check_mail_response.g.dart) | Dart | -15 | -4 | -5 | -24 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth\response\response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth%5Cresponse%5Cresponse.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth_reponse.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth_reponse.dart) | Dart | -17 | 0 | -8 | -25 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\auth_reponse.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cauth_reponse.g.dart) | Dart | -6 | -4 | -4 | -14 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\gpw.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cgpw.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\gpw\upload_file_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cgpw%5Cupload_file_response.dart) | Dart | -18 | 0 | -4 | -22 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\model.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cmodel.dart) | Dart | -5 | 0 | -1 | -6 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\base\base.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cbase%5Cbase.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\base\workplace_generic_data_converter.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cbase%5Cworkplace_generic_data_converter.dart) | Dart | -44 | 0 | -7 | -51 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\base\workplace_list_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cbase%5Cworkplace_list_response.dart) | Dart | -16 | 0 | -6 | -22 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\base\workplace_list_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cbase%5Cworkplace_list_response.g.dart) | Dart | -12 | -4 | -4 | -20 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\base\workplace_paging_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cbase%5Cworkplace_paging_response.dart) | Dart | -26 | 0 | -7 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\base\workplace_paging_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cbase%5Cworkplace_paging_response.g.dart) | Dart | -17 | -4 | -5 | -26 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\base\workplace_user.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cbase%5Cworkplace_user.dart) | Dart | -30 | -3 | -10 | -43 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\base\workplace_user.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cbase%5Cworkplace_user.g.dart) | Dart | -27 | -4 | -5 | -36 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\comment\comment.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Ccomment%5Ccomment.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\comment\message_tags.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Ccomment%5Cmessage_tags.dart) | Dart | -21 | 0 | -5 | -26 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\comment\message_tags.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Ccomment%5Cmessage_tags.g.dart) | Dart | -27 | -4 | -6 | -37 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\comment\workplace_reactions.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Ccomment%5Cworkplace_reactions.dart) | Dart | -19 | 0 | -7 | -26 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\comment\workplace_reactions.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Ccomment%5Cworkplace_reactions.g.dart) | Dart | -24 | -4 | -6 | -34 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\conversation\conversation.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cconversation%5Cconversation.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\conversation\workplace_conversation_attachment_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cconversation%5Cworkplace_conversation_attachment_response.dart) | Dart | -56 | 0 | -12 | -68 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\conversation\workplace_conversation_attachment_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cconversation%5Cworkplace_conversation_attachment_response.g.dart) | Dart | -34 | -4 | -6 | -44 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\conversation\workplace_conversations_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cconversation%5Cworkplace_conversations_response.dart) | Dart | -40 | 0 | -13 | -53 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\conversation\workplace_conversations_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cconversation%5Cworkplace_conversations_response.g.dart) | Dart | -35 | -4 | -6 | -45 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\enums\enums.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cenums%5Cenums.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\enums\workplace_enums.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cenums%5Cworkplace_enums.dart) | Dart | -48 | 0 | -16 | -64 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\group\group.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cgroup%5Cgroup.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\group\workplace_feeds_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cgroup%5Cworkplace_feeds_response.dart) | Dart | -29 | -2 | -7 | -38 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\group\workplace_feeds_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cgroup%5Cworkplace_feeds_response.g.dart) | Dart | -37 | -4 | -6 | -47 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\group\workplace_group_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cgroup%5Cworkplace_group_response.dart) | Dart | -46 | 0 | -11 | -57 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\group\workplace_group_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cgroup%5Cworkplace_group_response.g.dart) | Dart | -41 | -4 | -6 | -51 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\post\post.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cpost%5Cpost.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\post\workplace_post_attachments_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cpost%5Cworkplace_post_attachments_response.dart) | Dart | -58 | 0 | -12 | -70 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\post\workplace_post_attachments_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cpost%5Cworkplace_post_attachments_response.g.dart) | Dart | -56 | -4 | -9 | -69 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\post\workplace_post_comments_response.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cpost%5Cworkplace_post_comments_response.dart) | Dart | -29 | 0 | -7 | -36 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\post\workplace_post_comments_response.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cpost%5Cworkplace_post_comments_response.g.dart) | Dart | -28 | -4 | -5 | -37 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\model\workplace\workplace.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Cmodel%5Cworkplace%5Cworkplace.dart) | Dart | -6 | 0 | -1 | -7 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\repository\download_impl.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Crepository%5Cdownload_impl.dart) | Dart | -17 | 0 | -3 | -20 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\repository\facebook_repo_impl.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Crepository%5Cfacebook_repo_impl.dart) | Dart | -16 | 0 | -4 | -20 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\repository\gapo_impl.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Crepository%5Cgapo_impl.dart) | Dart | -75 | -10 | -11 | -96 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\repository\repository.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Crepository%5Crepository.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\data\repository\workplace_repo_impl.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdata%5Crepository%5Cworkplace_repo_impl.dart) | Dart | -61 | -10 | -14 | -85 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\component\app.component.config.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Ccomponent%5Capp.component.config.dart) | Dart | -334 | -8 | -12 | -354 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\component\app.component.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Ccomponent%5Capp.component.dart) | Dart | -18 | -9 | -5 | -32 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\component\component.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Ccomponent%5Ccomponent.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\di.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Cdi.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\modules\app.module.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Cmodules%5Capp.module.dart) | Dart | -26 | -9 | -6 | -41 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\modules\auth.module.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Cmodules%5Cauth.module.dart) | Dart | -17 | -11 | -6 | -34 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\modules\client.module.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Cmodules%5Cclient.module.dart) | Dart | -44 | -9 | -12 | -65 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\modules\database.module.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Cmodules%5Cdatabase.module.dart) | Dart | -25 | -10 | -5 | -40 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\modules\modules.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Cmodules%5Cmodules.dart) | Dart | -6 | 0 | -1 | -7 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\modules\navigator.module.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Cmodules%5Cnavigator.module.dart) | Dart | -17 | -10 | -6 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\di\modules\url.module.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdi%5Cmodules%5Curl.module.dart) | Dart | -26 | -12 | -8 | -46 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\domain.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cdomain.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\app\app.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Capp%5Capp.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\app\app_config.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Capp%5Capp_config.entity.dart) | Dart | -29 | -9 | -9 | -47 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\app\app_config.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Capp%5Capp_config.entity.g.dart) | Dart | -701 | -6 | -73 | -780 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\app\locale_enum.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Capp%5Clocale_enum.dart) | Dart | -9 | 0 | -4 | -13 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\base.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Cbase.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\base_crawl.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Cbase_crawl.entity.dart) | Dart | -96 | -10 | -17 | -123 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\log\base_crawl_log.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Clog%5Cbase_crawl_log.entity.dart) | Dart | -22 | -9 | -9 | -40 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\log\base_crawl_log.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Clog%5Cbase_crawl_log.entity.g.dart) | Dart | -1,012 | -6 | -87 | -1,105 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\log\log.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Clog%5Clog.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\status\base_crawl_status.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Cstatus%5Cbase_crawl_status.entity.dart) | Dart | -60 | -12 | -13 | -85 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\status\base_crawl_status.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Cstatus%5Cbase_crawl_status.entity.g.dart) | Dart | -946 | -8 | -78 | -1,032 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\base\status\status.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cbase%5Cstatus%5Cstatus.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Centity.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\enums\base_crawl_status_enum.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cenums%5Cbase_crawl_status_enum.dart) | Dart | -14 | -30 | -11 | -55 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\enums\base_crawl_type.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cenums%5Cbase_crawl_type.dart) | Dart | -10 | -17 | -9 | -36 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\enums\enums.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cenums%5Cenums.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\enums\gp_enums.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cenums%5Cgp_enums.dart) | Dart | -91 | 0 | -12 | -103 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\enums\upload\gp_upload_status_enum.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cenums%5Cupload%5Cgp_upload_status_enum.dart) | Dart | -12 | 0 | -5 | -17 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\enums\upload\upload.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cenums%5Cupload%5Cupload.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gapo.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgapo.dart) | Dart | -9 | 0 | -1 | -10 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_attachement.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_attachement.dart) | Dart | 0 | 0 | -1 | -1 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_auth.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_auth.dart) | Dart | -10 | 0 | -4 | -14 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_comment.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_comment.dart) | Dart | -70 | 0 | -11 | -81 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_comment.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_comment.g.dart) | Dart | -85 | -4 | -15 | -104 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_group.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_group.dart) | Dart | -29 | 0 | -4 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_group.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_group.g.dart) | Dart | -39 | -4 | -7 | -50 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_message.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_message.dart) | Dart | -23 | 0 | -5 | -28 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_message.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_message.g.dart) | Dart | -23 | -4 | -6 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_post.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_post.dart) | Dart | -121 | 0 | -10 | -131 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_post.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_post.g.dart) | Dart | -140 | -4 | -16 | -160 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_thread.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_thread.dart) | Dart | -21 | 0 | -5 | -26 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gp_thread.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgp_thread.g.dart) | Dart | -21 | -4 | -6 | -31 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gpuser.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgpuser.dart) | Dart | -21 | 0 | -7 | -28 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\gpuser.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cgpuser.g.dart) | Dart | -19 | -4 | -5 | -28 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\callback\callback.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Ccallback%5Ccallback.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\callback\gp_upload_callback.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Ccallback%5Cgp_upload_callback.dart) | Dart | -46 | -8 | -13 | -67 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\callback\gp_upload_progress.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Ccallback%5Cgp_upload_progress.entity.dart) | Dart | -17 | -12 | -5 | -34 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\callback\gp_upload_progress.entity.freezed.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Ccallback%5Cgp_upload_progress.entity.freezed.dart) | Dart | -123 | -15 | -23 | -161 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\gp_dashboard_upload.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Cgp_dashboard_upload.entity.dart) | Dart | -13 | -14 | -7 | -34 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\gp_dashboard_upload.entity.freezed.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Cgp_dashboard_upload.entity.freezed.dart) | Dart | -183 | -27 | -37 | -247 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\params\gp_upload_params.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Cparams%5Cgp_upload_params.dart) | Dart | -25 | -2 | -6 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\params\gp_upload_repo_params.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Cparams%5Cgp_upload_repo_params.dart) | Dart | -16 | 0 | -4 | -20 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\params\params.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Cparams%5Cparams.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\gapo\upload\upload.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cgapo%5Cupload%5Cupload.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\feed\feed.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cfeed%5Cfeed.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_base_feed.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cfeed%5Cworkplace_base_feed.entity.dart) | Dart | -44 | -10 | -14 | -68 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_base_feed.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cfeed%5Cworkplace_base_feed.entity.g.dart) | Dart | -1,822 | -6 | -175 | -2,003 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_group_feed.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cfeed%5Cworkplace_group_feed.entity.dart) | Dart | -18 | -9 | -6 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_group_feed.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cfeed%5Cworkplace_group_feed.entity.g.dart) | Dart | -608 | -6 | -63 | -677 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_user_feed.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cfeed%5Cworkplace_user_feed.entity.dart) | Dart | -19 | -9 | -7 | -35 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_user_feed.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cfeed%5Cworkplace_user_feed.entity.g.dart) | Dart | -620 | -6 | -66 | -692 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\group\group.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cgroup%5Cgroup.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\group\workplace_group.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cgroup%5Cworkplace_group.entity.dart) | Dart | -52 | -9 | -10 | -71 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\group\workplace_group.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cgroup%5Cworkplace_group.entity.g.dart) | Dart | -2,575 | -11 | -245 | -2,831 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\other\other.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cother%5Cother.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\other\workplace_attachment.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cother%5Cworkplace_attachment.entity.dart) | Dart | -66 | -11 | -24 | -101 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\other\workplace_attachment.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cother%5Cworkplace_attachment.entity.g.dart) | Dart | -2,979 | -19 | -240 | -3,238 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\other\workplace_comment.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cother%5Cworkplace_comment.entity.dart) | Dart | -62 | -9 | -16 | -87 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\other\workplace_comment.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cother%5Cworkplace_comment.entity.g.dart) | Dart | -2,614 | -13 | -222 | -2,849 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\thread\thread.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cthread%5Cthread.dart) | Dart | -2 | 0 | -1 | -3 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\thread\workplace_conversation_attachment.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cthread%5Cworkplace_conversation_attachment.entity.dart) | Dart | -61 | -9 | -13 | -83 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\thread\workplace_conversation_attachment.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cthread%5Cworkplace_conversation_attachment.entity.g.dart) | Dart | -2,909 | -13 | -247 | -3,169 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\thread\workplace_conversations.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cthread%5Cworkplace_conversations.entity.dart) | Dart | -77 | -10 | -21 | -108 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\thread\workplace_conversations.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cthread%5Cworkplace_conversations.entity.g.dart) | Dart | -2,897 | -10 | -284 | -3,191 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\user\user.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cuser%5Cuser.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\user\workplace_community_member.entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cuser%5Cworkplace_community_member.entity.dart) | Dart | -32 | -12 | -11 | -55 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\user\workplace_community_member.entity.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cuser%5Cworkplace_community_member.entity.g.dart) | Dart | -2,239 | -6 | -219 | -2,464 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\entity\workplace\workplace.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Centity%5Cworkplace%5Cworkplace.dart) | Dart | -5 | 0 | -1 | -6 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\repository\download_repo.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Crepository%5Cdownload_repo.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\repository\facebook_repo.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Crepository%5Cfacebook_repo.dart) | Dart | -4 | -1 | -2 | -7 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\repository\gapo_repo.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Crepository%5Cgapo_repo.dart) | Dart | -15 | -10 | -6 | -31 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\repository\repository.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Crepository%5Crepository.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\repository\workplace_repo.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Crepository%5Cworkplace_repo.dart) | Dart | -22 | -11 | -12 | -45 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\download_file.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cdownload_file.usecase.dart) | Dart | -31 | 0 | -9 | -40 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\download_then_upload.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cdownload_then_upload.usecase.dart) | Dart | -61 | -12 | -9 | -82 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\facebook_get_community.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cfacebook_get_community.usecase.dart) | Dart | -19 | -10 | -7 | -36 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\feed_attachment.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cfeed_attachment.usecase.dart) | Dart | -70 | -10 | -11 | -91 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\gapo\gapo.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cgapo%5Cgapo.dart) | Dart | -5 | 0 | -1 | -6 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_auth.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cgapo%5Cgapo_auth.usecase.dart) | Dart | -47 | -10 | -14 | -71 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_auth_check_mail.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cgapo%5Cgapo_auth_check_mail.usecase.dart) | Dart | -17 | -9 | -5 | -31 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_create_user.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cgapo%5Cgapo_create_user.usecase.dart) | Dart | -22 | -9 | -7 | -38 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_login.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cgapo%5Cgapo_login.usecase.dart) | Dart | -68 | 0 | -13 | -81 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_upload.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cgapo%5Cgapo_upload.usecase.dart) | Dart | -41 | -10 | -13 | -64 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cusecase.dart) | Dart | -14 | 0 | -1 | -15 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\workplace_get_all_groups.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cworkplace_get_all_groups.usecase.dart) | Dart | -25 | -10 | -8 | -43 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\workplace_get_app_config.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cworkplace_get_app_config.usecase.dart) | Dart | -20 | -10 | -7 | -37 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\workplace_get_community_members.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cworkplace_get_community_members.usecase.dart) | Dart | -26 | -1 | -7 | -34 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\workplace_get_conversations.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cworkplace_get_conversations.usecase.dart) | Dart | -33 | -10 | -8 | -51 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\workplace_get_group_feeds.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cworkplace_get_group_feeds.usecase.dart) | Dart | -28 | -1 | -7 | -36 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\workplace_get_group_members.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cworkplace_get_group_members.usecase.dart) | Dart | -27 | -1 | -7 | -35 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\workplace_get_post_attachments.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cworkplace_get_post_attachments.usecase.dart) | Dart | -27 | -1 | -7 | -35 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\workplace_get_post_comments.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cworkplace_get_post_comments.usecase.dart) | Dart | -27 | -10 | -8 | -45 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\domain\usecase\workplace_get_user_feeds.usecase.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cdomain%5Cusecase%5Cworkplace_get_user_feeds.usecase.dart) | Dart | -27 | -1 | -7 | -35 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\flutter_gen\assets.gen.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cflutter_gen%5Cassets.gen.dart) | Dart | -68 | -10 | -14 | -92 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\flutter_gen\flutter_gen.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cflutter_gen%5Cflutter_gen.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\helpers\csv.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Chelpers%5Ccsv.dart) | Dart | -32 | 0 | -5 | -37 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\helpers\file_helper.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Chelpers%5Cfile_helper.dart) | Dart | -36 | 0 | -9 | -45 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\helpers\gp_upload_management.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Chelpers%5Cgp_upload_management.dart) | Dart | -143 | -18 | -41 | -202 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\helpers\helpers.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Chelpers%5Chelpers.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\helpers\jwt_token_decode.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Chelpers%5Cjwt_token_decode.dart) | Dart | -58 | -31 | -10 | -99 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\l10n\app_en.arb](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cl10n%5Capp_en.arb) | JSON | -4 | 0 | 0 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\l10n\app_localizations.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cl10n%5Capp_localizations.dart) | Dart | -50 | -75 | -19 | -144 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\l10n\app_localizations_en.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cl10n%5Capp_localizations_en.dart) | Dart | -13 | -5 | -7 | -25 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\l10n\app_localizations_vi.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cl10n%5Capp_localizations_vi.dart) | Dart | -13 | -5 | -7 | -25 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\l10n\app_vi.arb](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cl10n%5Capp_vi.arb) | JSON | -17 | 0 | 0 | -17 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\l10n\l10n.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cl10n%5Cl10n.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\main.production.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmain.production.dart) | Dart | -8 | 0 | -3 | -11 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\main.staging.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmain.staging.dart) | Dart | -8 | 0 | -3 | -11 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\main.uat.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmain.uat.dart) | Dart | -8 | 0 | -3 | -11 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\mapper\entity\entity.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmapper%5Centity%5Centity.dart) | Dart | -4 | 0 | -1 | -5 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\mapper\entity\gapo_entity_mapper.auto_mappr.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmapper%5Centity%5Cgapo_entity_mapper.auto_mappr.dart) | Dart | -487 | -47 | -37 | -571 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\mapper\entity\gapo_entity_mapper.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmapper%5Centity%5Cgapo_entity_mapper.dart) | Dart | -219 | -18 | -36 | -273 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\mapper\entity\workplace_entity_mapper.auto_mappr.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmapper%5Centity%5Cworkplace_entity_mapper.auto_mappr.dart) | Dart | -860 | -56 | -47 | -963 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\mapper\entity\workplace_entity_mapper.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmapper%5Centity%5Cworkplace_entity_mapper.dart) | Dart | -216 | -12 | -23 | -251 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\mapper\gp_mapper.auto_mappr.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmapper%5Cgp_mapper.auto_mappr.dart) | Dart | -191 | -60 | -28 | -279 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\mapper\gp_mapper.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmapper%5Cgp_mapper.dart) | Dart | -62 | -13 | -10 | -85 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\mapper\mapper.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Cmapper%5Cmapper.dart) | Dart | -3 | 0 | -1 | -4 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\route\go_router.route.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Croute%5Cgo_router.route.dart) | Dart | -41 | -10 | -10 | -61 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\route\go_router.route.g.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Croute%5Cgo_router.route.g.dart) | Dart | -53 | -4 | -25 | -82 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\lib\route\route.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clib%5Croute%5Croute.dart) | Dart | -1 | 0 | -1 | -2 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\linux\main.cc](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clinux%5Cmain.cc) | C++ | -5 | 0 | -2 | -7 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\linux\my_application.cc](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clinux%5Cmy_application.cc) | C++ | -74 | -11 | -20 | -105 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\linux\my_application.h](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Clinux%5Cmy_application.h) | C++ | -7 | -7 | -5 | -19 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\macos\RunnerTests\RunnerTests.swift](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cmacos%5CRunnerTests%5CRunnerTests.swift) | Swift | -7 | -2 | -4 | -13 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\macos\Runner\AppDelegate.swift](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cmacos%5CRunner%5CAppDelegate.swift) | Swift | -8 | 0 | -2 | -10 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\macos\Runner\Assets.xcassets\AppIcon.appiconset\Contents.json](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cmacos%5CRunner%5CAssets.xcassets%5CAppIcon.appiconset%5CContents.json) | JSON | -68 | 0 | 0 | -68 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\macos\Runner\Base.lproj\MainMenu.xib](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cmacos%5CRunner%5CBase.lproj%5CMainMenu.xib) | XML | -343 | 0 | -1 | -344 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\macos\Runner\MainFlutterWindow.swift](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cmacos%5CRunner%5CMainFlutterWindow.swift) | Swift | -12 | 0 | -4 | -16 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\project_configs\flutter_launcher_icons-dev.yaml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cproject_configs%5Cflutter_launcher_icons-dev.yaml) | YAML | -17 | 0 | 0 | -17 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\project_configs\flutter_launcher_icons-prod.yaml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cproject_configs%5Cflutter_launcher_icons-prod.yaml) | YAML | -17 | 0 | 0 | -17 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\project_configs\flutter_launcher_icons-uat.yaml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cproject_configs%5Cflutter_launcher_icons-uat.yaml) | YAML | -17 | 0 | 0 | -17 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\project_configs\package_rename_config-dev.yaml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cproject_configs%5Cpackage_rename_config-dev.yaml) | YAML | -27 | 0 | -6 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\project_configs\package_rename_config-prod.yaml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cproject_configs%5Cpackage_rename_config-prod.yaml) | YAML | -27 | 0 | -6 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\project_configs\package_rename_config-uat.yaml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cproject_configs%5Cpackage_rename_config-uat.yaml) | YAML | -27 | 0 | -6 | -33 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\pubspec.yaml](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cpubspec.yaml) | YAML | -68 | -6 | -1 | -75 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\test\widget_test.dart](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Ctest%5Cwidget_test.dart) | Dart | -14 | -10 | -6 | -30 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\web\index.html](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cweb%5Cindex.html) | HTML | -35 | -16 | -17 | -68 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\web\manifest.json](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cweb%5Cmanifest.json) | JSON | -35 | 0 | -1 | -36 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\windows\runner\flutter_window.cpp](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cwindows%5Crunner%5Cflutter_window.cpp) | C++ | -49 | -7 | -16 | -72 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\windows\runner\flutter_window.h](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cwindows%5Crunner%5Cflutter_window.h) | C++ | -20 | -5 | -9 | -34 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\windows\runner\main.cpp](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cwindows%5Crunner%5Cmain.cpp) | C++ | -30 | -4 | -10 | -44 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\windows\runner\resource.h](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cwindows%5Crunner%5Cresource.h) | C++ | -9 | -6 | -2 | -17 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\windows\runner\utils.cpp](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cwindows%5Crunner%5Cutils.cpp) | C++ | -54 | -2 | -10 | -66 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\windows\runner\utils.h](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cwindows%5Crunner%5Cutils.h) | C++ | -8 | -6 | -6 | -20 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\windows\runner\win32_window.cpp](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cwindows%5Crunner%5Cwin32_window.cpp) | C++ | -210 | -24 | -55 | -289 |
| [\Users\toannm\Softwares\flutter.packages\gapoflutter-crawler\windows\runner\win32_window.h](/%5CUsers%5Ctoannm%5CSoftwares%5Cflutter.packages%5Cgapoflutter-crawler%5Cwindows%5Crunner%5Cwin32_window.h) | C++ | -48 | -31 | -24 | -103 |
| [README.md](/README.md) | Markdown | 40 | 0 | 12 | 52 |
| [analysis_options.yaml](/analysis_options.yaml) | YAML | 7 | 18 | 3 | 28 |
| [android/app/build.gradle](/android/app/build.gradle) | Groovy | 51 | 5 | 12 | 68 |
| [android/app/src/debug/AndroidManifest.xml](/android/app/src/debug/AndroidManifest.xml) | XML | 7 | 4 | 0 | 11 |
| [android/app/src/main/AndroidManifest.xml](/android/app/src/main/AndroidManifest.xml) | XML | 32 | 6 | 0 | 38 |
| [android/app/src/main/kotlin/vn/gapowork/crawler/MainActivity.kt](/android/app/src/main/kotlin/vn/gapowork/crawler/MainActivity.kt) | Kotlin | 4 | 0 | 3 | 7 |
| [android/app/src/main/res/drawable-v21/launch_background.xml](/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [android/app/src/main/res/drawable/launch_background.xml](/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [android/app/src/main/res/values-night/styles.xml](/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [android/app/src/main/res/values/styles.xml](/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [android/build.gradle](/android/build.gradle) | Groovy | 27 | 0 | 5 | 32 |
| [android/gradle.properties](/android/gradle.properties) | Java Properties | 3 | 0 | 1 | 4 |
| [android/gradle/wrapper/gradle-wrapper.properties](/android/gradle/wrapper/gradle-wrapper.properties) | Java Properties | 5 | 0 | 1 | 6 |
| [android/settings.gradle](/android/settings.gradle) | Groovy | 16 | 0 | 5 | 21 |
| [assets/images/splash_loading_1.json](/assets/images/splash_loading_1.json) | JSON | 1 | 0 | 0 | 1 |
| [assets/images/splash_loading_2.json](/assets/images/splash_loading_2.json) | JSON | 1 | 0 | 0 | 1 |
| [build.yaml](/build.yaml) | YAML | 7 | 0 | 0 | 7 |
| [ios/RunnerTests/RunnerTests.swift](/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [ios/Runner/AppDelegate.swift](/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [ios/Runner/Assets.xcassets/AppIcon-dev.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon-dev.appiconset/Contents.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/Assets.xcassets/AppIcon-prod.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon-prod.appiconset/Contents.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/Assets.xcassets/AppIcon-uat.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon-uat.appiconset/Contents.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 122 | 0 | 1 | 123 |
| [ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [ios/Runner/Base.lproj/LaunchScreen.storyboard](/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [ios/Runner/Base.lproj/Main.storyboard](/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [ios/Runner/Runner-Bridging-Header.h](/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [lib/app/app.dart](/lib/app/app.dart) | Dart | 8 | 0 | 1 | 9 |
| [lib/app/app_config/app_config.dart](/lib/app/app_config/app_config.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/app/app_config/bloc/app_config_bloc.dart](/lib/app/app_config/bloc/app_config_bloc.dart) | Dart | 93 | 0 | 16 | 109 |
| [lib/app/app_config/bloc/app_config_event.dart](/lib/app/app_config/bloc/app_config_event.dart) | Dart | 16 | 0 | 5 | 21 |
| [lib/app/app_config/bloc/app_config_state.dart](/lib/app/app_config/bloc/app_config_state.dart) | Dart | 17 | 1 | 5 | 23 |
| [lib/app/app_config/bloc/app_config_state.freezed.dart](/lib/app/app_config/bloc/app_config_state.freezed.dart) | Dart | 118 | 15 | 23 | 156 |
| [lib/app/app_config/bloc/bloc.dart](/lib/app/app_config/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/app_config/widgets/app_config_body_page.dart](/lib/app/app_config/widgets/app_config_body_page.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/app/app_config/widgets/widgets.dart](/lib/app/app_config/widgets/widgets.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/base/base.dart](/lib/app/base/base.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/base/networking/exception/exception.dart](/lib/app/base/networking/exception/exception.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/base/networking/exception/exception_code.dart](/lib/app/base/networking/exception/exception_code.dart) | Dart | 3 | 5 | 3 | 11 |
| [lib/app/base/networking/gapo/authentication_interceptor.dart](/lib/app/base/networking/gapo/authentication_interceptor.dart) | Dart | 33 | 0 | 11 | 44 |
| [lib/app/base/networking/gapo/gapo.dart](/lib/app/base/networking/gapo/gapo.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/app/base/networking/gapo/gp_token_interceptor.dart](/lib/app/base/networking/gapo/gp_token_interceptor.dart) | Dart | 113 | 39 | 33 | 185 |
| [lib/app/base/networking/logger_inteceptor.dart](/lib/app/base/networking/logger_inteceptor.dart) | Dart | 75 | 7 | 18 | 100 |
| [lib/app/base/networking/networking.dart](/lib/app/base/networking/networking.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/app/base/networking/workplace_auth_inteceptor.dart](/lib/app/base/networking/workplace_auth_inteceptor.dart) | Dart | 29 | 0 | 8 | 37 |
| [lib/app/constant/app_constant.dart](/lib/app/constant/app_constant.dart) | Dart | 7 | 0 | 2 | 9 |
| [lib/app/constant/constant.dart](/lib/app/constant/constant.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/constant/fb_wp_url.constants.dart](/lib/app/constant/fb_wp_url.constants.dart) | Dart | 16 | 11 | 5 | 32 |
| [lib/app/constant/gapo_url.constants.dart](/lib/app/constant/gapo_url.constants.dart) | Dart | 14 | 13 | 5 | 32 |
| [lib/app/features/crawler/base_crawl_bloc/base_crawl_bloc.dart](/lib/app/features/crawler/base_crawl_bloc/base_crawl_bloc.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/app/features/crawler/base_crawl_bloc/base_crawl_bloc_page.dart](/lib/app/features/crawler/base_crawl_bloc/base_crawl_bloc_page.dart) | Dart | 21 | 0 | 4 | 25 |
| [lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_bloc.dart](/lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_bloc.dart) | Dart | 28 | 1 | 5 | 34 |
| [lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_event.dart](/lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_event.dart) | Dart | 7 | 0 | 2 | 9 |
| [lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_state.dart](/lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_state.dart) | Dart | 14 | 1 | 5 | 20 |
| [lib/app/features/crawler/base_crawl_bloc/bloc/bloc.dart](/lib/app/features/crawler/base_crawl_bloc/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/base_crawl_bloc/widgets/base_crawl_bloc_body_page.dart](/lib/app/features/crawler/base_crawl_bloc/widgets/base_crawl_bloc_body_page.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/app/features/crawler/base_crawl_bloc/widgets/widgets.dart](/lib/app/features/crawler/base_crawl_bloc/widgets/widgets.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/bloc/bloc.dart](/lib/app/features/crawler/bloc/bloc.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/app/features/crawler/bloc/crawl_bloc.dart](/lib/app/features/crawler/bloc/crawl_bloc.dart) | Dart | 30 | 211 | 35 | 276 |
| [lib/app/features/crawler/bloc/crawl_bloc_bak.dart](/lib/app/features/crawler/bloc/crawl_bloc_bak.dart) | Dart | 0 | 535 | 69 | 604 |
| [lib/app/features/crawler/bloc/crawl_event.dart](/lib/app/features/crawler/bloc/crawl_event.dart) | Dart | 12 | 0 | 4 | 16 |
| [lib/app/features/crawler/bloc/crawl_state.dart](/lib/app/features/crawler/bloc/crawl_state.dart) | Dart | 48 | 1 | 6 | 55 |
| [lib/app/features/crawler/bloc/crawl_state.freezed.dart](/lib/app/features/crawler/bloc/crawl_state.freezed.dart) | Dart | 549 | 31 | 78 | 658 |
| [lib/app/features/crawler/crawl.dart](/lib/app/features/crawler/crawl.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/crawl.main.page.dart](/lib/app/features/crawler/crawl.main.page.dart) | Dart | 87 | 0 | 8 | 95 |
| [lib/app/features/crawler/crawl_feed/bloc/bloc.dart](/lib/app/features/crawler/crawl_feed/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/crawl_feed/bloc/crawl_feed_bloc.dart](/lib/app/features/crawler/crawl_feed/bloc/crawl_feed_bloc.dart) | Dart | 205 | 15 | 66 | 286 |
| [lib/app/features/crawler/crawl_feed/bloc/crawl_feed_event.dart](/lib/app/features/crawler/crawl_feed/bloc/crawl_feed_event.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/app/features/crawler/crawl_feed/bloc/crawl_feed_state.dart](/lib/app/features/crawler/crawl_feed/bloc/crawl_feed_state.dart) | Dart | 14 | 1 | 5 | 20 |
| [lib/app/features/crawler/crawl_feed/bloc/crawl_feed_state.freezed.dart](/lib/app/features/crawler/crawl_feed/bloc/crawl_feed_state.freezed.dart) | Dart | 45 | 15 | 17 | 77 |
| [lib/app/features/crawler/crawl_feed/crawl_feed.dart](/lib/app/features/crawler/crawl_feed/crawl_feed.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/crawl_group/bloc/bloc.dart](/lib/app/features/crawler/crawl_group/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/crawl_group/bloc/crawl_group_bloc.dart](/lib/app/features/crawler/crawl_group/bloc/crawl_group_bloc.dart) | Dart | 121 | 5 | 31 | 157 |
| [lib/app/features/crawler/crawl_group/bloc/crawl_group_event.dart](/lib/app/features/crawler/crawl_group/bloc/crawl_group_event.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/app/features/crawler/crawl_group/bloc/crawl_group_state.dart](/lib/app/features/crawler/crawl_group/bloc/crawl_group_state.dart) | Dart | 14 | 1 | 5 | 20 |
| [lib/app/features/crawler/crawl_group/bloc/crawl_group_state.freezed.dart](/lib/app/features/crawler/crawl_group/bloc/crawl_group_state.freezed.dart) | Dart | 45 | 15 | 17 | 77 |
| [lib/app/features/crawler/crawl_group/crawl_group.dart](/lib/app/features/crawler/crawl_group/crawl_group.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/crawl_member/bloc/bloc.dart](/lib/app/features/crawler/crawl_member/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/crawl_member/bloc/crawl_member_bloc.dart](/lib/app/features/crawler/crawl_member/bloc/crawl_member_bloc.dart) | Dart | 116 | 7 | 29 | 152 |
| [lib/app/features/crawler/crawl_member/bloc/crawl_member_event.dart](/lib/app/features/crawler/crawl_member/bloc/crawl_member_event.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/app/features/crawler/crawl_member/bloc/crawl_member_state.dart](/lib/app/features/crawler/crawl_member/bloc/crawl_member_state.dart) | Dart | 14 | 1 | 5 | 20 |
| [lib/app/features/crawler/crawl_member/bloc/crawl_member_state.freezed.dart](/lib/app/features/crawler/crawl_member/bloc/crawl_member_state.freezed.dart) | Dart | 45 | 15 | 17 | 77 |
| [lib/app/features/crawler/crawl_member/crawl_member.dart](/lib/app/features/crawler/crawl_member/crawl_member.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/crawl_thread/bloc/bloc.dart](/lib/app/features/crawler/crawl_thread/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/crawl_thread/bloc/crawl_thread_bloc.dart](/lib/app/features/crawler/crawl_thread/bloc/crawl_thread_bloc.dart) | Dart | 131 | 15 | 35 | 181 |
| [lib/app/features/crawler/crawl_thread/bloc/crawl_thread_event.dart](/lib/app/features/crawler/crawl_thread/bloc/crawl_thread_event.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/app/features/crawler/crawl_thread/bloc/crawl_thread_state.dart](/lib/app/features/crawler/crawl_thread/bloc/crawl_thread_state.dart) | Dart | 14 | 1 | 5 | 20 |
| [lib/app/features/crawler/crawl_thread/bloc/crawl_thread_state.freezed.dart](/lib/app/features/crawler/crawl_thread/bloc/crawl_thread_state.freezed.dart) | Dart | 45 | 15 | 17 | 77 |
| [lib/app/features/crawler/crawl_thread/crawl_thread.dart](/lib/app/features/crawler/crawl_thread/crawl_thread.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/crawler.dart](/lib/app/features/crawler/crawler.dart) | Dart | 10 | 0 | 1 | 11 |
| [lib/app/features/crawler/mixin/mixin.dart](/lib/app/features/crawler/mixin/mixin.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/mixin/upload_mixin.dart](/lib/app/features/crawler/mixin/upload_mixin.dart) | Dart | 94 | 1 | 21 | 116 |
| [lib/app/features/crawler/sync/sync.dart](/lib/app/features/crawler/sync/sync.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/sync/sync.page.dart](/lib/app/features/crawler/sync/sync.page.dart) | Dart | 0 | 0 | 2 | 2 |
| [lib/app/features/crawler/unsync/unsync.dart](/lib/app/features/crawler/unsync/unsync.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/unsync/unsync.page.dart](/lib/app/features/crawler/unsync/unsync.page.dart) | Dart | 0 | 0 | 2 | 2 |
| [lib/app/features/features.dart](/lib/app/features/features.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/app/features/home/<USER>/bloc.dart](/lib/app/features/home/<USER>/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/home/<USER>/home_page_bloc.dart](/lib/app/features/home/<USER>/home_page_bloc.dart) | Dart | 7 | 0 | 2 | 9 |
| [lib/app/features/home/<USER>/home_page_event.dart](/lib/app/features/home/<USER>/home_page_event.dart) | Dart | 0 | 0 | 2 | 2 |
| [lib/app/features/home/<USER>/home_page_state.dart](/lib/app/features/home/<USER>/home_page_state.dart) | Dart | 15 | 0 | 3 | 18 |
| [lib/app/features/home/<USER>/home_page_state.freezed.dart](/lib/app/features/home/<USER>/home_page_state.freezed.dart) | Dart | 96 | 15 | 23 | 134 |
| [lib/app/features/home/<USER>/lib/app/features/home/<USER>
| [lib/app/features/home/<USER>/lib/app/features/home/<USER>
| [lib/app/main.app.dart](/lib/app/main.app.dart) | Dart | 55 | 0 | 4 | 59 |
| [lib/app/splash/splash.dart](/lib/app/splash/splash.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/splash/splash.page.dart](/lib/app/splash/splash.page.dart) | Dart | 28 | 0 | 3 | 31 |
| [lib/app/test_async.page.dart](/lib/app/test_async.page.dart) | Dart | 118 | 5 | 26 | 149 |
| [lib/app/test_attachment.page.dart](/lib/app/test_attachment.page.dart) | Dart | 165 | 1 | 24 | 190 |
| [lib/config/app_configs.dart](/lib/config/app_configs.dart) | Dart | 29 | 0 | 11 | 40 |
| [lib/config/bootstrap.dart](/lib/config/bootstrap.dart) | Dart | 19 | 0 | 6 | 25 |
| [lib/config/config.dart](/lib/config/config.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/data.dart](/lib/data/data.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/data/data_source/data_source.dart](/lib/data/data_source/data_source.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/data_source/local/local.dart](/lib/data/data_source/local/local.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/data_source/local/workplace_local.service.dart](/lib/data/data_source/local/workplace_local.service.dart) | Dart | 180 | 9 | 32 | 221 |
| [lib/data/data_source/remote/download/download.dart](/lib/data/data_source/remote/download/download.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/data_source/remote/download/download.service.dart](/lib/data/data_source/remote/download/download.service.dart) | Dart | 32 | 0 | 4 | 36 |
| [lib/data/data_source/remote/facebook.service.dart](/lib/data/data_source/remote/facebook.service.dart) | Dart | 20 | 10 | 6 | 36 |
| [lib/data/data_source/remote/facebook.service.g.dart](/lib/data/data_source/remote/facebook.service.g.dart) | Dart | 60 | 5 | 13 | 78 |
| [lib/data/data_source/remote/gapo/auth.service.dart](/lib/data/data_source/remote/gapo/auth.service.dart) | Dart | 29 | 9 | 7 | 45 |
| [lib/data/data_source/remote/gapo/auth.service.g.dart](/lib/data/data_source/remote/gapo/auth.service.g.dart) | Dart | 127 | 5 | 15 | 147 |
| [lib/data/data_source/remote/gapo/gapo.dart](/lib/data/data_source/remote/gapo/gapo.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/data_source/remote/gapo/upload.service.dart](/lib/data/data_source/remote/gapo/upload.service.dart) | Dart | 43 | 16 | 9 | 68 |
| [lib/data/data_source/remote/gapo/upload.service.g.dart](/lib/data/data_source/remote/gapo/upload.service.g.dart) | Dart | 162 | 5 | 15 | 182 |
| [lib/data/data_source/remote/remote.dart](/lib/data/data_source/remote/remote.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/data/data_source/remote/workplace.service.dart](/lib/data/data_source/remote/workplace.service.dart) | Dart | 67 | 10 | 14 | 91 |
| [lib/data/data_source/remote/workplace.service.g.dart](/lib/data/data_source/remote/workplace.service.g.dart) | Dart | 315 | 5 | 20 | 340 |
| [lib/data/model/datetime_converter.dart](/lib/data/model/datetime_converter.dart) | Dart | 11 | 0 | 3 | 14 |
| [lib/data/model/download/download.dart](/lib/data/model/download/download.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/download/download_params.dart](/lib/data/model/download/download_params.dart) | Dart | 13 | 0 | 2 | 15 |
| [lib/data/model/download/download_params.freezed.dart](/lib/data/model/download/download_params.freezed.dart) | Dart | 118 | 15 | 23 | 156 |
| [lib/data/model/facebook/community_response.dart](/lib/data/model/facebook/community_response.dart) | Dart | 16 | 0 | 5 | 21 |
| [lib/data/model/facebook/community_response.g.dart](/lib/data/model/facebook/community_response.g.dart) | Dart | 8 | 4 | 4 | 16 |
| [lib/data/model/facebook/facebook.dart](/lib/data/model/facebook/facebook.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/gpw/auth/auth.dart](/lib/data/model/gpw/auth/auth.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/gpw/auth/request/auth_check_email_request.dart](/lib/data/model/gpw/auth/request/auth_check_email_request.dart) | Dart | 16 | 9 | 8 | 33 |
| [lib/data/model/gpw/auth/request/auth_check_email_request.g.dart](/lib/data/model/gpw/auth/request/auth_check_email_request.g.dart) | Dart | 13 | 4 | 5 | 22 |
| [lib/data/model/gpw/auth/request/gp_auth_params.dart](/lib/data/model/gpw/auth/request/gp_auth_params.dart) | Dart | 18 | 1 | 4 | 23 |
| [lib/data/model/gpw/auth/request/gp_auth_params.freezed.dart](/lib/data/model/gpw/auth/request/gp_auth_params.freezed.dart) | Dart | 226 | 15 | 23 | 264 |
| [lib/data/model/gpw/auth/request/gp_auth_params.g.dart](/lib/data/model/gpw/auth/request/gp_auth_params.g.dart) | Dart | 10 | 4 | 4 | 18 |
| [lib/data/model/gpw/auth/request/gp_signup_params.dart](/lib/data/model/gpw/auth/request/gp_signup_params.dart) | Dart | 21 | 1 | 4 | 26 |
| [lib/data/model/gpw/auth/request/gp_signup_params.freezed.dart](/lib/data/model/gpw/auth/request/gp_signup_params.freezed.dart) | Dart | 205 | 15 | 23 | 243 |
| [lib/data/model/gpw/auth/request/gp_signup_params.g.dart](/lib/data/model/gpw/auth/request/gp_signup_params.g.dart) | Dart | 10 | 4 | 4 | 18 |
| [lib/data/model/gpw/auth/request/request.dart](/lib/data/model/gpw/auth/request/request.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/data/model/gpw/auth/response/auth_check_mail_response.dart](/lib/data/model/gpw/auth/response/auth_check_mail_response.dart) | Dart | 18 | 9 | 8 | 35 |
| [lib/data/model/gpw/auth/response/auth_check_mail_response.g.dart](/lib/data/model/gpw/auth/response/auth_check_mail_response.g.dart) | Dart | 15 | 4 | 5 | 24 |
| [lib/data/model/gpw/auth/response/response.dart](/lib/data/model/gpw/auth/response/response.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/gpw/auth_reponse.dart](/lib/data/model/gpw/auth_reponse.dart) | Dart | 17 | 0 | 8 | 25 |
| [lib/data/model/gpw/auth_reponse.g.dart](/lib/data/model/gpw/auth_reponse.g.dart) | Dart | 6 | 4 | 4 | 14 |
| [lib/data/model/gpw/gpw.dart](/lib/data/model/gpw/gpw.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/data/model/gpw/upload_file_response.dart](/lib/data/model/gpw/upload_file_response.dart) | Dart | 18 | 0 | 4 | 22 |
| [lib/data/model/model.dart](/lib/data/model/model.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/data/model/workplace/base/base.dart](/lib/data/model/workplace/base/base.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/data/model/workplace/base/workplace_generic_data_converter.dart](/lib/data/model/workplace/base/workplace_generic_data_converter.dart) | Dart | 48 | 0 | 7 | 55 |
| [lib/data/model/workplace/base/workplace_list_response.dart](/lib/data/model/workplace/base/workplace_list_response.dart) | Dart | 16 | 0 | 6 | 22 |
| [lib/data/model/workplace/base/workplace_list_response.g.dart](/lib/data/model/workplace/base/workplace_list_response.g.dart) | Dart | 12 | 4 | 4 | 20 |
| [lib/data/model/workplace/base/workplace_paging_response.dart](/lib/data/model/workplace/base/workplace_paging_response.dart) | Dart | 26 | 0 | 7 | 33 |
| [lib/data/model/workplace/base/workplace_paging_response.g.dart](/lib/data/model/workplace/base/workplace_paging_response.g.dart) | Dart | 17 | 4 | 5 | 26 |
| [lib/data/model/workplace/base/workplace_user.dart](/lib/data/model/workplace/base/workplace_user.dart) | Dart | 30 | 3 | 10 | 43 |
| [lib/data/model/workplace/base/workplace_user.g.dart](/lib/data/model/workplace/base/workplace_user.g.dart) | Dart | 27 | 4 | 5 | 36 |
| [lib/data/model/workplace/comment/comment.dart](/lib/data/model/workplace/comment/comment.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/comment/message_tags.dart](/lib/data/model/workplace/comment/message_tags.dart) | Dart | 21 | 0 | 5 | 26 |
| [lib/data/model/workplace/comment/message_tags.g.dart](/lib/data/model/workplace/comment/message_tags.g.dart) | Dart | 27 | 4 | 6 | 37 |
| [lib/data/model/workplace/comment/workplace_reactions.dart](/lib/data/model/workplace/comment/workplace_reactions.dart) | Dart | 19 | 0 | 7 | 26 |
| [lib/data/model/workplace/comment/workplace_reactions.g.dart](/lib/data/model/workplace/comment/workplace_reactions.g.dart) | Dart | 24 | 4 | 6 | 34 |
| [lib/data/model/workplace/conversation/conversation.dart](/lib/data/model/workplace/conversation/conversation.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart](/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart) | Dart | 56 | 0 | 12 | 68 |
| [lib/data/model/workplace/conversation/workplace_conversation_attachment_response.g.dart](/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.g.dart) | Dart | 34 | 4 | 6 | 44 |
| [lib/data/model/workplace/conversation/workplace_conversations_response.dart](/lib/data/model/workplace/conversation/workplace_conversations_response.dart) | Dart | 40 | 0 | 13 | 53 |
| [lib/data/model/workplace/conversation/workplace_conversations_response.g.dart](/lib/data/model/workplace/conversation/workplace_conversations_response.g.dart) | Dart | 35 | 4 | 6 | 45 |
| [lib/data/model/workplace/enums/enums.dart](/lib/data/model/workplace/enums/enums.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/workplace/enums/workplace_enums.dart](/lib/data/model/workplace/enums/workplace_enums.dart) | Dart | 53 | 0 | 17 | 70 |
| [lib/data/model/workplace/group/group.dart](/lib/data/model/workplace/group/group.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/group/workplace_feeds_response.dart](/lib/data/model/workplace/group/workplace_feeds_response.dart) | Dart | 29 | 2 | 7 | 38 |
| [lib/data/model/workplace/group/workplace_feeds_response.g.dart](/lib/data/model/workplace/group/workplace_feeds_response.g.dart) | Dart | 37 | 4 | 6 | 47 |
| [lib/data/model/workplace/group/workplace_group_response.dart](/lib/data/model/workplace/group/workplace_group_response.dart) | Dart | 46 | 0 | 11 | 57 |
| [lib/data/model/workplace/group/workplace_group_response.g.dart](/lib/data/model/workplace/group/workplace_group_response.g.dart) | Dart | 41 | 4 | 6 | 51 |
| [lib/data/model/workplace/post/post.dart](/lib/data/model/workplace/post/post.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.dart) | Dart | 63 | 0 | 13 | 76 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.g.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart) | Dart | 59 | 4 | 9 | 72 |
| [lib/data/model/workplace/post/workplace_post_comments_response.dart](/lib/data/model/workplace/post/workplace_post_comments_response.dart) | Dart | 31 | 0 | 9 | 40 |
| [lib/data/model/workplace/post/workplace_post_comments_response.g.dart](/lib/data/model/workplace/post/workplace_post_comments_response.g.dart) | Dart | 34 | 4 | 5 | 43 |
| [lib/data/model/workplace/workplace.dart](/lib/data/model/workplace/workplace.dart) | Dart | 6 | 0 | 1 | 7 |
| [lib/data/repository/download_impl.dart](/lib/data/repository/download_impl.dart) | Dart | 17 | 0 | 3 | 20 |
| [lib/data/repository/facebook_repo_impl.dart](/lib/data/repository/facebook_repo_impl.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/data/repository/gapo_impl.dart](/lib/data/repository/gapo_impl.dart) | Dart | 75 | 10 | 11 | 96 |
| [lib/data/repository/repository.dart](/lib/data/repository/repository.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/data/repository/workplace_repo_impl.dart](/lib/data/repository/workplace_repo_impl.dart) | Dart | 61 | 10 | 14 | 85 |
| [lib/di/component/app.component.config.dart](/lib/di/component/app.component.config.dart) | Dart | 316 | 8 | 12 | 336 |
| [lib/di/component/app.component.dart](/lib/di/component/app.component.dart) | Dart | 18 | 9 | 5 | 32 |
| [lib/di/component/component.dart](/lib/di/component/component.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/di/di.dart](/lib/di/di.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/di/modules/app.module.dart](/lib/di/modules/app.module.dart) | Dart | 26 | 9 | 6 | 41 |
| [lib/di/modules/auth.module.dart](/lib/di/modules/auth.module.dart) | Dart | 17 | 11 | 6 | 34 |
| [lib/di/modules/client.module.dart](/lib/di/modules/client.module.dart) | Dart | 44 | 9 | 12 | 65 |
| [lib/di/modules/database.module.dart](/lib/di/modules/database.module.dart) | Dart | 28 | 20 | 5 | 53 |
| [lib/di/modules/modules.dart](/lib/di/modules/modules.dart) | Dart | 6 | 0 | 1 | 7 |
| [lib/di/modules/navigator.module.dart](/lib/di/modules/navigator.module.dart) | Dart | 17 | 10 | 6 | 33 |
| [lib/di/modules/url.module.dart](/lib/di/modules/url.module.dart) | Dart | 26 | 12 | 8 | 46 |
| [lib/domain/domain.dart](/lib/domain/domain.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/base/app/app.dart](/lib/domain/entity/base/app/app.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/base/app/app_config.entity.dart](/lib/domain/entity/base/app/app_config.entity.dart) | Dart | 29 | 9 | 9 | 47 |
| [lib/domain/entity/base/app/app_config.entity.g.dart](/lib/domain/entity/base/app/app_config.entity.g.dart) | Dart | 701 | 6 | 73 | 780 |
| [lib/domain/entity/base/app/locale_enum.dart](/lib/domain/entity/base/app/locale_enum.dart) | Dart | 9 | 0 | 4 | 13 |
| [lib/domain/entity/base/base.dart](/lib/domain/entity/base/base.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/entity/base/base_crawl.entity.dart](/lib/domain/entity/base/base_crawl.entity.dart) | Dart | 144 | 10 | 21 | 175 |
| [lib/domain/entity/base/log/base_crawl_log.entity.dart](/lib/domain/entity/base/log/base_crawl_log.entity.dart) | Dart | 22 | 9 | 9 | 40 |
| [lib/domain/entity/base/log/base_crawl_log.entity.g.dart](/lib/domain/entity/base/log/base_crawl_log.entity.g.dart) | Dart | 1,010 | 6 | 87 | 1,103 |
| [lib/domain/entity/base/log/log.dart](/lib/domain/entity/base/log/log.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/base/status/base_crawl_status.entity.dart](/lib/domain/entity/base/status/base_crawl_status.entity.dart) | Dart | 60 | 12 | 13 | 85 |
| [lib/domain/entity/base/status/base_crawl_status.entity.g.dart](/lib/domain/entity/base/status/base_crawl_status.entity.g.dart) | Dart | 946 | 8 | 78 | 1,032 |
| [lib/domain/entity/base/status/status.dart](/lib/domain/entity/base/status/status.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/entity.dart](/lib/domain/entity/entity.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/entity/enums/base_crawl_status_enum.dart](/lib/domain/entity/enums/base_crawl_status_enum.dart) | Dart | 14 | 30 | 11 | 55 |
| [lib/domain/entity/enums/base_crawl_type.dart](/lib/domain/entity/enums/base_crawl_type.dart) | Dart | 9 | 16 | 8 | 33 |
| [lib/domain/entity/enums/enums.dart](/lib/domain/entity/enums/enums.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/entity/enums/gp_enums.dart](/lib/domain/entity/enums/gp_enums.dart) | Dart | 91 | 0 | 12 | 103 |
| [lib/domain/entity/enums/upload/gp_upload_status_enum.dart](/lib/domain/entity/enums/upload/gp_upload_status_enum.dart) | Dart | 12 | 0 | 5 | 17 |
| [lib/domain/entity/enums/upload/upload.dart](/lib/domain/entity/enums/upload/upload.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/gapo/gapo.dart](/lib/domain/entity/gapo/gapo.dart) | Dart | 9 | 0 | 1 | 10 |
| [lib/domain/entity/gapo/gp_attachement.dart](/lib/domain/entity/gapo/gp_attachement.dart) | Dart | 0 | 0 | 1 | 1 |
| [lib/domain/entity/gapo/gp_auth.dart](/lib/domain/entity/gapo/gp_auth.dart) | Dart | 10 | 0 | 4 | 14 |
| [lib/domain/entity/gapo/gp_comment.dart](/lib/domain/entity/gapo/gp_comment.dart) | Dart | 70 | 0 | 11 | 81 |
| [lib/domain/entity/gapo/gp_comment.g.dart](/lib/domain/entity/gapo/gp_comment.g.dart) | Dart | 85 | 4 | 15 | 104 |
| [lib/domain/entity/gapo/gp_group.dart](/lib/domain/entity/gapo/gp_group.dart) | Dart | 29 | 0 | 4 | 33 |
| [lib/domain/entity/gapo/gp_group.g.dart](/lib/domain/entity/gapo/gp_group.g.dart) | Dart | 39 | 4 | 7 | 50 |
| [lib/domain/entity/gapo/gp_message.dart](/lib/domain/entity/gapo/gp_message.dart) | Dart | 23 | 0 | 5 | 28 |
| [lib/domain/entity/gapo/gp_message.g.dart](/lib/domain/entity/gapo/gp_message.g.dart) | Dart | 23 | 4 | 6 | 33 |
| [lib/domain/entity/gapo/gp_post.dart](/lib/domain/entity/gapo/gp_post.dart) | Dart | 121 | 0 | 10 | 131 |
| [lib/domain/entity/gapo/gp_post.g.dart](/lib/domain/entity/gapo/gp_post.g.dart) | Dart | 140 | 4 | 16 | 160 |
| [lib/domain/entity/gapo/gp_thread.dart](/lib/domain/entity/gapo/gp_thread.dart) | Dart | 21 | 0 | 5 | 26 |
| [lib/domain/entity/gapo/gp_thread.g.dart](/lib/domain/entity/gapo/gp_thread.g.dart) | Dart | 21 | 4 | 6 | 31 |
| [lib/domain/entity/gapo/gpuser.dart](/lib/domain/entity/gapo/gpuser.dart) | Dart | 21 | 0 | 7 | 28 |
| [lib/domain/entity/gapo/gpuser.g.dart](/lib/domain/entity/gapo/gpuser.g.dart) | Dart | 19 | 4 | 5 | 28 |
| [lib/domain/entity/gapo/upload/callback/callback.dart](/lib/domain/entity/gapo/upload/callback/callback.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/gapo/upload/callback/gp_upload_callback.dart](/lib/domain/entity/gapo/upload/callback/gp_upload_callback.dart) | Dart | 46 | 8 | 13 | 67 |
| [lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart](/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart) | Dart | 17 | 12 | 5 | 34 |
| [lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.freezed.dart](/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.freezed.dart) | Dart | 123 | 15 | 23 | 161 |
| [lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.dart](/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.dart) | Dart | 13 | 14 | 7 | 34 |
| [lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.freezed.dart](/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.freezed.dart) | Dart | 183 | 27 | 37 | 247 |
| [lib/domain/entity/gapo/upload/params/gp_upload_params.dart](/lib/domain/entity/gapo/upload/params/gp_upload_params.dart) | Dart | 25 | 2 | 6 | 33 |
| [lib/domain/entity/gapo/upload/params/gp_upload_repo_params.dart](/lib/domain/entity/gapo/upload/params/gp_upload_repo_params.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/domain/entity/gapo/upload/params/params.dart](/lib/domain/entity/gapo/upload/params/params.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/gapo/upload/upload.dart](/lib/domain/entity/gapo/upload/upload.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/workplace/feed/feed.dart](/lib/domain/entity/workplace/feed/feed.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/workplace/feed/workplace_base_feed.entity.dart](/lib/domain/entity/workplace/feed/workplace_base_feed.entity.dart) | Dart | 45 | 10 | 15 | 70 |
| [lib/domain/entity/workplace/feed/workplace_base_feed.entity.g.dart](/lib/domain/entity/workplace/feed/workplace_base_feed.entity.g.dart) | Dart | 1,700 | 6 | 171 | 1,877 |
| [lib/domain/entity/workplace/feed/workplace_group_feed.entity.dart](/lib/domain/entity/workplace/feed/workplace_group_feed.entity.dart) | Dart | 0 | 27 | 6 | 33 |
| [lib/domain/entity/workplace/feed/workplace_user_feed.entity.dart](/lib/domain/entity/workplace/feed/workplace_user_feed.entity.dart) | Dart | 0 | 28 | 7 | 35 |
| [lib/domain/entity/workplace/group/group.dart](/lib/domain/entity/workplace/group/group.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/workplace/group/workplace_group.entity.dart](/lib/domain/entity/workplace/group/workplace_group.entity.dart) | Dart | 52 | 9 | 10 | 71 |
| [lib/domain/entity/workplace/group/workplace_group.entity.g.dart](/lib/domain/entity/workplace/group/workplace_group.entity.g.dart) | Dart | 2,573 | 11 | 245 | 2,829 |
| [lib/domain/entity/workplace/other/other.dart](/lib/domain/entity/workplace/other/other.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/workplace/other/workplace_attachment.entity.dart](/lib/domain/entity/workplace/other/workplace_attachment.entity.dart) | Dart | 69 | 59 | 35 | 163 |
| [lib/domain/entity/workplace/other/workplace_attachment.entity.g.dart](/lib/domain/entity/workplace/other/workplace_attachment.entity.g.dart) | Dart | 2,471 | 15 | 220 | 2,706 |
| [lib/domain/entity/workplace/other/workplace_comment.entity.dart](/lib/domain/entity/workplace/other/workplace_comment.entity.dart) | Dart | 65 | 9 | 17 | 91 |
| [lib/domain/entity/workplace/other/workplace_comment.entity.g.dart](/lib/domain/entity/workplace/other/workplace_comment.entity.g.dart) | Dart | 2,600 | 13 | 224 | 2,837 |
| [lib/domain/entity/workplace/thread/thread.dart](/lib/domain/entity/workplace/thread/thread.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.dart](/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.dart) | Dart | 61 | 9 | 13 | 83 |
| [lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.g.dart](/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.g.dart) | Dart | 2,907 | 13 | 247 | 3,167 |
| [lib/domain/entity/workplace/thread/workplace_conversations.entity.dart](/lib/domain/entity/workplace/thread/workplace_conversations.entity.dart) | Dart | 53 | 34 | 21 | 108 |
| [lib/domain/entity/workplace/thread/workplace_conversations.entity.g.dart](/lib/domain/entity/workplace/thread/workplace_conversations.entity.g.dart) | Dart | 2,309 | 8 | 228 | 2,545 |
| [lib/domain/entity/workplace/user/user.dart](/lib/domain/entity/workplace/user/user.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/workplace/user/workplace_community_member.entity.dart](/lib/domain/entity/workplace/user/workplace_community_member.entity.dart) | Dart | 32 | 12 | 11 | 55 |
| [lib/domain/entity/workplace/user/workplace_community_member.entity.g.dart](/lib/domain/entity/workplace/user/workplace_community_member.entity.g.dart) | Dart | 2,237 | 6 | 219 | 2,462 |
| [lib/domain/entity/workplace/workplace.dart](/lib/domain/entity/workplace/workplace.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/domain/repository/download_repo.dart](/lib/domain/repository/download_repo.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/repository/facebook_repo.dart](/lib/domain/repository/facebook_repo.dart) | Dart | 4 | 1 | 2 | 7 |
| [lib/domain/repository/gapo_repo.dart](/lib/domain/repository/gapo_repo.dart) | Dart | 15 | 10 | 6 | 31 |
| [lib/domain/repository/repository.dart](/lib/domain/repository/repository.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/repository/workplace_repo.dart](/lib/domain/repository/workplace_repo.dart) | Dart | 22 | 11 | 12 | 45 |
| [lib/domain/usecase/download_file.usecase.dart](/lib/domain/usecase/download_file.usecase.dart) | Dart | 31 | 0 | 9 | 40 |
| [lib/domain/usecase/facebook_get_community.usecase.dart](/lib/domain/usecase/facebook_get_community.usecase.dart) | Dart | 19 | 10 | 7 | 36 |
| [lib/domain/usecase/feed_attachment.usecase.dart](/lib/domain/usecase/feed_attachment.usecase.dart) | Dart | 0 | 89 | 12 | 101 |
| [lib/domain/usecase/gapo/gapo.dart](/lib/domain/usecase/gapo/gapo.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/domain/usecase/gapo/gapo_auth.usecase.dart](/lib/domain/usecase/gapo/gapo_auth.usecase.dart) | Dart | 47 | 10 | 14 | 71 |
| [lib/domain/usecase/gapo/gapo_auth_check_mail.usecase.dart](/lib/domain/usecase/gapo/gapo_auth_check_mail.usecase.dart) | Dart | 17 | 9 | 5 | 31 |
| [lib/domain/usecase/gapo/gapo_create_user.usecase.dart](/lib/domain/usecase/gapo/gapo_create_user.usecase.dart) | Dart | 31 | 9 | 7 | 47 |
| [lib/domain/usecase/gapo/gapo_login.usecase.dart](/lib/domain/usecase/gapo/gapo_login.usecase.dart) | Dart | 74 | 9 | 15 | 98 |
| [lib/domain/usecase/gapo/gapo_upload.usecase.dart](/lib/domain/usecase/gapo/gapo_upload.usecase.dart) | Dart | 41 | 10 | 13 | 64 |
| [lib/domain/usecase/usecase.dart](/lib/domain/usecase/usecase.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/domain/usecase/workplace/workplace.dart](/lib/domain/usecase/workplace/workplace.dart) | Dart | 9 | 0 | 1 | 10 |
| [lib/domain/usecase/workplace/workplace_get_all_groups.usecase.dart](/lib/domain/usecase/workplace/workplace_get_all_groups.usecase.dart) | Dart | 38 | 10 | 9 | 57 |
| [lib/domain/usecase/workplace/workplace_get_app_config.usecase.dart](/lib/domain/usecase/workplace/workplace_get_app_config.usecase.dart) | Dart | 20 | 10 | 7 | 37 |
| [lib/domain/usecase/workplace/workplace_get_community_members.usecase.dart](/lib/domain/usecase/workplace/workplace_get_community_members.usecase.dart) | Dart | 37 | 10 | 9 | 56 |
| [lib/domain/usecase/workplace/workplace_get_conversations.usecase.dart](/lib/domain/usecase/workplace/workplace_get_conversations.usecase.dart) | Dart | 40 | 10 | 9 | 59 |
| [lib/domain/usecase/workplace/workplace_get_group_feeds.usecase.dart](/lib/domain/usecase/workplace/workplace_get_group_feeds.usecase.dart) | Dart | 40 | 10 | 9 | 59 |
| [lib/domain/usecase/workplace/workplace_get_group_members.usecase.dart](/lib/domain/usecase/workplace/workplace_get_group_members.usecase.dart) | Dart | 0 | 51 | 9 | 60 |
| [lib/domain/usecase/workplace/workplace_get_post_attachments.usecase.dart](/lib/domain/usecase/workplace/workplace_get_post_attachments.usecase.dart) | Dart | 27 | 10 | 8 | 45 |
| [lib/domain/usecase/workplace/workplace_get_post_comments.usecase.dart](/lib/domain/usecase/workplace/workplace_get_post_comments.usecase.dart) | Dart | 27 | 10 | 8 | 45 |
| [lib/domain/usecase/workplace/workplace_get_user_feeds.usecase.dart](/lib/domain/usecase/workplace/workplace_get_user_feeds.usecase.dart) | Dart | 0 | 37 | 8 | 45 |
| [lib/flutter_gen/assets.gen.dart](/lib/flutter_gen/assets.gen.dart) | Dart | 68 | 10 | 14 | 92 |
| [lib/flutter_gen/flutter_gen.dart](/lib/flutter_gen/flutter_gen.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/helpers/csv.dart](/lib/helpers/csv.dart) | Dart | 34 | 0 | 10 | 44 |
| [lib/helpers/file_helper.dart](/lib/helpers/file_helper.dart) | Dart | 45 | 0 | 11 | 56 |
| [lib/helpers/gp_upload_management.dart](/lib/helpers/gp_upload_management.dart) | Dart | 143 | 18 | 41 | 202 |
| [lib/helpers/helpers.dart](/lib/helpers/helpers.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/helpers/jwt_token_decode.dart](/lib/helpers/jwt_token_decode.dart) | Dart | 58 | 31 | 10 | 99 |
| [lib/l10n/app_en.arb](/lib/l10n/app_en.arb) | JSON | 4 | 0 | 0 | 4 |
| [lib/l10n/app_localizations.dart](/lib/l10n/app_localizations.dart) | Dart | 50 | 75 | 19 | 144 |
| [lib/l10n/app_localizations_en.dart](/lib/l10n/app_localizations_en.dart) | Dart | 13 | 5 | 7 | 25 |
| [lib/l10n/app_localizations_vi.dart](/lib/l10n/app_localizations_vi.dart) | Dart | 13 | 5 | 7 | 25 |
| [lib/l10n/app_vi.arb](/lib/l10n/app_vi.arb) | JSON | 17 | 0 | 0 | 17 |
| [lib/l10n/l10n.dart](/lib/l10n/l10n.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/main.production.dart](/lib/main.production.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/main.staging.dart](/lib/main.staging.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/main.uat.dart](/lib/main.uat.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/mapper/entity/entity.dart](/lib/mapper/entity/entity.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/mapper/entity/gapo_entity_mapper.auto_mappr.dart](/lib/mapper/entity/gapo_entity_mapper.auto_mappr.dart) | Dart | 488 | 47 | 37 | 572 |
| [lib/mapper/entity/gapo_entity_mapper.dart](/lib/mapper/entity/gapo_entity_mapper.dart) | Dart | 209 | 35 | 40 | 284 |
| [lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart](/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart) | Dart | 841 | 55 | 47 | 943 |
| [lib/mapper/entity/workplace_entity_mapper.dart](/lib/mapper/entity/workplace_entity_mapper.dart) | Dart | 226 | 23 | 35 | 284 |
| [lib/mapper/gp_mapper.auto_mappr.dart](/lib/mapper/gp_mapper.auto_mappr.dart) | Dart | 191 | 60 | 28 | 279 |
| [lib/mapper/gp_mapper.dart](/lib/mapper/gp_mapper.dart) | Dart | 62 | 13 | 10 | 85 |
| [lib/mapper/mapper.dart](/lib/mapper/mapper.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/route/go_router.route.dart](/lib/route/go_router.route.dart) | Dart | 40 | 10 | 10 | 60 |
| [lib/route/go_router.route.g.dart](/lib/route/go_router.route.g.dart) | Dart | 53 | 4 | 25 | 82 |
| [lib/route/route.dart](/lib/route/route.dart) | Dart | 1 | 0 | 1 | 2 |
| [linux/main.cc](/linux/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [linux/my_application.cc](/linux/my_application.cc) | C++ | 74 | 11 | 20 | 105 |
| [linux/my_application.h](/linux/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [macos/RunnerTests/RunnerTests.swift](/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [macos/Runner/AppDelegate.swift](/macos/Runner/AppDelegate.swift) | Swift | 8 | 0 | 2 | 10 |
| [macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 0 | 68 |
| [macos/Runner/Base.lproj/MainMenu.xib](/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [macos/Runner/MainFlutterWindow.swift](/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [project_configs/flutter_launcher_icons-dev.yaml](/project_configs/flutter_launcher_icons-dev.yaml) | YAML | 17 | 0 | 0 | 17 |
| [project_configs/flutter_launcher_icons-prod.yaml](/project_configs/flutter_launcher_icons-prod.yaml) | YAML | 17 | 0 | 0 | 17 |
| [project_configs/flutter_launcher_icons-uat.yaml](/project_configs/flutter_launcher_icons-uat.yaml) | YAML | 17 | 0 | 0 | 17 |
| [project_configs/package_rename_config-dev.yaml](/project_configs/package_rename_config-dev.yaml) | YAML | 27 | 0 | 6 | 33 |
| [project_configs/package_rename_config-prod.yaml](/project_configs/package_rename_config-prod.yaml) | YAML | 27 | 0 | 6 | 33 |
| [project_configs/package_rename_config-uat.yaml](/project_configs/package_rename_config-uat.yaml) | YAML | 27 | 0 | 6 | 33 |
| [pubspec.yaml](/pubspec.yaml) | YAML | 69 | 6 | 1 | 76 |
| [test/widget_test.dart](/test/widget_test.dart) | Dart | 14 | 10 | 6 | 30 |
| [web/index.html](/web/index.html) | HTML | 35 | 16 | 17 | 68 |
| [web/manifest.json](/web/manifest.json) | JSON | 35 | 0 | 1 | 36 |
| [windows/runner/flutter_window.cpp](/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [windows/runner/flutter_window.h](/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [windows/runner/main.cpp](/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [windows/runner/resource.h](/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [windows/runner/utils.cpp](/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [windows/runner/utils.h](/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [windows/runner/win32_window.cpp](/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [windows/runner/win32_window.h](/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details