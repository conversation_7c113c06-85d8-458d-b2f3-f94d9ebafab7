# Diff Summary

Date : 2024-06-23 19:45:01

Directory c:\\Softwares\\Gapo\\gapoflutter-crawler

Total : 657 files,  -1935 codes, 1163 comments, 212 blanks, all -560 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| YAML | 18 | 1 | 0 | 0 | 1 |
| JSON | 22 | 0 | 0 | 0 | 0 |
| HTML | 2 | 0 | 0 | 0 | 0 |
| Swift | 10 | 0 | 0 | 0 | 0 |
| Markdown | 4 | 0 | 0 | 0 | 0 |
| C++ | 24 | 0 | 0 | 0 | 0 |
| XML | 18 | 0 | 0 | 0 | 0 |
| Groovy | 6 | 0 | 0 | 0 | 0 |
| Java Properties | 4 | 0 | 0 | 0 | 0 |
| Kotlin | 2 | 0 | 0 | 0 | 0 |
| Dart | 547 | -1,936 | 1,163 | 212 | -561 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 657 | -1,935 | 1,163 | 212 | -560 |
| . (Files) | 4 | 123 | 24 | 16 | 163 |
| .. | 311 | -34,417 | -1,560 | -4,206 | -40,183 |
| ..\\.. | 311 | -34,417 | -1,560 | -4,206 | -40,183 |
| ..\\..\\.. | 311 | -34,417 | -1,560 | -4,206 | -40,183 |
| ..\\..\\..\\Users | 311 | -34,417 | -1,560 | -4,206 | -40,183 |
| ..\\..\\..\\Users\\toannm | 311 | -34,417 | -1,560 | -4,206 | -40,183 |
| ..\\..\\..\\Users\\toannm\\Softwares | 311 | -34,417 | -1,560 | -4,206 | -40,183 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages | 311 | -34,417 | -1,560 | -4,206 | -40,183 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler | 311 | -34,417 | -1,560 | -4,206 | -40,183 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler (Files) | 4 | -122 | -24 | -16 | -162 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android | 12 | -171 | -47 | -33 | -251 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android (Files) | 3 | -46 | 0 | -11 | -57 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app | 8 | -120 | -47 | -21 | -188 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app (Files) | 1 | -51 | -5 | -12 | -68 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src | 7 | -69 | -42 | -9 | -120 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\debug | 1 | -7 | -4 | 0 | -11 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main | 6 | -62 | -38 | -9 | -109 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main (Files) | 1 | -32 | -6 | 0 | -38 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main\\kotlin | 1 | -4 | 0 | -3 | -7 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main\\kotlin\\vn | 1 | -4 | 0 | -3 | -7 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main\\kotlin\\vn\\gapowork | 1 | -4 | 0 | -3 | -7 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main\\kotlin\\vn\\gapowork\\crawler | 1 | -4 | 0 | -3 | -7 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main\\res | 4 | -26 | -32 | -6 | -64 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main\\res\\drawable | 1 | -4 | -7 | -2 | -13 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main\\res\\drawable-v21 | 1 | -4 | -7 | -2 | -13 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main\\res\\values | 1 | -9 | -9 | -1 | -19 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\app\\src\\main\\res\\values-night | 1 | -9 | -9 | -1 | -19 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\gradle | 1 | -5 | 0 | -1 | -6 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\android\\gradle\\wrapper | 1 | -5 | 0 | -1 | -6 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\assets | 2 | -2 | 0 | 0 | -2 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\assets\\images | 2 | -2 | 0 | 0 | -2 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios | 11 | -232 | -4 | -13 | -249 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\Runner | 10 | -225 | -2 | -9 | -236 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\Runner (Files) | 2 | -13 | 0 | -3 | -16 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\RunnerTests | 1 | -7 | -2 | -4 | -13 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\Runner\\Assets.xcassets | 6 | -151 | 0 | -4 | -155 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\Runner\\Assets.xcassets\\AppIcon-dev.appiconset | 1 | -1 | 0 | 0 | -1 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\Runner\\Assets.xcassets\\AppIcon-prod.appiconset | 1 | -1 | 0 | 0 | -1 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\Runner\\Assets.xcassets\\AppIcon-uat.appiconset | 1 | -1 | 0 | 0 | -1 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\Runner\\Assets.xcassets\\AppIcon.appiconset | 1 | -122 | 0 | -1 | -123 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\Runner\\Assets.xcassets\\LaunchImage.imageset | 2 | -26 | 0 | -3 | -29 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\ios\\Runner\\Base.lproj | 2 | -61 | -2 | -2 | -65 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib | 257 | -32,722 | -1,354 | -3,932 | -38,008 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib (Files) | 3 | -24 | 0 | -9 | -33 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app | 45 | -2,398 | -156 | -412 | -2,966 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app (Files) | 4 | -346 | -6 | -55 | -407 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\app_config | 8 | -247 | -17 | -52 | -316 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\app_config (Files) | 1 | -2 | 0 | -1 | -3 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\app_config\\bloc | 5 | -236 | -17 | -47 | -300 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\app_config\\widgets | 2 | -9 | 0 | -4 | -13 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\base | 7 | -256 | -46 | -73 | -375 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\base (Files) | 1 | -1 | 0 | -1 | -2 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\base\\networking | 6 | -255 | -46 | -72 | -373 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\base\\networking (Files) | 3 | -107 | -7 | -27 | -141 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\base\\networking\\gapo | 3 | -148 | -39 | -45 | -232 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\constant | 4 | -40 | -24 | -13 | -77 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features | 20 | -1,480 | -63 | -215 | -1,758 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features (Files) | 1 | -1 | 0 | -1 | -2 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features\\crawler | 12 | -1,230 | -46 | -174 | -1,450 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features\\crawler (Files) | 3 | -95 | 0 | -10 | -105 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features\\crawler\\bloc | 5 | -1,133 | -46 | -158 | -1,337 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features\\crawler\\sync | 2 | -1 | 0 | -3 | -4 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features\\crawler\\unsync | 2 | -1 | 0 | -3 | -4 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features\\home | 7 | -249 | -17 | -40 | -306 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features\\home (Files) | 2 | -100 | -2 | -6 | -108 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\features\\home\\bloc | 5 | -149 | -15 | -34 | -198 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\app\\splash | 2 | -29 | 0 | -4 | -33 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\config | 3 | -49 | 0 | -17 | -66 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data | 77 | -2,680 | -232 | -494 | -3,406 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data (Files) | 1 | -3 | 0 | -1 | -4 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\data_source | 15 | -910 | -74 | -121 | -1,105 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\data_source (Files) | 1 | -2 | 0 | -1 | -3 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\data_source\\local | 2 | -46 | -9 | -14 | -69 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\data_source\\remote | 12 | -862 | -65 | -106 | -1,033 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\data_source\\remote (Files) | 5 | -466 | -30 | -54 | -550 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\data_source\\remote\\download | 2 | -33 | 0 | -5 | -38 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\data_source\\remote\\gapo | 5 | -363 | -35 | -47 | -445 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model | 56 | -1,594 | -138 | -339 | -2,071 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model (Files) | 2 | -16 | 0 | -4 | -20 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\download | 3 | -132 | -15 | -26 | -173 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\facebook | 3 | -25 | -4 | -10 | -39 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\gpw | 17 | -602 | -70 | -108 | -780 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\gpw (Files) | 4 | -44 | -4 | -17 | -65 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\gpw\\auth | 13 | -558 | -66 | -91 | -715 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\gpw\\auth (Files) | 1 | -2 | 0 | -1 | -3 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\gpw\\auth\\request | 9 | -522 | -53 | -76 | -651 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\gpw\\auth\\response | 3 | -34 | -13 | -14 | -61 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\workplace | 31 | -819 | -49 | -191 | -1,059 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\workplace (Files) | 1 | -6 | 0 | -1 | -7 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\workplace\\base | 8 | -176 | -15 | -45 | -236 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\workplace\\comment | 5 | -93 | -8 | -25 | -126 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\workplace\\conversation | 5 | -167 | -8 | -38 | -213 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\workplace\\enums | 2 | -49 | 0 | -17 | -66 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\workplace\\group | 5 | -155 | -10 | -31 | -196 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\model\\workplace\\post | 5 | -173 | -8 | -34 | -215 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\data\\repository | 5 | -173 | -20 | -33 | -226 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\di | 11 | -517 | -78 | -63 | -658 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\di (Files) | 1 | -2 | 0 | -1 | -3 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\di\\component | 3 | -354 | -17 | -18 | -389 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\di\\modules | 7 | -161 | -61 | -44 | -266 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain | 94 | -24,475 | -524 | -2,603 | -27,602 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain (Files) | 1 | -3 | 0 | -1 | -4 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity | 68 | -23,788 | -387 | -2,424 | -26,599 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity (Files) | 1 | -4 | 0 | -1 | -5 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\base | 12 | -2,883 | -60 | -294 | -3,237 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\base (Files) | 2 | -100 | -10 | -18 | -128 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\base\\app | 4 | -741 | -15 | -87 | -843 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\base\\log | 3 | -1,035 | -15 | -97 | -1,147 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\base\\status | 3 | -1,007 | -20 | -92 | -1,119 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\enums | 6 | -132 | -47 | -39 | -218 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\enums (Files) | 4 | -119 | -47 | -33 | -199 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\enums\\upload | 2 | -13 | 0 | -6 | -19 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\gapo | 25 | -1,061 | -102 | -201 | -1,364 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\gapo (Files) | 15 | -631 | -24 | -103 | -758 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\gapo\\upload | 10 | -430 | -78 | -98 | -606 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\gapo\\upload (Files) | 3 | -199 | -41 | -45 | -285 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\gapo\\upload\\callback | 4 | -188 | -35 | -42 | -265 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\gapo\\upload\\params | 3 | -43 | -2 | -11 | -56 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\workplace | 24 | -19,708 | -178 | -1,889 | -21,775 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\workplace (Files) | 1 | -5 | 0 | -1 | -6 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\workplace\\feed | 7 | -3,134 | -46 | -332 | -3,512 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\workplace\\group | 3 | -2,628 | -20 | -256 | -2,904 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\workplace\\other | 5 | -5,723 | -52 | -503 | -6,278 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\workplace\\thread | 5 | -5,946 | -42 | -566 | -6,554 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\entity\\workplace\\user | 3 | -2,272 | -18 | -231 | -2,521 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\repository | 5 | -49 | -22 | -22 | -93 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\usecase | 20 | -635 | -115 | -156 | -906 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\usecase (Files) | 14 | -435 | -77 | -103 | -615 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\domain\\usecase\\gapo | 6 | -200 | -38 | -53 | -291 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\flutter_gen | 2 | -69 | -10 | -15 | -94 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\helpers | 5 | -273 | -49 | -66 | -388 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\l10n | 6 | -100 | -85 | -34 | -219 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\mapper | 8 | -2,042 | -206 | -183 | -2,431 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\mapper (Files) | 3 | -256 | -73 | -39 | -368 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\mapper\\entity | 5 | -1,786 | -133 | -144 | -2,063 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\lib\\route | 3 | -95 | -14 | -36 | -145 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\linux | 3 | -86 | -18 | -27 | -131 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\macos | 5 | -438 | -2 | -11 | -451 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\macos\\Runner | 4 | -431 | 0 | -7 | -438 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\macos\\Runner (Files) | 2 | -20 | 0 | -6 | -26 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\macos\\RunnerTests | 1 | -7 | -2 | -4 | -13 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\macos\\Runner\\Assets.xcassets | 1 | -68 | 0 | 0 | -68 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\macos\\Runner\\Assets.xcassets\\AppIcon.appiconset | 1 | -68 | 0 | 0 | -68 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\macos\\Runner\\Base.lproj | 1 | -343 | 0 | -1 | -344 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\project_configs | 6 | -132 | 0 | -18 | -150 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\test | 1 | -14 | -10 | -6 | -30 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\web | 2 | -70 | -16 | -18 | -104 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\windows | 8 | -428 | -85 | -132 | -645 |
| ..\\..\\..\\Users\\toannm\\Softwares\\flutter.packages\\gapoflutter-crawler\\windows\\runner | 8 | -428 | -85 | -132 | -645 |
| android | 12 | 171 | 47 | 33 | 251 |
| android (Files) | 3 | 46 | 0 | 11 | 57 |
| android\\app | 8 | 120 | 47 | 21 | 188 |
| android\\app (Files) | 1 | 51 | 5 | 12 | 68 |
| android\\app\\src | 7 | 69 | 42 | 9 | 120 |
| android\\app\\src\\debug | 1 | 7 | 4 | 0 | 11 |
| android\\app\\src\\main | 6 | 62 | 38 | 9 | 109 |
| android\\app\\src\\main (Files) | 1 | 32 | 6 | 0 | 38 |
| android\\app\\src\\main\\kotlin | 1 | 4 | 0 | 3 | 7 |
| android\\app\\src\\main\\kotlin\\vn | 1 | 4 | 0 | 3 | 7 |
| android\\app\\src\\main\\kotlin\\vn\\gapowork | 1 | 4 | 0 | 3 | 7 |
| android\\app\\src\\main\\kotlin\\vn\\gapowork\\crawler | 1 | 4 | 0 | 3 | 7 |
| android\\app\\src\\main\\res | 4 | 26 | 32 | 6 | 64 |
| android\\app\\src\\main\\res\\drawable | 1 | 4 | 7 | 2 | 13 |
| android\\app\\src\\main\\res\\drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| android\\app\\src\\main\\res\\values | 1 | 9 | 9 | 1 | 19 |
| android\\app\\src\\main\\res\\values-night | 1 | 9 | 9 | 1 | 19 |
| android\\gradle | 1 | 5 | 0 | 1 | 6 |
| android\\gradle\\wrapper | 1 | 5 | 0 | 1 | 6 |
| assets | 2 | 2 | 0 | 0 | 2 |
| assets\\images | 2 | 2 | 0 | 0 | 2 |
| ios | 11 | 232 | 4 | 13 | 249 |
| ios\\Runner | 10 | 225 | 2 | 9 | 236 |
| ios\\Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| ios\\RunnerTests | 1 | 7 | 2 | 4 | 13 |
| ios\\Runner\\Assets.xcassets | 6 | 151 | 0 | 4 | 155 |
| ios\\Runner\\Assets.xcassets\\AppIcon-dev.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios\\Runner\\Assets.xcassets\\AppIcon-prod.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios\\Runner\\Assets.xcassets\\AppIcon-uat.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios\\Runner\\Assets.xcassets\\AppIcon.appiconset | 1 | 122 | 0 | 1 | 123 |
| ios\\Runner\\Assets.xcassets\\LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| ios\\Runner\\Base.lproj | 2 | 61 | 2 | 2 | 65 |
| lib | 292 | 30,786 | 2,517 | 4,144 | 37,447 |
| lib (Files) | 3 | 24 | 0 | 9 | 33 |
| lib\\app | 82 | 2,926 | 1,003 | 763 | 4,692 |
| lib\\app (Files) | 4 | 346 | 6 | 55 | 407 |
| lib\\app\\app_config | 8 | 258 | 16 | 55 | 329 |
| lib\\app\\app_config (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\app\\app_config\\bloc | 5 | 247 | 16 | 50 | 313 |
| lib\\app\\app_config\\widgets | 2 | 9 | 0 | 4 | 13 |
| lib\\app\\base | 9 | 261 | 51 | 77 | 389 |
| lib\\app\\base (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\base\\networking | 8 | 260 | 51 | 76 | 387 |
| lib\\app\\base\\networking (Files) | 3 | 108 | 7 | 27 | 142 |
| lib\\app\\base\\networking\\exception | 2 | 4 | 5 | 4 | 13 |
| lib\\app\\base\\networking\\gapo | 3 | 148 | 39 | 45 | 232 |
| lib\\app\\constant | 4 | 40 | 24 | 13 | 77 |
| lib\\app\\features | 55 | 1,992 | 906 | 559 | 3,457 |
| lib\\app\\features (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\app\\features\\crawler | 47 | 1,765 | 887 | 518 | 3,170 |
| lib\\app\\features\\crawler (Files) | 3 | 100 | 0 | 10 | 110 |
| lib\\app\\features\\crawler\\base_crawl_bloc | 8 | 84 | 2 | 22 | 108 |
| lib\\app\\features\\crawler\\base_crawl_bloc (Files) | 2 | 23 | 0 | 5 | 28 |
| lib\\app\\features\\crawler\\base_crawl_bloc\\bloc | 4 | 52 | 2 | 13 | 67 |
| lib\\app\\features\\crawler\\base_crawl_bloc\\widgets | 2 | 9 | 0 | 4 | 13 |
| lib\\app\\features\\crawler\\bloc | 6 | 643 | 778 | 193 | 1,614 |
| lib\\app\\features\\crawler\\crawl_feed | 6 | 272 | 31 | 92 | 395 |
| lib\\app\\features\\crawler\\crawl_feed (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\features\\crawler\\crawl_feed\\bloc | 5 | 271 | 31 | 91 | 393 |
| lib\\app\\features\\crawler\\crawl_group | 6 | 188 | 21 | 57 | 266 |
| lib\\app\\features\\crawler\\crawl_group (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\features\\crawler\\crawl_group\\bloc | 5 | 187 | 21 | 56 | 264 |
| lib\\app\\features\\crawler\\crawl_member | 6 | 183 | 23 | 55 | 261 |
| lib\\app\\features\\crawler\\crawl_member (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\features\\crawler\\crawl_member\\bloc | 5 | 182 | 23 | 54 | 259 |
| lib\\app\\features\\crawler\\crawl_thread | 6 | 198 | 31 | 61 | 290 |
| lib\\app\\features\\crawler\\crawl_thread (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\features\\crawler\\crawl_thread\\bloc | 5 | 197 | 31 | 60 | 288 |
| lib\\app\\features\\crawler\\mixin | 2 | 95 | 1 | 22 | 118 |
| lib\\app\\features\\crawler\\sync | 2 | 1 | 0 | 3 | 4 |
| lib\\app\\features\\crawler\\unsync | 2 | 1 | 0 | 3 | 4 |
| lib\\app\\features\\home | 7 | 225 | 19 | 40 | 284 |
| lib\\app\\features\\home (Files) | 2 | 104 | 4 | 9 | 117 |
| lib\\app\\features\\home\\bloc | 5 | 121 | 15 | 31 | 167 |
| lib\\app\\splash | 2 | 29 | 0 | 4 | 33 |
| lib\\config | 3 | 50 | 0 | 18 | 68 |
| lib\\data | 77 | 2,840 | 232 | 517 | 3,589 |
| lib\\data (Files) | 1 | 3 | 0 | 1 | 4 |
| lib\\data\\data_source | 15 | 1,045 | 74 | 140 | 1,259 |
| lib\\data\\data_source (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\data\\data_source\\local | 2 | 181 | 9 | 33 | 223 |
| lib\\data\\data_source\\remote | 12 | 862 | 65 | 106 | 1,033 |
| lib\\data\\data_source\\remote (Files) | 5 | 466 | 30 | 54 | 550 |
| lib\\data\\data_source\\remote\\download | 2 | 33 | 0 | 5 | 38 |
| lib\\data\\data_source\\remote\\gapo | 5 | 363 | 35 | 47 | 445 |
| lib\\data\\model | 56 | 1,619 | 138 | 343 | 2,100 |
| lib\\data\\model (Files) | 2 | 16 | 0 | 4 | 20 |
| lib\\data\\model\\download | 3 | 132 | 15 | 26 | 173 |
| lib\\data\\model\\facebook | 3 | 25 | 4 | 10 | 39 |
| lib\\data\\model\\gpw | 17 | 602 | 70 | 108 | 780 |
| lib\\data\\model\\gpw (Files) | 4 | 44 | 4 | 17 | 65 |
| lib\\data\\model\\gpw\\auth | 13 | 558 | 66 | 91 | 715 |
| lib\\data\\model\\gpw\\auth (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\data\\model\\gpw\\auth\\request | 9 | 522 | 53 | 76 | 651 |
| lib\\data\\model\\gpw\\auth\\response | 3 | 34 | 13 | 14 | 61 |
| lib\\data\\model\\workplace | 31 | 844 | 49 | 195 | 1,088 |
| lib\\data\\model\\workplace (Files) | 1 | 6 | 0 | 1 | 7 |
| lib\\data\\model\\workplace\\base | 8 | 180 | 15 | 45 | 240 |
| lib\\data\\model\\workplace\\comment | 5 | 93 | 8 | 25 | 126 |
| lib\\data\\model\\workplace\\conversation | 5 | 167 | 8 | 38 | 213 |
| lib\\data\\model\\workplace\\enums | 2 | 54 | 0 | 18 | 72 |
| lib\\data\\model\\workplace\\group | 5 | 155 | 10 | 31 | 196 |
| lib\\data\\model\\workplace\\post | 5 | 189 | 8 | 37 | 234 |
| lib\\data\\repository | 5 | 173 | 20 | 33 | 226 |
| lib\\di | 11 | 502 | 88 | 63 | 653 |
| lib\\di (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\di\\component | 3 | 336 | 17 | 18 | 371 |
| lib\\di\\modules | 7 | 164 | 71 | 44 | 279 |
| lib\\domain | 92 | 21,873 | 803 | 2,417 | 25,093 |
| lib\\domain (Files) | 1 | 3 | 0 | 1 | 4 |
| lib\\domain\\entity | 66 | 21,313 | 477 | 2,233 | 24,023 |
| lib\\domain\\entity (Files) | 1 | 4 | 0 | 1 | 5 |
| lib\\domain\\entity\\base | 12 | 2,929 | 60 | 298 | 3,287 |
| lib\\domain\\entity\\base (Files) | 2 | 148 | 10 | 22 | 180 |
| lib\\domain\\entity\\base\\app | 4 | 741 | 15 | 87 | 843 |
| lib\\domain\\entity\\base\\log | 3 | 1,033 | 15 | 97 | 1,145 |
| lib\\domain\\entity\\base\\status | 3 | 1,007 | 20 | 92 | 1,119 |
| lib\\domain\\entity\\enums | 6 | 131 | 46 | 38 | 215 |
| lib\\domain\\entity\\enums (Files) | 4 | 118 | 46 | 32 | 196 |
| lib\\domain\\entity\\enums\\upload | 2 | 13 | 0 | 6 | 19 |
| lib\\domain\\entity\\gapo | 25 | 1,061 | 102 | 201 | 1,364 |
| lib\\domain\\entity\\gapo (Files) | 15 | 631 | 24 | 103 | 758 |
| lib\\domain\\entity\\gapo\\upload | 10 | 430 | 78 | 98 | 606 |
| lib\\domain\\entity\\gapo\\upload (Files) | 3 | 199 | 41 | 45 | 285 |
| lib\\domain\\entity\\gapo\\upload\\callback | 4 | 188 | 35 | 42 | 265 |
| lib\\domain\\entity\\gapo\\upload\\params | 3 | 43 | 2 | 11 | 56 |
| lib\\domain\\entity\\workplace | 22 | 17,188 | 269 | 1,695 | 19,152 |
| lib\\domain\\entity\\workplace (Files) | 1 | 5 | 0 | 1 | 6 |
| lib\\domain\\entity\\workplace\\feed | 5 | 1,748 | 71 | 200 | 2,019 |
| lib\\domain\\entity\\workplace\\group | 3 | 2,626 | 20 | 256 | 2,902 |
| lib\\domain\\entity\\workplace\\other | 5 | 5,207 | 96 | 497 | 5,800 |
| lib\\domain\\entity\\workplace\\thread | 5 | 5,332 | 64 | 510 | 5,906 |
| lib\\domain\\entity\\workplace\\user | 3 | 2,270 | 18 | 231 | 2,519 |
| lib\\domain\\repository | 5 | 49 | 22 | 22 | 93 |
| lib\\domain\\usecase | 20 | 508 | 304 | 161 | 973 |
| lib\\domain\\usecase (Files) | 4 | 55 | 99 | 29 | 183 |
| lib\\domain\\usecase\\gapo | 6 | 215 | 47 | 55 | 317 |
| lib\\domain\\usecase\\workplace | 10 | 238 | 158 | 77 | 473 |
| lib\\flutter_gen | 2 | 69 | 10 | 15 | 94 |
| lib\\helpers | 5 | 284 | 49 | 73 | 406 |
| lib\\l10n | 6 | 100 | 85 | 34 | 219 |
| lib\\mapper | 8 | 2,024 | 233 | 199 | 2,456 |
| lib\\mapper (Files) | 3 | 256 | 73 | 39 | 368 |
| lib\\mapper\\entity | 5 | 1,768 | 160 | 160 | 2,088 |
| lib\\route | 3 | 94 | 14 | 36 | 144 |
| linux | 3 | 86 | 18 | 27 | 131 |
| macos | 5 | 438 | 2 | 11 | 451 |
| macos\\Runner | 4 | 431 | 0 | 7 | 438 |
| macos\\Runner (Files) | 2 | 20 | 0 | 6 | 26 |
| macos\\RunnerTests | 1 | 7 | 2 | 4 | 13 |
| macos\\Runner\\Assets.xcassets | 1 | 68 | 0 | 0 | 68 |
| macos\\Runner\\Assets.xcassets\\AppIcon.appiconset | 1 | 68 | 0 | 0 | 68 |
| macos\\Runner\\Base.lproj | 1 | 343 | 0 | 1 | 344 |
| project_configs | 6 | 132 | 0 | 18 | 150 |
| test | 1 | 14 | 10 | 6 | 30 |
| web | 2 | 70 | 16 | 18 | 104 |
| windows | 8 | 428 | 85 | 132 | 645 |
| windows\\runner | 8 | 428 | 85 | 132 | 645 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)