# Summary

Date : 2024-06-23 19:45:01

Directory c:\\Softwares\\Gapo\\gapoflutter-crawler

Total : 346 files,  32482 codes, 2723 comments, 4418 blanks, all 39623 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 291 | 30,779 | 2,527 | 4,150 | 37,456 |
| C++ | 12 | 515 | 103 | 160 | 778 |
| XML | 9 | 469 | 44 | 9 | 522 |
| JSON | 11 | 274 | 0 | 3 | 277 |
| YAML | 9 | 215 | 24 | 22 | 261 |
| Groovy | 3 | 94 | 5 | 22 | 121 |
| Swift | 5 | 46 | 4 | 16 | 66 |
| Markdown | 2 | 43 | 0 | 14 | 57 |
| HTML | 1 | 35 | 16 | 17 | 68 |
| Java Properties | 2 | 8 | 0 | 2 | 10 |
| Kotlin | 1 | 4 | 0 | 3 | 7 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 346 | 32,482 | 2,723 | 4,418 | 39,623 |
| . (Files) | 4 | 123 | 24 | 16 | 163 |
| android | 12 | 171 | 47 | 33 | 251 |
| android (Files) | 3 | 46 | 0 | 11 | 57 |
| android\\app | 8 | 120 | 47 | 21 | 188 |
| android\\app (Files) | 1 | 51 | 5 | 12 | 68 |
| android\\app\\src | 7 | 69 | 42 | 9 | 120 |
| android\\app\\src\\debug | 1 | 7 | 4 | 0 | 11 |
| android\\app\\src\\main | 6 | 62 | 38 | 9 | 109 |
| android\\app\\src\\main (Files) | 1 | 32 | 6 | 0 | 38 |
| android\\app\\src\\main\\kotlin | 1 | 4 | 0 | 3 | 7 |
| android\\app\\src\\main\\kotlin\\vn | 1 | 4 | 0 | 3 | 7 |
| android\\app\\src\\main\\kotlin\\vn\\gapowork | 1 | 4 | 0 | 3 | 7 |
| android\\app\\src\\main\\kotlin\\vn\\gapowork\\crawler | 1 | 4 | 0 | 3 | 7 |
| android\\app\\src\\main\\res | 4 | 26 | 32 | 6 | 64 |
| android\\app\\src\\main\\res\\drawable | 1 | 4 | 7 | 2 | 13 |
| android\\app\\src\\main\\res\\drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| android\\app\\src\\main\\res\\values | 1 | 9 | 9 | 1 | 19 |
| android\\app\\src\\main\\res\\values-night | 1 | 9 | 9 | 1 | 19 |
| android\\gradle | 1 | 5 | 0 | 1 | 6 |
| android\\gradle\\wrapper | 1 | 5 | 0 | 1 | 6 |
| assets | 2 | 2 | 0 | 0 | 2 |
| assets\\images | 2 | 2 | 0 | 0 | 2 |
| ios | 11 | 232 | 4 | 13 | 249 |
| ios\\Runner | 10 | 225 | 2 | 9 | 236 |
| ios\\Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| ios\\RunnerTests | 1 | 7 | 2 | 4 | 13 |
| ios\\Runner\\Assets.xcassets | 6 | 151 | 0 | 4 | 155 |
| ios\\Runner\\Assets.xcassets\\AppIcon-dev.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios\\Runner\\Assets.xcassets\\AppIcon-prod.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios\\Runner\\Assets.xcassets\\AppIcon-uat.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios\\Runner\\Assets.xcassets\\AppIcon.appiconset | 1 | 122 | 0 | 1 | 123 |
| ios\\Runner\\Assets.xcassets\\LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| ios\\Runner\\Base.lproj | 2 | 61 | 2 | 2 | 65 |
| lib | 292 | 30,786 | 2,517 | 4,144 | 37,447 |
| lib (Files) | 3 | 24 | 0 | 9 | 33 |
| lib\\app | 82 | 2,926 | 1,003 | 763 | 4,692 |
| lib\\app (Files) | 4 | 346 | 6 | 55 | 407 |
| lib\\app\\app_config | 8 | 258 | 16 | 55 | 329 |
| lib\\app\\app_config (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\app\\app_config\\bloc | 5 | 247 | 16 | 50 | 313 |
| lib\\app\\app_config\\widgets | 2 | 9 | 0 | 4 | 13 |
| lib\\app\\base | 9 | 261 | 51 | 77 | 389 |
| lib\\app\\base (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\base\\networking | 8 | 260 | 51 | 76 | 387 |
| lib\\app\\base\\networking (Files) | 3 | 108 | 7 | 27 | 142 |
| lib\\app\\base\\networking\\exception | 2 | 4 | 5 | 4 | 13 |
| lib\\app\\base\\networking\\gapo | 3 | 148 | 39 | 45 | 232 |
| lib\\app\\constant | 4 | 40 | 24 | 13 | 77 |
| lib\\app\\features | 55 | 1,992 | 906 | 559 | 3,457 |
| lib\\app\\features (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\app\\features\\crawler | 47 | 1,765 | 887 | 518 | 3,170 |
| lib\\app\\features\\crawler (Files) | 3 | 100 | 0 | 10 | 110 |
| lib\\app\\features\\crawler\\base_crawl_bloc | 8 | 84 | 2 | 22 | 108 |
| lib\\app\\features\\crawler\\base_crawl_bloc (Files) | 2 | 23 | 0 | 5 | 28 |
| lib\\app\\features\\crawler\\base_crawl_bloc\\bloc | 4 | 52 | 2 | 13 | 67 |
| lib\\app\\features\\crawler\\base_crawl_bloc\\widgets | 2 | 9 | 0 | 4 | 13 |
| lib\\app\\features\\crawler\\bloc | 6 | 643 | 778 | 193 | 1,614 |
| lib\\app\\features\\crawler\\crawl_feed | 6 | 272 | 31 | 92 | 395 |
| lib\\app\\features\\crawler\\crawl_feed (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\features\\crawler\\crawl_feed\\bloc | 5 | 271 | 31 | 91 | 393 |
| lib\\app\\features\\crawler\\crawl_group | 6 | 188 | 21 | 57 | 266 |
| lib\\app\\features\\crawler\\crawl_group (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\features\\crawler\\crawl_group\\bloc | 5 | 187 | 21 | 56 | 264 |
| lib\\app\\features\\crawler\\crawl_member | 6 | 183 | 23 | 55 | 261 |
| lib\\app\\features\\crawler\\crawl_member (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\features\\crawler\\crawl_member\\bloc | 5 | 182 | 23 | 54 | 259 |
| lib\\app\\features\\crawler\\crawl_thread | 6 | 198 | 31 | 61 | 290 |
| lib\\app\\features\\crawler\\crawl_thread (Files) | 1 | 1 | 0 | 1 | 2 |
| lib\\app\\features\\crawler\\crawl_thread\\bloc | 5 | 197 | 31 | 60 | 288 |
| lib\\app\\features\\crawler\\mixin | 2 | 95 | 1 | 22 | 118 |
| lib\\app\\features\\crawler\\sync | 2 | 1 | 0 | 3 | 4 |
| lib\\app\\features\\crawler\\unsync | 2 | 1 | 0 | 3 | 4 |
| lib\\app\\features\\home | 7 | 225 | 19 | 40 | 284 |
| lib\\app\\features\\home (Files) | 2 | 104 | 4 | 9 | 117 |
| lib\\app\\features\\home\\bloc | 5 | 121 | 15 | 31 | 167 |
| lib\\app\\splash | 2 | 29 | 0 | 4 | 33 |
| lib\\config | 3 | 50 | 0 | 18 | 68 |
| lib\\data | 77 | 2,840 | 232 | 517 | 3,589 |
| lib\\data (Files) | 1 | 3 | 0 | 1 | 4 |
| lib\\data\\data_source | 15 | 1,045 | 74 | 140 | 1,259 |
| lib\\data\\data_source (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\data\\data_source\\local | 2 | 181 | 9 | 33 | 223 |
| lib\\data\\data_source\\remote | 12 | 862 | 65 | 106 | 1,033 |
| lib\\data\\data_source\\remote (Files) | 5 | 466 | 30 | 54 | 550 |
| lib\\data\\data_source\\remote\\download | 2 | 33 | 0 | 5 | 38 |
| lib\\data\\data_source\\remote\\gapo | 5 | 363 | 35 | 47 | 445 |
| lib\\data\\model | 56 | 1,619 | 138 | 343 | 2,100 |
| lib\\data\\model (Files) | 2 | 16 | 0 | 4 | 20 |
| lib\\data\\model\\download | 3 | 132 | 15 | 26 | 173 |
| lib\\data\\model\\facebook | 3 | 25 | 4 | 10 | 39 |
| lib\\data\\model\\gpw | 17 | 602 | 70 | 108 | 780 |
| lib\\data\\model\\gpw (Files) | 4 | 44 | 4 | 17 | 65 |
| lib\\data\\model\\gpw\\auth | 13 | 558 | 66 | 91 | 715 |
| lib\\data\\model\\gpw\\auth (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\data\\model\\gpw\\auth\\request | 9 | 522 | 53 | 76 | 651 |
| lib\\data\\model\\gpw\\auth\\response | 3 | 34 | 13 | 14 | 61 |
| lib\\data\\model\\workplace | 31 | 844 | 49 | 195 | 1,088 |
| lib\\data\\model\\workplace (Files) | 1 | 6 | 0 | 1 | 7 |
| lib\\data\\model\\workplace\\base | 8 | 180 | 15 | 45 | 240 |
| lib\\data\\model\\workplace\\comment | 5 | 93 | 8 | 25 | 126 |
| lib\\data\\model\\workplace\\conversation | 5 | 167 | 8 | 38 | 213 |
| lib\\data\\model\\workplace\\enums | 2 | 54 | 0 | 18 | 72 |
| lib\\data\\model\\workplace\\group | 5 | 155 | 10 | 31 | 196 |
| lib\\data\\model\\workplace\\post | 5 | 189 | 8 | 37 | 234 |
| lib\\data\\repository | 5 | 173 | 20 | 33 | 226 |
| lib\\di | 11 | 502 | 88 | 63 | 653 |
| lib\\di (Files) | 1 | 2 | 0 | 1 | 3 |
| lib\\di\\component | 3 | 336 | 17 | 18 | 371 |
| lib\\di\\modules | 7 | 164 | 71 | 44 | 279 |
| lib\\domain | 92 | 21,873 | 803 | 2,417 | 25,093 |
| lib\\domain (Files) | 1 | 3 | 0 | 1 | 4 |
| lib\\domain\\entity | 66 | 21,313 | 477 | 2,233 | 24,023 |
| lib\\domain\\entity (Files) | 1 | 4 | 0 | 1 | 5 |
| lib\\domain\\entity\\base | 12 | 2,929 | 60 | 298 | 3,287 |
| lib\\domain\\entity\\base (Files) | 2 | 148 | 10 | 22 | 180 |
| lib\\domain\\entity\\base\\app | 4 | 741 | 15 | 87 | 843 |
| lib\\domain\\entity\\base\\log | 3 | 1,033 | 15 | 97 | 1,145 |
| lib\\domain\\entity\\base\\status | 3 | 1,007 | 20 | 92 | 1,119 |
| lib\\domain\\entity\\enums | 6 | 131 | 46 | 38 | 215 |
| lib\\domain\\entity\\enums (Files) | 4 | 118 | 46 | 32 | 196 |
| lib\\domain\\entity\\enums\\upload | 2 | 13 | 0 | 6 | 19 |
| lib\\domain\\entity\\gapo | 25 | 1,061 | 102 | 201 | 1,364 |
| lib\\domain\\entity\\gapo (Files) | 15 | 631 | 24 | 103 | 758 |
| lib\\domain\\entity\\gapo\\upload | 10 | 430 | 78 | 98 | 606 |
| lib\\domain\\entity\\gapo\\upload (Files) | 3 | 199 | 41 | 45 | 285 |
| lib\\domain\\entity\\gapo\\upload\\callback | 4 | 188 | 35 | 42 | 265 |
| lib\\domain\\entity\\gapo\\upload\\params | 3 | 43 | 2 | 11 | 56 |
| lib\\domain\\entity\\workplace | 22 | 17,188 | 269 | 1,695 | 19,152 |
| lib\\domain\\entity\\workplace (Files) | 1 | 5 | 0 | 1 | 6 |
| lib\\domain\\entity\\workplace\\feed | 5 | 1,748 | 71 | 200 | 2,019 |
| lib\\domain\\entity\\workplace\\group | 3 | 2,626 | 20 | 256 | 2,902 |
| lib\\domain\\entity\\workplace\\other | 5 | 5,207 | 96 | 497 | 5,800 |
| lib\\domain\\entity\\workplace\\thread | 5 | 5,332 | 64 | 510 | 5,906 |
| lib\\domain\\entity\\workplace\\user | 3 | 2,270 | 18 | 231 | 2,519 |
| lib\\domain\\repository | 5 | 49 | 22 | 22 | 93 |
| lib\\domain\\usecase | 20 | 508 | 304 | 161 | 973 |
| lib\\domain\\usecase (Files) | 4 | 55 | 99 | 29 | 183 |
| lib\\domain\\usecase\\gapo | 6 | 215 | 47 | 55 | 317 |
| lib\\domain\\usecase\\workplace | 10 | 238 | 158 | 77 | 473 |
| lib\\flutter_gen | 2 | 69 | 10 | 15 | 94 |
| lib\\helpers | 5 | 284 | 49 | 73 | 406 |
| lib\\l10n | 6 | 100 | 85 | 34 | 219 |
| lib\\mapper | 8 | 2,024 | 233 | 199 | 2,456 |
| lib\\mapper (Files) | 3 | 256 | 73 | 39 | 368 |
| lib\\mapper\\entity | 5 | 1,768 | 160 | 160 | 2,088 |
| lib\\route | 3 | 94 | 14 | 36 | 144 |
| linux | 3 | 86 | 18 | 27 | 131 |
| macos | 5 | 438 | 2 | 11 | 451 |
| macos\\Runner | 4 | 431 | 0 | 7 | 438 |
| macos\\Runner (Files) | 2 | 20 | 0 | 6 | 26 |
| macos\\RunnerTests | 1 | 7 | 2 | 4 | 13 |
| macos\\Runner\\Assets.xcassets | 1 | 68 | 0 | 0 | 68 |
| macos\\Runner\\Assets.xcassets\\AppIcon.appiconset | 1 | 68 | 0 | 0 | 68 |
| macos\\Runner\\Base.lproj | 1 | 343 | 0 | 1 | 344 |
| project_configs | 6 | 132 | 0 | 18 | 150 |
| test | 1 | 14 | 10 | 6 | 30 |
| web | 2 | 70 | 16 | 18 | 104 |
| windows | 8 | 428 | 85 | 132 | 645 |
| windows\\runner | 8 | 428 | 85 | 132 | 645 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)