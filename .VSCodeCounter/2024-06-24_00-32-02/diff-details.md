# Diff Details

Date : 2024-06-24 00:32:02

Directory c:\\Softwares\\Gapo\\gapoflutter-crawler

Total : 51 files,  -775 codes, -809 comments, -269 blanks, all -1853 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [assets/images/horizontal_loading.json](/assets/images/horizontal_loading.json) | JSON | 1 | 0 | 0 | 1 |
| [lib/app/features/crawler/base_crawl_bloc/base_crawl_bloc.dart](/lib/app/features/crawler/base_crawl_bloc/base_crawl_bloc.dart) | Dart | -2 | 0 | -1 | -3 |
| [lib/app/features/crawler/base_crawl_bloc/base_crawl_bloc_page.dart](/lib/app/features/crawler/base_crawl_bloc/base_crawl_bloc_page.dart) | Dart | -21 | 0 | -4 | -25 |
| [lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_bloc.dart](/lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_bloc.dart) | Dart | -28 | -1 | -5 | -34 |
| [lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_event.dart](/lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_event.dart) | Dart | -7 | 0 | -2 | -9 |
| [lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_state.dart](/lib/app/features/crawler/base_crawl_bloc/bloc/base_crawl_bloc_state.dart) | Dart | -14 | -1 | -5 | -20 |
| [lib/app/features/crawler/base_crawl_bloc/bloc/bloc.dart](/lib/app/features/crawler/base_crawl_bloc/bloc/bloc.dart) | Dart | -3 | 0 | -1 | -4 |
| [lib/app/features/crawler/base_crawl_bloc/widgets/base_crawl_bloc_body_page.dart](/lib/app/features/crawler/base_crawl_bloc/widgets/base_crawl_bloc_body_page.dart) | Dart | -8 | 0 | -3 | -11 |
| [lib/app/features/crawler/base_crawl_bloc/widgets/widgets.dart](/lib/app/features/crawler/base_crawl_bloc/widgets/widgets.dart) | Dart | -1 | 0 | -1 | -2 |
| [lib/app/features/crawler/bloc/bloc.dart](/lib/app/features/crawler/bloc/bloc.dart) | Dart | -1 | 0 | 0 | -1 |
| [lib/app/features/crawler/bloc/crawl_bloc.dart](/lib/app/features/crawler/bloc/crawl_bloc.dart) | Dart | 95 | -211 | -5 | -121 |
| [lib/app/features/crawler/bloc/crawl_bloc_bak.dart](/lib/app/features/crawler/bloc/crawl_bloc_bak.dart) | Dart | 0 | -535 | -69 | -604 |
| [lib/app/features/crawler/bloc/crawl_event.dart](/lib/app/features/crawler/bloc/crawl_event.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/app/features/crawler/bloc/crawl_state.dart](/lib/app/features/crawler/bloc/crawl_state.dart) | Dart | -12 | 0 | 4 | -8 |
| [lib/app/features/crawler/bloc/crawl_state.freezed.dart](/lib/app/features/crawler/bloc/crawl_state.freezed.dart) | Dart | -431 | -13 | -54 | -498 |
| [lib/app/features/crawler/crawl.main.page.dart](/lib/app/features/crawler/crawl.main.page.dart) | Dart | 83 | 55 | 9 | 147 |
| [lib/app/features/crawler/crawl_feed/bloc/bloc.dart](/lib/app/features/crawler/crawl_feed/bloc/bloc.dart) | Dart | -3 | 0 | -1 | -4 |
| [lib/app/features/crawler/crawl_feed/bloc/crawl_feed_bloc.dart](/lib/app/features/crawler/crawl_feed/bloc/crawl_feed_bloc.dart) | Dart | -205 | -15 | -66 | -286 |
| [lib/app/features/crawler/crawl_feed/bloc/crawl_feed_event.dart](/lib/app/features/crawler/crawl_feed/bloc/crawl_feed_event.dart) | Dart | -4 | 0 | -2 | -6 |
| [lib/app/features/crawler/crawl_feed/bloc/crawl_feed_state.dart](/lib/app/features/crawler/crawl_feed/bloc/crawl_feed_state.dart) | Dart | -14 | -1 | -5 | -20 |
| [lib/app/features/crawler/crawl_feed/bloc/crawl_feed_state.freezed.dart](/lib/app/features/crawler/crawl_feed/bloc/crawl_feed_state.freezed.dart) | Dart | -45 | -15 | -17 | -77 |
| [lib/app/features/crawler/crawl_feed/crawl_feed.dart](/lib/app/features/crawler/crawl_feed/crawl_feed.dart) | Dart | -1 | 0 | -1 | -2 |
| [lib/app/features/crawler/crawl_group/bloc/bloc.dart](/lib/app/features/crawler/crawl_group/bloc/bloc.dart) | Dart | -3 | 0 | -1 | -4 |
| [lib/app/features/crawler/crawl_group/bloc/crawl_group_bloc.dart](/lib/app/features/crawler/crawl_group/bloc/crawl_group_bloc.dart) | Dart | -121 | -5 | -31 | -157 |
| [lib/app/features/crawler/crawl_group/bloc/crawl_group_event.dart](/lib/app/features/crawler/crawl_group/bloc/crawl_group_event.dart) | Dart | -4 | 0 | -2 | -6 |
| [lib/app/features/crawler/crawl_group/bloc/crawl_group_state.dart](/lib/app/features/crawler/crawl_group/bloc/crawl_group_state.dart) | Dart | -14 | -1 | -5 | -20 |
| [lib/app/features/crawler/crawl_group/bloc/crawl_group_state.freezed.dart](/lib/app/features/crawler/crawl_group/bloc/crawl_group_state.freezed.dart) | Dart | -45 | -15 | -17 | -77 |
| [lib/app/features/crawler/crawl_group/crawl_group.dart](/lib/app/features/crawler/crawl_group/crawl_group.dart) | Dart | -1 | 0 | -1 | -2 |
| [lib/app/features/crawler/crawl_member/bloc/bloc.dart](/lib/app/features/crawler/crawl_member/bloc/bloc.dart) | Dart | -3 | 0 | -1 | -4 |
| [lib/app/features/crawler/crawl_member/bloc/crawl_member_bloc.dart](/lib/app/features/crawler/crawl_member/bloc/crawl_member_bloc.dart) | Dart | -116 | -7 | -29 | -152 |
| [lib/app/features/crawler/crawl_member/bloc/crawl_member_event.dart](/lib/app/features/crawler/crawl_member/bloc/crawl_member_event.dart) | Dart | -4 | 0 | -2 | -6 |
| [lib/app/features/crawler/crawl_member/bloc/crawl_member_state.dart](/lib/app/features/crawler/crawl_member/bloc/crawl_member_state.dart) | Dart | -14 | -1 | -5 | -20 |
| [lib/app/features/crawler/crawl_member/bloc/crawl_member_state.freezed.dart](/lib/app/features/crawler/crawl_member/bloc/crawl_member_state.freezed.dart) | Dart | -45 | -15 | -17 | -77 |
| [lib/app/features/crawler/crawl_member/crawl_member.dart](/lib/app/features/crawler/crawl_member/crawl_member.dart) | Dart | -1 | 0 | -1 | -2 |
| [lib/app/features/crawler/crawl_thread/bloc/bloc.dart](/lib/app/features/crawler/crawl_thread/bloc/bloc.dart) | Dart | -3 | 0 | -1 | -4 |
| [lib/app/features/crawler/crawl_thread/bloc/crawl_thread_bloc.dart](/lib/app/features/crawler/crawl_thread/bloc/crawl_thread_bloc.dart) | Dart | -131 | -15 | -35 | -181 |
| [lib/app/features/crawler/crawl_thread/bloc/crawl_thread_event.dart](/lib/app/features/crawler/crawl_thread/bloc/crawl_thread_event.dart) | Dart | -4 | 0 | -2 | -6 |
| [lib/app/features/crawler/crawl_thread/bloc/crawl_thread_state.dart](/lib/app/features/crawler/crawl_thread/bloc/crawl_thread_state.dart) | Dart | -14 | -1 | -5 | -20 |
| [lib/app/features/crawler/crawl_thread/bloc/crawl_thread_state.freezed.dart](/lib/app/features/crawler/crawl_thread/bloc/crawl_thread_state.freezed.dart) | Dart | -45 | -15 | -17 | -77 |
| [lib/app/features/crawler/crawl_thread/crawl_thread.dart](/lib/app/features/crawler/crawl_thread/crawl_thread.dart) | Dart | -1 | 0 | -1 | -2 |
| [lib/app/features/crawler/crawler.dart](/lib/app/features/crawler/crawler.dart) | Dart | -3 | 0 | 0 | -3 |
| [lib/app/features/crawler/queues/feed_queue.dart](/lib/app/features/crawler/queues/feed_queue.dart) | Dart | 180 | 1 | 60 | 241 |
| [lib/app/features/crawler/queues/group_queue.dart](/lib/app/features/crawler/queues/group_queue.dart) | Dart | 98 | 1 | 27 | 126 |
| [lib/app/features/crawler/queues/member_queue.dart](/lib/app/features/crawler/queues/member_queue.dart) | Dart | 131 | 4 | 35 | 170 |
| [lib/app/features/crawler/queues/queues.dart](/lib/app/features/crawler/queues/queues.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/app/features/crawler/queues/thread_queue.dart](/lib/app/features/crawler/queues/thread_queue.dart) | Dart | 110 | 15 | 32 | 157 |
| [lib/app/features/home/<USER>/home_page_state.dart](/lib/app/features/home/<USER>/home_page_state.dart) | Dart | -11 | 0 | -1 | -12 |
| [lib/app/features/home/<USER>/home_page_state.freezed.dart](/lib/app/features/home/<USER>/home_page_state.freezed.dart) | Dart | -96 | -15 | -23 | -134 |
| [lib/app/features/home/<USER>/lib/app/features/home/<USER>
| [lib/flutter_gen/assets.gen.dart](/lib/flutter_gen/assets.gen.dart) | Dart | 3 | 1 | 1 | 5 |
| [pubspec.yaml](/pubspec.yaml) | YAML | 1 | 0 | 0 | 1 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details