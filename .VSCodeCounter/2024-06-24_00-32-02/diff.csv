"filename", "language", "YAML", "<PERSON><PERSON><PERSON>", "Dart", "comment", "blank", "total"
"c:\Softwares\Gapo\gapoflutter-crawler\assets\images\horizontal_loading.json", "JSON", 0, 1, 0, 0, 0, 1
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\base_crawl_bloc\base_crawl_bloc.dart", "Dart", 0, 0, -2, 0, -1, -3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\base_crawl_bloc\base_crawl_bloc_page.dart", "Dart", 0, 0, -21, 0, -4, -25
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\base_crawl_bloc\bloc\base_crawl_bloc_bloc.dart", "Dart", 0, 0, -28, -1, -5, -34
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\base_crawl_bloc\bloc\base_crawl_bloc_event.dart", "Dart", 0, 0, -7, 0, -2, -9
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\base_crawl_bloc\bloc\base_crawl_bloc_state.dart", "Dart", 0, 0, -14, -1, -5, -20
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\base_crawl_bloc\bloc\bloc.dart", "Dart", 0, 0, -3, 0, -1, -4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\base_crawl_bloc\widgets\base_crawl_bloc_body_page.dart", "Dart", 0, 0, -8, 0, -3, -11
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\base_crawl_bloc\widgets\widgets.dart", "Dart", 0, 0, -1, 0, -1, -2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\bloc.dart", "Dart", 0, 0, -1, 0, 0, -1
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_bloc.dart", "Dart", 0, 0, 95, -211, -5, -121
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_bloc_bak.dart", "Dart", 0, 0, 0, -535, -69, -604
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_event.dart", "Dart", 0, 0, 4, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_state.dart", "Dart", 0, 0, -12, 0, 4, -8
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_state.freezed.dart", "Dart", 0, 0, -431, -13, -54, -498
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl.main.page.dart", "Dart", 0, 0, 83, 55, 9, 147
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_feed\bloc\bloc.dart", "Dart", 0, 0, -3, 0, -1, -4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_feed\bloc\crawl_feed_bloc.dart", "Dart", 0, 0, -205, -15, -66, -286
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_feed\bloc\crawl_feed_event.dart", "Dart", 0, 0, -4, 0, -2, -6
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_feed\bloc\crawl_feed_state.dart", "Dart", 0, 0, -14, -1, -5, -20
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_feed\bloc\crawl_feed_state.freezed.dart", "Dart", 0, 0, -45, -15, -17, -77
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_feed\crawl_feed.dart", "Dart", 0, 0, -1, 0, -1, -2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_group\bloc\bloc.dart", "Dart", 0, 0, -3, 0, -1, -4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_group\bloc\crawl_group_bloc.dart", "Dart", 0, 0, -121, -5, -31, -157
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_group\bloc\crawl_group_event.dart", "Dart", 0, 0, -4, 0, -2, -6
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_group\bloc\crawl_group_state.dart", "Dart", 0, 0, -14, -1, -5, -20
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_group\bloc\crawl_group_state.freezed.dart", "Dart", 0, 0, -45, -15, -17, -77
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_group\crawl_group.dart", "Dart", 0, 0, -1, 0, -1, -2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_member\bloc\bloc.dart", "Dart", 0, 0, -3, 0, -1, -4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_member\bloc\crawl_member_bloc.dart", "Dart", 0, 0, -116, -7, -29, -152
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_member\bloc\crawl_member_event.dart", "Dart", 0, 0, -4, 0, -2, -6
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_member\bloc\crawl_member_state.dart", "Dart", 0, 0, -14, -1, -5, -20
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_member\bloc\crawl_member_state.freezed.dart", "Dart", 0, 0, -45, -15, -17, -77
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_member\crawl_member.dart", "Dart", 0, 0, -1, 0, -1, -2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_thread\bloc\bloc.dart", "Dart", 0, 0, -3, 0, -1, -4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_thread\bloc\crawl_thread_bloc.dart", "Dart", 0, 0, -131, -15, -35, -181
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_thread\bloc\crawl_thread_event.dart", "Dart", 0, 0, -4, 0, -2, -6
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_thread\bloc\crawl_thread_state.dart", "Dart", 0, 0, -14, -1, -5, -20
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_thread\bloc\crawl_thread_state.freezed.dart", "Dart", 0, 0, -45, -15, -17, -77
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl_thread\crawl_thread.dart", "Dart", 0, 0, -1, 0, -1, -2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawler.dart", "Dart", 0, 0, -3, 0, 0, -3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\feed_queue.dart", "Dart", 0, 0, 180, 1, 60, 241
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\group_queue.dart", "Dart", 0, 0, 98, 1, 27, 126
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\member_queue.dart", "Dart", 0, 0, 131, 4, 35, 170
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\queues.dart", "Dart", 0, 0, 4, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\thread_queue.dart", "Dart", 0, 0, 110, 15, 32, 157
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\home\bloc\home_page_state.dart", "Dart", 0, 0, -11, 0, -1, -12
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\home\bloc\home_page_state.freezed.dart", "Dart", 0, 0, -96, -15, -23, -134
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\home\home_page.dart", "Dart", 0, 0, -6, -4, 0, -10
"c:\Softwares\Gapo\gapoflutter-crawler\lib\flutter_gen\assets.gen.dart", "Dart", 0, 0, 3, 1, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\pubspec.yaml", "YAML", 1, 0, 0, 0, 0, 1
"Total", "-", 1, 1, -777, -809, -269, -1853