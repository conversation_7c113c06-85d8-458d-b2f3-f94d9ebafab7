"filename", "language", "YAM<PERSON>", "H<PERSON><PERSON>", "JSON", "Dart", "Markdown", "C++", "Swift", "XML", "Groovy", "Java Properties", "Kotlin", "comment", "blank", "total"
"c:\Softwares\Gapo\gapoflutter-crawler\README.md", "Markdown", 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 12, 52
"c:\Softwares\Gapo\gapoflutter-crawler\analysis_options.yaml", "YAML", 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 3, 28
"c:\Softwares\Gapo\gapoflutter-crawler\android\app\build.gradle", "Groovy", 0, 0, 0, 0, 0, 0, 0, 0, 51, 0, 0, 5, 12, 68
"c:\Softwares\Gapo\gapoflutter-crawler\android\app\src\debug\AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 4, 0, 11
"c:\Softwares\Gapo\gapoflutter-crawler\android\app\src\main\AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 32, 0, 0, 0, 6, 0, 38
"c:\Softwares\Gapo\gapoflutter-crawler\android\app\src\main\kotlin\vn\gapowork\crawler\MainActivity.kt", "Kotlin", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 3, 7
"c:\Softwares\Gapo\gapoflutter-crawler\android\app\src\main\res\drawable-v21\launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 7, 2, 13
"c:\Softwares\Gapo\gapoflutter-crawler\android\app\src\main\res\drawable\launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 7, 2, 13
"c:\Softwares\Gapo\gapoflutter-crawler\android\app\src\main\res\values-night\styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 9, 1, 19
"c:\Softwares\Gapo\gapoflutter-crawler\android\app\src\main\res\values\styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 9, 1, 19
"c:\Softwares\Gapo\gapoflutter-crawler\android\build.gradle", "Groovy", 0, 0, 0, 0, 0, 0, 0, 0, 27, 0, 0, 0, 5, 32
"c:\Softwares\Gapo\gapoflutter-crawler\android\gradle.properties", "Java Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\android\gradle\wrapper\gradle-wrapper.properties", "Java Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 1, 6
"c:\Softwares\Gapo\gapoflutter-crawler\android\settings.gradle", "Groovy", 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 5, 21
"c:\Softwares\Gapo\gapoflutter-crawler\assets\images\horizontal_loading.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Softwares\Gapo\gapoflutter-crawler\assets\images\splash_loading_1.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Softwares\Gapo\gapoflutter-crawler\assets\images\splash_loading_2.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Softwares\Gapo\gapoflutter-crawler\build.yaml", "YAML", 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7
"c:\Softwares\Gapo\gapoflutter-crawler\ios\RunnerTests\RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 2, 4, 13
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 2, 14
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\Assets.xcassets\AppIcon-dev.appiconset\Contents.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\Assets.xcassets\AppIcon-prod.appiconset\Contents.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\Assets.xcassets\AppIcon-uat.appiconset\Contents.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\Assets.xcassets\AppIcon.appiconset\Contents.json", "JSON", 0, 0, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 123
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\Assets.xcassets\LaunchImage.imageset\Contents.json", "JSON", 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\Assets.xcassets\LaunchImage.imageset\README.md", "Markdown", 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 2, 5
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\Base.lproj\LaunchScreen.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 36, 0, 0, 0, 1, 1, 38
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\Base.lproj\Main.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 0, 1, 1, 27
"c:\Softwares\Gapo\gapoflutter-crawler\ios\Runner\Runner-Bridging-Header.h", "C++", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\app.dart", "Dart", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\app_config\app_config.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\app_config\bloc\app_config_bloc.dart", "Dart", 0, 0, 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 16, 109
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\app_config\bloc\app_config_event.dart", "Dart", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 5, 21
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\app_config\bloc\app_config_state.dart", "Dart", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 1, 5, 23
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\app_config\bloc\app_config_state.freezed.dart", "Dart", 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 15, 23, 156
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\app_config\bloc\bloc.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\app_config\widgets\app_config_body_page.dart", "Dart", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\app_config\widgets\widgets.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\base\base.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\base\networking\exception\exception.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\base\networking\exception\exception_code.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 5, 3, 11
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\base\networking\gapo\authentication_interceptor.dart", "Dart", 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 11, 44
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\base\networking\gapo\gapo.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\base\networking\gapo\gp_token_interceptor.dart", "Dart", 0, 0, 0, 113, 0, 0, 0, 0, 0, 0, 0, 39, 33, 185
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\base\networking\logger_inteceptor.dart", "Dart", 0, 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 7, 18, 100
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\base\networking\networking.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\base\networking\workplace_auth_inteceptor.dart", "Dart", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 8, 37
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\constant\app_constant.dart", "Dart", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\constant\constant.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\constant\fb_wp_url.constants.dart", "Dart", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 11, 5, 32
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\constant\gapo_url.constants.dart", "Dart", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 13, 5, 32
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\bloc.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_bloc.dart", "Dart", 0, 0, 0, 125, 0, 0, 0, 0, 0, 0, 0, 0, 30, 155
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_event.dart", "Dart", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 5, 21
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_state.dart", "Dart", 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 1, 10, 47
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\bloc\crawl_state.freezed.dart", "Dart", 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 18, 24, 160
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawl.main.page.dart", "Dart", 0, 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 55, 17, 242
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\crawler.dart", "Dart", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 1, 8
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\mixin\mixin.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\mixin\upload_mixin.dart", "Dart", 0, 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 1, 21, 116
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\feed_queue.dart", "Dart", 0, 0, 0, 180, 0, 0, 0, 0, 0, 0, 0, 1, 60, 241
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\group_queue.dart", "Dart", 0, 0, 0, 98, 0, 0, 0, 0, 0, 0, 0, 1, 27, 126
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\member_queue.dart", "Dart", 0, 0, 0, 131, 0, 0, 0, 0, 0, 0, 0, 4, 35, 170
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\queues.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\queues\thread_queue.dart", "Dart", 0, 0, 0, 110, 0, 0, 0, 0, 0, 0, 0, 15, 32, 157
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\sync\sync.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\sync\sync.page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\unsync\unsync.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\crawler\unsync\unsync.page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\features.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\home\bloc\bloc.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\home\bloc\home_page_bloc.dart", "Dart", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\home\bloc\home_page_event.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\home\bloc\home_page_state.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 2, 6
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\home\home.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\features\home\home_page.dart", "Dart", 0, 0, 0, 96, 0, 0, 0, 0, 0, 0, 0, 0, 8, 104
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\main.app.dart", "Dart", 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 4, 59
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\splash\splash.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\splash\splash.page.dart", "Dart", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 3, 31
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\test_async.page.dart", "Dart", 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 5, 26, 149
"c:\Softwares\Gapo\gapoflutter-crawler\lib\app\test_attachment.page.dart", "Dart", 0, 0, 0, 165, 0, 0, 0, 0, 0, 0, 0, 1, 24, 190
"c:\Softwares\Gapo\gapoflutter-crawler\lib\config\app_configs.dart", "Dart", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 11, 40
"c:\Softwares\Gapo\gapoflutter-crawler\lib\config\bootstrap.dart", "Dart", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 6, 25
"c:\Softwares\Gapo\gapoflutter-crawler\lib\config\config.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\data_source.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\local\local.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\local\workplace_local.service.dart", "Dart", 0, 0, 0, 180, 0, 0, 0, 0, 0, 0, 0, 9, 32, 221
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\download\download.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\download\download.service.dart", "Dart", 0, 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 4, 36
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\facebook.service.dart", "Dart", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 10, 6, 36
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\facebook.service.g.dart", "Dart", 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 5, 13, 78
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\gapo\auth.service.dart", "Dart", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 9, 7, 45
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\gapo\auth.service.g.dart", "Dart", 0, 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 5, 15, 147
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\gapo\gapo.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\gapo\upload.service.dart", "Dart", 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 16, 9, 68
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\gapo\upload.service.g.dart", "Dart", 0, 0, 0, 162, 0, 0, 0, 0, 0, 0, 0, 5, 15, 182
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\remote.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\workplace.service.dart", "Dart", 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 10, 14, 91
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\data_source\remote\workplace.service.g.dart", "Dart", 0, 0, 0, 315, 0, 0, 0, 0, 0, 0, 0, 5, 20, 340
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\datetime_converter.dart", "Dart", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 3, 14
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\download\download.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\download\download_params.dart", "Dart", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 2, 15
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\download\download_params.freezed.dart", "Dart", 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 15, 23, 156
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\facebook\community_response.dart", "Dart", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 5, 21
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\facebook\community_response.g.dart", "Dart", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 4, 4, 16
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\facebook\facebook.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\auth.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\request\auth_check_email_request.dart", "Dart", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 9, 8, 33
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\request\auth_check_email_request.g.dart", "Dart", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 4, 5, 22
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_auth_params.dart", "Dart", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 1, 4, 23
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_auth_params.freezed.dart", "Dart", 0, 0, 0, 226, 0, 0, 0, 0, 0, 0, 0, 15, 23, 264
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_auth_params.g.dart", "Dart", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 4, 4, 18
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_signup_params.dart", "Dart", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 1, 4, 26
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_signup_params.freezed.dart", "Dart", 0, 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 15, 23, 243
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\request\gp_signup_params.g.dart", "Dart", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 4, 4, 18
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\request\request.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\response\auth_check_mail_response.dart", "Dart", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 9, 8, 35
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\response\auth_check_mail_response.g.dart", "Dart", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 4, 5, 24
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth\response\response.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth_reponse.dart", "Dart", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 8, 25
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\auth_reponse.g.dart", "Dart", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 4, 4, 14
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\gpw.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\gpw\upload_file_response.dart", "Dart", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 4, 22
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\model.dart", "Dart", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\base\base.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\base\workplace_generic_data_converter.dart", "Dart", 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 7, 55
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\base\workplace_list_response.dart", "Dart", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 6, 22
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\base\workplace_list_response.g.dart", "Dart", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 4, 4, 20
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\base\workplace_paging_response.dart", "Dart", 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 7, 33
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\base\workplace_paging_response.g.dart", "Dart", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 4, 5, 26
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\base\workplace_user.dart", "Dart", 0, 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 3, 10, 43
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\base\workplace_user.g.dart", "Dart", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 4, 5, 36
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\comment\comment.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\comment\message_tags.dart", "Dart", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 5, 26
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\comment\message_tags.g.dart", "Dart", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 4, 6, 37
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\comment\workplace_reactions.dart", "Dart", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 7, 26
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\comment\workplace_reactions.g.dart", "Dart", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 4, 6, 34
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\conversation\conversation.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\conversation\workplace_conversation_attachment_response.dart", "Dart", 0, 0, 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 12, 68
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\conversation\workplace_conversation_attachment_response.g.dart", "Dart", 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 4, 6, 44
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\conversation\workplace_conversations_response.dart", "Dart", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 13, 53
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\conversation\workplace_conversations_response.g.dart", "Dart", 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 4, 6, 45
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\enums\enums.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\enums\workplace_enums.dart", "Dart", 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 0, 17, 70
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\group\group.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\group\workplace_feeds_response.dart", "Dart", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 2, 7, 38
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\group\workplace_feeds_response.g.dart", "Dart", 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 4, 6, 47
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\group\workplace_group_response.dart", "Dart", 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 11, 57
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\group\workplace_group_response.g.dart", "Dart", 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 4, 6, 51
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\post\post.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\post\workplace_post_attachments_response.dart", "Dart", 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 0, 13, 76
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\post\workplace_post_attachments_response.g.dart", "Dart", 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 4, 9, 72
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\post\workplace_post_comments_response.dart", "Dart", 0, 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 9, 40
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\post\workplace_post_comments_response.g.dart", "Dart", 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 4, 5, 43
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\model\workplace\workplace.dart", "Dart", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 1, 7
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\repository\download_impl.dart", "Dart", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 3, 20
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\repository\facebook_repo_impl.dart", "Dart", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 4, 20
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\repository\gapo_impl.dart", "Dart", 0, 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 10, 11, 96
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\repository\repository.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\data\repository\workplace_repo_impl.dart", "Dart", 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 10, 14, 85
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\component\app.component.config.dart", "Dart", 0, 0, 0, 316, 0, 0, 0, 0, 0, 0, 0, 8, 12, 336
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\component\app.component.dart", "Dart", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 9, 5, 32
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\component\component.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\di.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\modules\app.module.dart", "Dart", 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 9, 6, 41
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\modules\auth.module.dart", "Dart", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 11, 6, 34
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\modules\client.module.dart", "Dart", 0, 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 9, 12, 65
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\modules\database.module.dart", "Dart", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 20, 5, 53
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\modules\modules.dart", "Dart", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 1, 7
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\modules\navigator.module.dart", "Dart", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 10, 6, 33
"c:\Softwares\Gapo\gapoflutter-crawler\lib\di\modules\url.module.dart", "Dart", 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 12, 8, 46
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\domain.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\app\app.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\app\app_config.entity.dart", "Dart", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 9, 9, 47
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\app\app_config.entity.g.dart", "Dart", 0, 0, 0, 701, 0, 0, 0, 0, 0, 0, 0, 6, 73, 780
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\app\locale_enum.dart", "Dart", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 4, 13
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\base.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\base_crawl.entity.dart", "Dart", 0, 0, 0, 144, 0, 0, 0, 0, 0, 0, 0, 10, 21, 175
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\log\base_crawl_log.entity.dart", "Dart", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 9, 9, 40
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\log\base_crawl_log.entity.g.dart", "Dart", 0, 0, 0, 1010, 0, 0, 0, 0, 0, 0, 0, 6, 87, 1103
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\log\log.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\status\base_crawl_status.entity.dart", "Dart", 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 12, 13, 85
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\status\base_crawl_status.entity.g.dart", "Dart", 0, 0, 0, 946, 0, 0, 0, 0, 0, 0, 0, 8, 78, 1032
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\base\status\status.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\entity.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\enums\base_crawl_status_enum.dart", "Dart", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 30, 11, 55
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\enums\base_crawl_type.dart", "Dart", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 16, 8, 33
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\enums\enums.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\enums\gp_enums.dart", "Dart", 0, 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 12, 103
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\enums\upload\gp_upload_status_enum.dart", "Dart", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 5, 17
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\enums\upload\upload.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gapo.dart", "Dart", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 1, 10
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_attachement.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_auth.dart", "Dart", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 4, 14
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_comment.dart", "Dart", 0, 0, 0, 70, 0, 0, 0, 0, 0, 0, 0, 0, 11, 81
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_comment.g.dart", "Dart", 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 4, 15, 104
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_group.dart", "Dart", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 4, 33
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_group.g.dart", "Dart", 0, 0, 0, 39, 0, 0, 0, 0, 0, 0, 0, 4, 7, 50
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_message.dart", "Dart", 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 5, 28
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_message.g.dart", "Dart", 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 4, 6, 33
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_post.dart", "Dart", 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 10, 131
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_post.g.dart", "Dart", 0, 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 4, 16, 160
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_thread.dart", "Dart", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 5, 26
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gp_thread.g.dart", "Dart", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 4, 6, 31
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gpuser.dart", "Dart", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 7, 28
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\gpuser.g.dart", "Dart", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 4, 5, 28
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\callback\callback.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\callback\gp_upload_callback.dart", "Dart", 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 8, 13, 67
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\callback\gp_upload_progress.entity.dart", "Dart", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 12, 5, 34
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\callback\gp_upload_progress.entity.freezed.dart", "Dart", 0, 0, 0, 123, 0, 0, 0, 0, 0, 0, 0, 15, 23, 161
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\gp_dashboard_upload.entity.dart", "Dart", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 14, 7, 34
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\gp_dashboard_upload.entity.freezed.dart", "Dart", 0, 0, 0, 183, 0, 0, 0, 0, 0, 0, 0, 27, 37, 247
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\params\gp_upload_params.dart", "Dart", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 2, 6, 33
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\params\gp_upload_repo_params.dart", "Dart", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 4, 20
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\params\params.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\gapo\upload\upload.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\feed\feed.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_base_feed.entity.dart", "Dart", 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 10, 15, 70
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_base_feed.entity.g.dart", "Dart", 0, 0, 0, 1700, 0, 0, 0, 0, 0, 0, 0, 6, 171, 1877
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_group_feed.entity.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 6, 33
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\feed\workplace_user_feed.entity.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 7, 35
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\group\group.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\group\workplace_group.entity.dart", "Dart", 0, 0, 0, 52, 0, 0, 0, 0, 0, 0, 0, 9, 10, 71
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\group\workplace_group.entity.g.dart", "Dart", 0, 0, 0, 2573, 0, 0, 0, 0, 0, 0, 0, 11, 245, 2829
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\other\other.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\other\workplace_attachment.entity.dart", "Dart", 0, 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 59, 35, 163
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\other\workplace_attachment.entity.g.dart", "Dart", 0, 0, 0, 2471, 0, 0, 0, 0, 0, 0, 0, 15, 220, 2706
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\other\workplace_comment.entity.dart", "Dart", 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 9, 17, 91
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\other\workplace_comment.entity.g.dart", "Dart", 0, 0, 0, 2600, 0, 0, 0, 0, 0, 0, 0, 13, 224, 2837
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\thread\thread.dart", "Dart", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\thread\workplace_conversation_attachment.entity.dart", "Dart", 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 9, 13, 83
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\thread\workplace_conversation_attachment.entity.g.dart", "Dart", 0, 0, 0, 2907, 0, 0, 0, 0, 0, 0, 0, 13, 247, 3167
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\thread\workplace_conversations.entity.dart", "Dart", 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 34, 21, 108
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\thread\workplace_conversations.entity.g.dart", "Dart", 0, 0, 0, 2309, 0, 0, 0, 0, 0, 0, 0, 8, 228, 2545
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\user\user.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\user\workplace_community_member.entity.dart", "Dart", 0, 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 12, 11, 55
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\user\workplace_community_member.entity.g.dart", "Dart", 0, 0, 0, 2237, 0, 0, 0, 0, 0, 0, 0, 6, 219, 2462
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\entity\workplace\workplace.dart", "Dart", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\repository\download_repo.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\repository\facebook_repo.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 1, 2, 7
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\repository\gapo_repo.dart", "Dart", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 10, 6, 31
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\repository\repository.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\repository\workplace_repo.dart", "Dart", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 11, 12, 45
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\download_file.usecase.dart", "Dart", 0, 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 9, 40
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\facebook_get_community.usecase.dart", "Dart", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 10, 7, 36
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\feed_attachment.usecase.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 89, 12, 101
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\gapo\gapo.dart", "Dart", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_auth.usecase.dart", "Dart", 0, 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 10, 14, 71
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_auth_check_mail.usecase.dart", "Dart", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 9, 5, 31
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_create_user.usecase.dart", "Dart", 0, 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 9, 7, 47
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_login.usecase.dart", "Dart", 0, 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 9, 15, 98
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\gapo\gapo_upload.usecase.dart", "Dart", 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 10, 13, 64
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\usecase.dart", "Dart", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace.dart", "Dart", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 1, 10
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace_get_all_groups.usecase.dart", "Dart", 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 10, 9, 57
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace_get_app_config.usecase.dart", "Dart", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 10, 7, 37
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace_get_community_members.usecase.dart", "Dart", 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 10, 9, 56
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace_get_conversations.usecase.dart", "Dart", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 10, 9, 59
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace_get_group_feeds.usecase.dart", "Dart", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 10, 9, 59
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace_get_group_members.usecase.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 9, 60
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace_get_post_attachments.usecase.dart", "Dart", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 10, 8, 45
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace_get_post_comments.usecase.dart", "Dart", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 10, 8, 45
"c:\Softwares\Gapo\gapoflutter-crawler\lib\domain\usecase\workplace\workplace_get_user_feeds.usecase.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 8, 45
"c:\Softwares\Gapo\gapoflutter-crawler\lib\flutter_gen\assets.gen.dart", "Dart", 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 11, 15, 97
"c:\Softwares\Gapo\gapoflutter-crawler\lib\flutter_gen\flutter_gen.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\lib\helpers\csv.dart", "Dart", 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 10, 44
"c:\Softwares\Gapo\gapoflutter-crawler\lib\helpers\file_helper.dart", "Dart", 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 11, 56
"c:\Softwares\Gapo\gapoflutter-crawler\lib\helpers\gp_upload_management.dart", "Dart", 0, 0, 0, 143, 0, 0, 0, 0, 0, 0, 0, 18, 41, 202
"c:\Softwares\Gapo\gapoflutter-crawler\lib\helpers\helpers.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\helpers\jwt_token_decode.dart", "Dart", 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 31, 10, 99
"c:\Softwares\Gapo\gapoflutter-crawler\lib\l10n\app_en.arb", "JSON", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\l10n\app_localizations.dart", "Dart", 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 75, 19, 144
"c:\Softwares\Gapo\gapoflutter-crawler\lib\l10n\app_localizations_en.dart", "Dart", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 5, 7, 25
"c:\Softwares\Gapo\gapoflutter-crawler\lib\l10n\app_localizations_vi.dart", "Dart", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 5, 7, 25
"c:\Softwares\Gapo\gapoflutter-crawler\lib\l10n\app_vi.arb", "JSON", 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17
"c:\Softwares\Gapo\gapoflutter-crawler\lib\l10n\l10n.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\main.production.dart", "Dart", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11
"c:\Softwares\Gapo\gapoflutter-crawler\lib\main.staging.dart", "Dart", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11
"c:\Softwares\Gapo\gapoflutter-crawler\lib\main.uat.dart", "Dart", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11
"c:\Softwares\Gapo\gapoflutter-crawler\lib\mapper\entity\entity.dart", "Dart", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Softwares\Gapo\gapoflutter-crawler\lib\mapper\entity\gapo_entity_mapper.auto_mappr.dart", "Dart", 0, 0, 0, 488, 0, 0, 0, 0, 0, 0, 0, 47, 37, 572
"c:\Softwares\Gapo\gapoflutter-crawler\lib\mapper\entity\gapo_entity_mapper.dart", "Dart", 0, 0, 0, 209, 0, 0, 0, 0, 0, 0, 0, 35, 40, 284
"c:\Softwares\Gapo\gapoflutter-crawler\lib\mapper\entity\workplace_entity_mapper.auto_mappr.dart", "Dart", 0, 0, 0, 841, 0, 0, 0, 0, 0, 0, 0, 55, 47, 943
"c:\Softwares\Gapo\gapoflutter-crawler\lib\mapper\entity\workplace_entity_mapper.dart", "Dart", 0, 0, 0, 226, 0, 0, 0, 0, 0, 0, 0, 23, 35, 284
"c:\Softwares\Gapo\gapoflutter-crawler\lib\mapper\gp_mapper.auto_mappr.dart", "Dart", 0, 0, 0, 191, 0, 0, 0, 0, 0, 0, 0, 60, 28, 279
"c:\Softwares\Gapo\gapoflutter-crawler\lib\mapper\gp_mapper.dart", "Dart", 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 13, 10, 85
"c:\Softwares\Gapo\gapoflutter-crawler\lib\mapper\mapper.dart", "Dart", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Softwares\Gapo\gapoflutter-crawler\lib\route\go_router.route.dart", "Dart", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 10, 10, 60
"c:\Softwares\Gapo\gapoflutter-crawler\lib\route\go_router.route.g.dart", "Dart", 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 4, 25, 82
"c:\Softwares\Gapo\gapoflutter-crawler\lib\route\route.dart", "Dart", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Softwares\Gapo\gapoflutter-crawler\linux\main.cc", "C++", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 2, 7
"c:\Softwares\Gapo\gapoflutter-crawler\linux\my_application.cc", "C++", 0, 0, 0, 0, 0, 74, 0, 0, 0, 0, 0, 11, 20, 105
"c:\Softwares\Gapo\gapoflutter-crawler\linux\my_application.h", "C++", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 7, 5, 19
"c:\Softwares\Gapo\gapoflutter-crawler\macos\RunnerTests\RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 2, 4, 13
"c:\Softwares\Gapo\gapoflutter-crawler\macos\Runner\AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 2, 10
"c:\Softwares\Gapo\gapoflutter-crawler\macos\Runner\Assets.xcassets\AppIcon.appiconset\Contents.json", "JSON", 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68
"c:\Softwares\Gapo\gapoflutter-crawler\macos\Runner\Base.lproj\MainMenu.xib", "XML", 0, 0, 0, 0, 0, 0, 0, 343, 0, 0, 0, 0, 1, 344
"c:\Softwares\Gapo\gapoflutter-crawler\macos\Runner\MainFlutterWindow.swift", "Swift", 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 4, 16
"c:\Softwares\Gapo\gapoflutter-crawler\project_configs\flutter_launcher_icons-dev.yaml", "YAML", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17
"c:\Softwares\Gapo\gapoflutter-crawler\project_configs\flutter_launcher_icons-prod.yaml", "YAML", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17
"c:\Softwares\Gapo\gapoflutter-crawler\project_configs\flutter_launcher_icons-uat.yaml", "YAML", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17
"c:\Softwares\Gapo\gapoflutter-crawler\project_configs\package_rename_config-dev.yaml", "YAML", 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 33
"c:\Softwares\Gapo\gapoflutter-crawler\project_configs\package_rename_config-prod.yaml", "YAML", 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 33
"c:\Softwares\Gapo\gapoflutter-crawler\project_configs\package_rename_config-uat.yaml", "YAML", 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 33
"c:\Softwares\Gapo\gapoflutter-crawler\pubspec.yaml", "YAML", 70, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 1, 77
"c:\Softwares\Gapo\gapoflutter-crawler\test\widget_test.dart", "Dart", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 10, 6, 30
"c:\Softwares\Gapo\gapoflutter-crawler\web\index.html", "HTML", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 17, 68
"c:\Softwares\Gapo\gapoflutter-crawler\web\manifest.json", "JSON", 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"c:\Softwares\Gapo\gapoflutter-crawler\windows\runner\flutter_window.cpp", "C++", 0, 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 7, 16, 72
"c:\Softwares\Gapo\gapoflutter-crawler\windows\runner\flutter_window.h", "C++", 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 5, 9, 34
"c:\Softwares\Gapo\gapoflutter-crawler\windows\runner\main.cpp", "C++", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 4, 10, 44
"c:\Softwares\Gapo\gapoflutter-crawler\windows\runner\resource.h", "C++", 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 6, 2, 17
"c:\Softwares\Gapo\gapoflutter-crawler\windows\runner\utils.cpp", "C++", 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 2, 10, 66
"c:\Softwares\Gapo\gapoflutter-crawler\windows\runner\utils.h", "C++", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 6, 6, 20
"c:\Softwares\Gapo\gapoflutter-crawler\windows\runner\win32_window.cpp", "C++", 0, 0, 0, 0, 0, 210, 0, 0, 0, 0, 0, 24, 55, 289
"c:\Softwares\Gapo\gapoflutter-crawler\windows\runner\win32_window.h", "C++", 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 31, 24, 103
"Total", "-", 216, 35, 275, 30002, 43, 515, 46, 469, 94, 8, 4, 1914, 4149, 37770