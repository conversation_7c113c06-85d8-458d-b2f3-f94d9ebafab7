{"file:///c%3A/Softwares/Gapo/gapoflutter-crawler/pubspec.yaml": {"language": "YAML", "code": 70, "comment": 6, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/web/index.html": {"language": "HTML", "code": 35, "comment": 16, "blank": 17}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/assets/images/splash_loading_2.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/assets/images/horizontal_loading.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/test/widget_test.dart": {"language": "Dart", "code": 14, "comment": 10, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/project_configs/flutter_launcher_icons-prod.yaml": {"language": "YAML", "code": 17, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/app.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/README.md": {"language": "<PERSON><PERSON>", "code": 40, "comment": 0, "blank": 12}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/test_async.page.dart": {"language": "Dart", "code": 118, "comment": 5, "blank": 26}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/test_attachment.page.dart": {"language": "Dart", "code": 165, "comment": 1, "blank": 24}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/splash/splash.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/mapper/mapper.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/mapper/gp_mapper.dart": {"language": "Dart", "code": 62, "comment": 13, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/home/<USER>": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/features.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/home/<USER>/bloc.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/home/<USER>/home_page_event.dart": {"language": "Dart", "code": 0, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/home/<USER>/home_page_bloc.dart": {"language": "Dart", "code": 7, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/home/<USER>/home_page_state.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/mapper/gp_mapper.auto_mappr.dart": {"language": "Dart", "code": 191, "comment": 60, "blank": 28}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/crawl.main.page.dart": {"language": "Dart", "code": 170, "comment": 55, "blank": 17}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/mixin/mixin.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/unsync/unsync.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/mixin/upload_mixin.dart": {"language": "Dart", "code": 94, "comment": 1, "blank": 21}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/unsync/unsync.page.dart": {"language": "Dart", "code": 0, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/sync/sync.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/sync/sync.page.dart": {"language": "Dart", "code": 0, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/splash/splash.page.dart": {"language": "Dart", "code": 28, "comment": 0, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/crawler.dart": {"language": "Dart", "code": 7, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/queues/queues.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/crawl.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/queues/group_queue.dart": {"language": "Dart", "code": 98, "comment": 1, "blank": 27}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/queues/feed_queue.dart": {"language": "Dart", "code": 180, "comment": 1, "blank": 60}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/queues/thread_queue.dart": {"language": "Dart", "code": 110, "comment": 15, "blank": 32}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/queues/member_queue.dart": {"language": "Dart", "code": 131, "comment": 4, "blank": 35}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/web/manifest.json": {"language": "JSON", "code": 35, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_event.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/bloc/bloc.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/mapper/entity/entity.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/project_configs/flutter_launcher_icons-dev.yaml": {"language": "YAML", "code": 17, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.dart": {"language": "Dart", "code": 36, "comment": 1, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.freezed.dart": {"language": "Dart", "code": 118, "comment": 18, "blank": 24}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/mapper/entity/gapo_entity_mapper.auto_mappr.dart": {"language": "Dart", "code": 488, "comment": 47, "blank": 37}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.dart": {"language": "Dart", "code": 226, "comment": 23, "blank": 35}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/mapper/entity/gapo_entity_mapper.dart": {"language": "Dart", "code": 209, "comment": 35, "blank": 40}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart": {"language": "Dart", "code": 841, "comment": 55, "blank": 47}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/linux/my_application.h": {"language": "C++", "code": 7, "comment": 7, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/main.app.dart": {"language": "Dart", "code": 55, "comment": 0, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/route/go_router.route.g.dart": {"language": "Dart", "code": 53, "comment": 4, "blank": 25}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/linux/my_application.cc": {"language": "C++", "code": 74, "comment": 11, "blank": 20}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/route/go_router.route.dart": {"language": "Dart", "code": 40, "comment": 10, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/main.staging.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/main.uat.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/main.production.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/route/route.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/constant/constant.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/constant/gapo_url.constants.dart": {"language": "Dart", "code": 14, "comment": 13, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/constant/fb_wp_url.constants.dart": {"language": "Dart", "code": 16, "comment": 11, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/constant/app_constant.dart": {"language": "Dart", "code": 7, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/base/base.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/base/networking/networking.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/base/networking/logger_inteceptor.dart": {"language": "Dart", "code": 75, "comment": 7, "blank": 18}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/base/networking/workplace_auth_inteceptor.dart": {"language": "Dart", "code": 29, "comment": 0, "blank": 8}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/base/networking/exception/exception.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/linux/main.cc": {"language": "C++", "code": 5, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/base/networking/gapo/gp_token_interceptor.dart": {"language": "Dart", "code": 113, "comment": 39, "blank": 33}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/base/networking/gapo/gapo.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/base/networking/gapo/authentication_interceptor.dart": {"language": "Dart", "code": 33, "comment": 0, "blank": 11}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/l10n/app_en.arb": {"language": "JSON", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/base/networking/exception/exception_code.dart": {"language": "Dart", "code": 3, "comment": 5, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/l10n/app_localizations_vi.dart": {"language": "Dart", "code": 13, "comment": 5, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/l10n/app_localizations.dart": {"language": "Dart", "code": 50, "comment": 75, "blank": 19}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/l10n/app_localizations_en.dart": {"language": "Dart", "code": 13, "comment": 5, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/app_config/app_config.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/helpers/file_helper.dart": {"language": "Dart", "code": 45, "comment": 0, "blank": 11}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/helpers/helpers.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/l10n/app_vi.arb": {"language": "JSON", "code": 17, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/helpers/csv.dart": {"language": "Dart", "code": 34, "comment": 0, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/flutter_gen/assets.gen.dart": {"language": "Dart", "code": 71, "comment": 11, "blank": 15}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/helpers/gp_upload_management.dart": {"language": "Dart", "code": 143, "comment": 18, "blank": 41}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/flutter_gen/flutter_gen.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/helpers/jwt_token_decode.dart": {"language": "Dart", "code": 58, "comment": 31, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/app_config/widgets/app_config_body_page.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/app_config/bloc/app_config_event.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/app_config/widgets/widgets.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/app_config/bloc/app_config_bloc.dart": {"language": "Dart", "code": 93, "comment": 0, "blank": 16}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/l10n/l10n.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/app_config/bloc/app_config_state.dart": {"language": "Dart", "code": 17, "comment": 1, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/project_configs/package_rename_config-uat.yaml": {"language": "YAML", "code": 27, "comment": 0, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/app_config/bloc/bloc.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/app_config/bloc/app_config_state.freezed.dart": {"language": "Dart", "code": 118, "comment": 15, "blank": 23}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/macos/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/domain.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/repository/repository.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/entity.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/download_file.usecase.dart": {"language": "Dart", "code": 31, "comment": 0, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/facebook_get_community.usecase.dart": {"language": "Dart", "code": 19, "comment": 10, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/repository/gapo_repo.dart": {"language": "Dart", "code": 15, "comment": 10, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/repository/workplace_repo.dart": {"language": "Dart", "code": 22, "comment": 11, "blank": 12}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/usecase.dart": {"language": "Dart", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/repository/facebook_repo.dart": {"language": "Dart", "code": 4, "comment": 1, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_comment.g.dart": {"language": "Dart", "code": 85, "comment": 4, "blank": 15}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_comment.dart": {"language": "Dart", "code": 70, "comment": 0, "blank": 11}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/feed/feed.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/workplace.dart": {"language": "Dart", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_group.dart": {"language": "Dart", "code": 29, "comment": 0, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/status/base_crawl_status.entity.g.dart": {"language": "Dart", "code": 946, "comment": 8, "blank": 78}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/status/status.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/status/base_crawl_status.entity.dart": {"language": "Dart", "code": 60, "comment": 12, "blank": 13}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/app/locale_enum.dart": {"language": "Dart", "code": 9, "comment": 0, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_group.g.dart": {"language": "Dart", "code": 39, "comment": 4, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/app/app_config.entity.g.dart": {"language": "Dart", "code": 701, "comment": 6, "blank": 73}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/app/app_config.entity.dart": {"language": "Dart", "code": 29, "comment": 9, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_message.dart": {"language": "Dart", "code": 23, "comment": 0, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_attachement.dart": {"language": "Dart", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.g.dart": {"language": "Dart", "code": 19, "comment": 4, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_group_feed.entity.dart": {"language": "Dart", "code": 0, "comment": 27, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_message.g.dart": {"language": "Dart", "code": 23, "comment": 4, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_base_feed.entity.dart": {"language": "Dart", "code": 45, "comment": 10, "blank": 15}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_base_feed.entity.g.dart": {"language": "Dart", "code": 1700, "comment": 6, "blank": 171}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.dart": {"language": "Dart", "code": 21, "comment": 0, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_post.g.dart": {"language": "Dart", "code": 140, "comment": 4, "blank": 16}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_post.dart": {"language": "Dart", "code": 121, "comment": 0, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/app/app.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_thread.dart": {"language": "Dart", "code": 21, "comment": 0, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/feed/workplace_user_feed.entity.dart": {"language": "Dart", "code": 0, "comment": 28, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gp_thread.g.dart": {"language": "Dart", "code": 21, "comment": 4, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/gapo.dart": {"language": "Dart", "code": 9, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/user/user.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/thread/thread.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/base_crawl.entity.dart": {"language": "Dart", "code": 144, "comment": 10, "blank": 21}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/assets/images/splash_loading_1.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/base.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/other/other.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/feed_attachment.usecase.dart": {"language": "Dart", "code": 0, "comment": 89, "blank": 12}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/repository/download_repo.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/user/workplace_community_member.entity.dart": {"language": "Dart", "code": 32, "comment": 12, "blank": 11}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/thread/workplace_conversations.entity.g.dart": {"language": "Dart", "code": 2309, "comment": 8, "blank": 228}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/log/log.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/user/workplace_community_member.entity.g.dart": {"language": "Dart", "code": 2237, "comment": 6, "blank": 219}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/other/workplace_comment.entity.g.dart": {"language": "Dart", "code": 2600, "comment": 13, "blank": 224}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/log/base_crawl_log.entity.g.dart": {"language": "Dart", "code": 1010, "comment": 6, "blank": 87}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/other/workplace_comment.entity.dart": {"language": "Dart", "code": 65, "comment": 9, "blank": 17}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/group/group.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.g.dart": {"language": "Dart", "code": 2907, "comment": 13, "blank": 247}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/thread/workplace_conversations.entity.dart": {"language": "Dart", "code": 53, "comment": 34, "blank": 21}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace.dart": {"language": "Dart", "code": 9, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/other/workplace_attachment.entity.dart": {"language": "Dart", "code": 69, "comment": 59, "blank": 35}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/base/log/base_crawl_log.entity.dart": {"language": "Dart", "code": 22, "comment": 9, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/thread/workplace_conversation_attachment.entity.dart": {"language": "Dart", "code": 61, "comment": 9, "blank": 13}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/other/workplace_attachment.entity.g.dart": {"language": "Dart", "code": 2471, "comment": 15, "blank": 220}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/callback/callback.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/group/workplace_group.entity.dart": {"language": "Dart", "code": 52, "comment": 9, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/workplace/group/workplace_group.entity.g.dart": {"language": "Dart", "code": 2573, "comment": 11, "blank": 245}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace_get_user_feeds.usecase.dart": {"language": "Dart", "code": 0, "comment": 37, "blank": 8}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/di.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/component/app.component.config.dart": {"language": "Dart", "code": 316, "comment": 8, "blank": 12}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_upload.usecase.dart": {"language": "Dart", "code": 41, "comment": 10, "blank": 13}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace_get_community_members.usecase.dart": {"language": "Dart", "code": 37, "comment": 10, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/modules/auth.module.dart": {"language": "Dart", "code": 17, "comment": 11, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/repository/repository.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/modules/app.module.dart": {"language": "Dart", "code": 26, "comment": 9, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/modules/client.module.dart": {"language": "Dart", "code": 44, "comment": 9, "blank": 12}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/repository/facebook_repo_impl.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/repository/gapo_impl.dart": {"language": "Dart", "code": 75, "comment": 10, "blank": 11}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/repository/workplace_repo_impl.dart": {"language": "Dart", "code": 61, "comment": 10, "blank": 14}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/modules/url.module.dart": {"language": "Dart", "code": 26, "comment": 12, "blank": 8}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/modules/navigator.module.dart": {"language": "Dart", "code": 17, "comment": 10, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/component/component.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/modules/modules.dart": {"language": "Dart", "code": 6, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.g.dart": {"language": "Dart", "code": 315, "comment": 5, "blank": 20}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/auth.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/download/download.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/facebook.service.dart": {"language": "Dart", "code": 20, "comment": 10, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/download/download.service.dart": {"language": "Dart", "code": 32, "comment": 0, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/upload_file_response.dart": {"language": "Dart", "code": 18, "comment": 0, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth_reponse.g.dart": {"language": "Dart", "code": 6, "comment": 4, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/gpw.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/response/auth_check_mail_response.g.dart": {"language": "Dart", "code": 15, "comment": 4, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/request/auth_check_email_request.dart": {"language": "Dart", "code": 16, "comment": 9, "blank": 8}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/request/request.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/response/auth_check_mail_response.dart": {"language": "Dart", "code": 18, "comment": 9, "blank": 8}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_signup_params.g.dart": {"language": "Dart", "code": 10, "comment": 4, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_signup_params.freezed.dart": {"language": "Dart", "code": 205, "comment": 15, "blank": 23}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_signup_params.dart": {"language": "Dart", "code": 21, "comment": 1, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_auth_params.g.dart": {"language": "Dart", "code": 10, "comment": 4, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_auth_params.freezed.dart": {"language": "Dart", "code": 226, "comment": 15, "blank": 23}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_auth_params.dart": {"language": "Dart", "code": 18, "comment": 1, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/request/auth_check_email_request.g.dart": {"language": "Dart", "code": 13, "comment": 4, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth_reponse.dart": {"language": "Dart", "code": 17, "comment": 0, "blank": 8}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/gpw/auth/response/response.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/datetime_converter.dart": {"language": "Dart", "code": 11, "comment": 0, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/download/download_params.freezed.dart": {"language": "Dart", "code": 118, "comment": 15, "blank": 23}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/download/download_params.dart": {"language": "Dart", "code": 13, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/conversation/conversation.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/model.dart": {"language": "Dart", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/comment/comment.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/comment/message_tags.dart": {"language": "Dart", "code": 21, "comment": 0, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversations_response.dart": {"language": "Dart", "code": 40, "comment": 0, "blank": 13}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversations_response.g.dart": {"language": "Dart", "code": 35, "comment": 4, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/base/workplace_generic_data_converter.dart": {"language": "Dart", "code": 48, "comment": 0, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/base/base.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.dart": {"language": "Dart", "code": 63, "comment": 0, "blank": 13}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/comment/workplace_reactions.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/comment/message_tags.g.dart": {"language": "Dart", "code": 27, "comment": 4, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart": {"language": "Dart", "code": 56, "comment": 0, "blank": 12}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_bloc.dart": {"language": "Dart", "code": 125, "comment": 0, "blank": 30}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/post/post.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.g.dart": {"language": "Dart", "code": 34, "comment": 4, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/base/workplace_paging_response.g.dart": {"language": "Dart", "code": 17, "comment": 4, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/base/workplace_list_response.g.dart": {"language": "Dart", "code": 12, "comment": 4, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/workplace.dart": {"language": "Dart", "code": 6, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.dart": {"language": "Dart", "code": 31, "comment": 0, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.g.dart": {"language": "Dart", "code": 34, "comment": 4, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/base/workplace_paging_response.dart": {"language": "Dart", "code": 26, "comment": 0, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.dart": {"language": "Dart", "code": 30, "comment": 3, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace_get_post_comments.usecase.dart": {"language": "Dart", "code": 27, "comment": 10, "blank": 8}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/repository/download_impl.dart": {"language": "Dart", "code": 17, "comment": 0, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/comment/workplace_reactions.g.dart": {"language": "Dart", "code": 24, "comment": 4, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart": {"language": "Dart", "code": 59, "comment": 4, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/download/download.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.g.dart": {"language": "Dart", "code": 27, "comment": 4, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/facebook/facebook.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/facebook/community_response.g.dart": {"language": "Dart", "code": 8, "comment": 4, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/facebook/community_response.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.dart": {"language": "Dart", "code": 67, "comment": 10, "blank": 14}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/facebook.service.g.dart": {"language": "Dart", "code": 60, "comment": 5, "blank": 13}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/remote.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/enums/workplace_enums.dart": {"language": "Dart", "code": 53, "comment": 0, "blank": 17}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/base/workplace_list_response.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.g.dart": {"language": "Dart", "code": 41, "comment": 4, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/group/group.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.dart": {"language": "Dart", "code": 46, "comment": 0, "blank": 11}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/enums/enums.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/group/workplace_feeds_response.g.dart": {"language": "Dart", "code": 37, "comment": 4, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/gapo/upload.service.g.dart": {"language": "Dart", "code": 162, "comment": 5, "blank": 15}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/gapo/auth.service.dart": {"language": "Dart", "code": 29, "comment": 9, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/gapo/upload.service.dart": {"language": "Dart", "code": 43, "comment": 16, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/gapo/gapo.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/model/workplace/group/workplace_feeds_response.dart": {"language": "Dart", "code": 29, "comment": 2, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/remote/gapo/auth.service.g.dart": {"language": "Dart", "code": 127, "comment": 5, "blank": 15}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/modules/database.module.dart": {"language": "Dart", "code": 28, "comment": 20, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.freezed.dart": {"language": "Dart", "code": 123, "comment": 15, "blank": 23}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart": {"language": "Dart", "code": 17, "comment": 12, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/callback/gp_upload_callback.dart": {"language": "Dart", "code": 46, "comment": 8, "blank": 13}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/data_source.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/di/component/app.component.dart": {"language": "Dart", "code": 18, "comment": 9, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/upload.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/enums/upload/gp_upload_status_enum.dart": {"language": "Dart", "code": 12, "comment": 0, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace_get_post_attachments.usecase.dart": {"language": "Dart", "code": 27, "comment": 10, "blank": 8}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_login.usecase.dart": {"language": "Dart", "code": 74, "comment": 9, "blank": 15}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/enums/upload/upload.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/local/local.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/enums/gp_enums.dart": {"language": "Dart", "code": 91, "comment": 0, "blank": 12}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace_get_group_members.usecase.dart": {"language": "Dart", "code": 0, "comment": 51, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.freezed.dart": {"language": "Dart", "code": 183, "comment": 27, "blank": 37}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_create_user.usecase.dart": {"language": "Dart", "code": 31, "comment": 9, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/enums/enums.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace_get_group_feeds.usecase.dart": {"language": "Dart", "code": 40, "comment": 10, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_auth_check_mail.usecase.dart": {"language": "Dart", "code": 17, "comment": 9, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/gp_dashboard_upload.entity.dart": {"language": "Dart", "code": 13, "comment": 14, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace_get_conversations.usecase.dart": {"language": "Dart", "code": 40, "comment": 10, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/enums/base_crawl_type.dart": {"language": "Dart", "code": 9, "comment": 16, "blank": 8}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/enums/base_crawl_status_enum.dart": {"language": "Dart", "code": 14, "comment": 30, "blank": 11}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_auth.usecase.dart": {"language": "Dart", "code": 47, "comment": 10, "blank": 14}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/gapo/gapo.dart": {"language": "Dart", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace_get_app_config.usecase.dart": {"language": "Dart", "code": 20, "comment": 10, "blank": 7}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/usecase/workplace/workplace_get_all_groups.usecase.dart": {"language": "Dart", "code": 38, "comment": 10, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/project_configs/package_rename_config-prod.yaml": {"language": "YAML", "code": 27, "comment": 0, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/project_configs/package_rename_config-dev.yaml": {"language": "YAML", "code": 27, "comment": 0, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/params/gp_upload_params.dart": {"language": "Dart", "code": 25, "comment": 2, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/params/gp_upload_repo_params.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/domain/entity/gapo/upload/params/params.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/windows/runner/main.cpp": {"language": "C++", "code": 30, "comment": 4, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/windows/runner/flutter_window.cpp": {"language": "C++", "code": 49, "comment": 7, "blank": 16}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/windows/runner/flutter_window.h": {"language": "C++", "code": 20, "comment": 5, "blank": 9}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/config/config.dart": {"language": "Dart", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/config/app_configs.dart": {"language": "Dart", "code": 29, "comment": 0, "blank": 11}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/windows/runner/resource.h": {"language": "C++", "code": 9, "comment": 6, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/config/bootstrap.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/windows/runner/utils.h": {"language": "C++", "code": 8, "comment": 6, "blank": 6}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/windows/runner/win32_window.cpp": {"language": "C++", "code": 210, "comment": 24, "blank": 55}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/windows/runner/utils.cpp": {"language": "C++", "code": 54, "comment": 2, "blank": 10}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/macos/Runner/AppDelegate.swift": {"language": "Swift", "code": 8, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/macos/Runner/Base.lproj/MainMenu.xib": {"language": "XML", "code": 343, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/windows/runner/win32_window.h": {"language": "C++", "code": 48, "comment": 31, "blank": 24}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/build.yaml": {"language": "YAML", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/project_configs/flutter_launcher_icons-uat.yaml": {"language": "YAML", "code": 17, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/macos/Runner/MainFlutterWindow.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/analysis_options.yaml": {"language": "YAML", "code": 7, "comment": 18, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 68, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/settings.gradle": {"language": "Groovy", "code": 16, "comment": 0, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/gradle.properties": {"language": "Java Properties", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/build.gradle": {"language": "Groovy", "code": 27, "comment": 0, "blank": 5}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/Base.lproj/Main.storyboard": {"language": "XML", "code": 25, "comment": 1, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"language": "XML", "code": 36, "comment": 1, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/gradle/wrapper/gradle-wrapper.properties": {"language": "Java Properties", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md": {"language": "<PERSON><PERSON>", "code": 3, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json": {"language": "JSON", "code": 23, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 122, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon-prod.appiconset/Contents.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon-uat.appiconset/Contents.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/Runner-Bridging-Header.h": {"language": "C++", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/Assets.xcassets/AppIcon-dev.appiconset/Contents.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/ios/Runner/AppDelegate.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/app/src/debug/AndroidManifest.xml": {"language": "XML", "code": 7, "comment": 4, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/app/src/main/AndroidManifest.xml": {"language": "XML", "code": 32, "comment": 6, "blank": 0}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/app/src/main/kotlin/vn/gapowork/crawler/MainActivity.kt": {"language": "<PERSON><PERSON><PERSON>", "code": 4, "comment": 0, "blank": 3}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/app/src/main/res/values/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/app/src/main/res/values-night/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/app/src/main/res/drawable-v21/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/app/src/main/res/drawable/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/android/app/build.gradle": {"language": "Groovy", "code": 51, "comment": 5, "blank": 12}, "file:///c%3A/Softwares/Gapo/gapoflutter-crawler/lib/data/data_source/local/workplace_local.service.dart": {"language": "Dart", "code": 180, "comment": 9, "blank": 32}}