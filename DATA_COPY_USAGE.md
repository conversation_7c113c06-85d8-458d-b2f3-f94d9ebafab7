# Hướng dẫn sử dụng DataCopyHelper

## M<PERSON> tả
`DataCopyHelper` là một utility class giúp copy files từ thư mục `data` trong project đến thư mục `gpdata` trong thư mục Documents của máy tính. Nếu file đã tồn tại thì sẽ bỏ qua.

## Cách sử dụng

### 1. Import class
```dart
import 'helpers/data_copy_helper.dart';
```

### 2. Copy tất cả files từ thư mục data
```dart
// Copy tất cả files và folders từ thư mục data đến gpdata
await DataCopyHelper.copyDataToDocuments();
```

### 3. Copy một file cụ thể
```dart
// Copy file computer_ids.txt từ data đến gpdata
await DataCopyHelper.copySpecificFile('computer_ids.txt');
```

### 4. <PERSON><PERSON><PERSON> tra thư mục gpdata có tồn tại không
```dart
bool exists = await DataCopyHelper.isGpDataExists();
if (exists) {
  print('Thư mục gpdata đã tồn tại');
} else {
  print('Thư mục gpdata chưa tồn tại');
}
```

### 5. Lấy đường dẫn đến thư mục gpdata
```dart
String path = await DataCopyHelper.getGpDataPath();
print('Đường dẫn gpdata: $path');
```

### 6. Xóa thư mục gpdata
```dart
// Xóa thư mục gpdata và tất cả nội dung bên trong
await DataCopyHelper.deleteGpData();
```

## Sử dụng trong main function

Để tự động copy data khi khởi động app, bạn có thể uncomment dòng trong `main.production.dart`:

```dart
void main() async {
  await initApp(AppConfigProd());

  runApp(const GPCrawlerApp());

  _logIndex();

  _updateTotalDownloadSize().then((value) => _updateTotalUploadSize());
  
  // Uncomment dòng dưới để copy database khi khởi động app
  await _copyDatabase(); // <- Uncomment dòng này
}
```

## Cấu trúc thư mục

### Thư mục nguồn (data):
```
data/
├── base/
│   ├── default.isar
│   └── default.isar-lck
├── gQ9pMpzI0SO8j8MX/
│   ├── groups.txt
│   ├── threads.txt
│   └── users.txt
└── computer_ids.txt
```

### Thư mục đích (gpdata trong Documents):
```
~/Documents/gpdata/
├── base/
│   ├── default.isar
│   └── default.isar-lck
├── gQ9pMpzI0SO8j8MX/
│   ├── groups.txt
│   ├── threads.txt
│   └── users.txt
└── computer_ids.txt
```

## Lưu ý

1. **Bỏ qua file đã tồn tại**: Nếu file đã tồn tại trong thư mục đích, nó sẽ được bỏ qua và không bị ghi đè.

2. **Tạo thư mục tự động**: Thư mục `gpdata` sẽ được tạo tự động nếu chưa tồn tại.

3. **Copy đệ quy**: Tất cả thư mục con và files bên trong sẽ được copy một cách đệ quy.

4. **Error handling**: Tất cả các method đều có xử lý lỗi và in ra console để debug.

5. **Platform support**: Hoạt động trên tất cả các platform mà Flutter hỗ trợ (iOS, Android, macOS, Windows, Linux).

## Ví dụ sử dụng trong UI

Xem file `lib/examples/data_copy_example.dart` để biết cách tích hợp vào UI với các button và hiển thị trạng thái.
