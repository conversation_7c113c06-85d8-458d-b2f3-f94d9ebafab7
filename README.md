# gp_fbwp_crawler

## Tasks

### UI

- <PERSON><PERSON><PERSON>ình cho nhập token, cho phép users chọn các loại dữ liệu cần đồng bộ:
    - Chat
    - Members
        - Post
            - Comment
    - Group
    - Post
        - Comment
    - Attachments: đ<PERSON><PERSON><PERSON> gắn với post, comment, chat.
- <PERSON><PERSON><PERSON> hình thể hiện progress: đã sync được những gì…?
- Deploy to web.

### Data - domain:

- **Clone data:** clone data từ Facebook WorkPlace → Client: data gồm toàn bộ những phần users đã chọn.
- **Storage**: lưu trữ data đã đồng bộ vào local database.
- **Mapper**:
    - map data lưu trong database về entity để tạo ở phía GapoWork.
    - đánh dấu các data không thể map.
- **Api**: gọi các api tương ứng để tạo data phía GapoWork (*ws_id lấy từ 1 api ở GapoWork*)
- Testing:
    - unit test for Map<PERSON>.

## Commands:

- For generating locales:
```sh
flutter gen-l10n
```
or run `Command: Generate l10n` from launch

- For generating model, data, mapper...:
```sh
dart run build_runner build --delete-conflicting-outputs
```
or run `Command: Generate data, domain` from launch

- For generating assets...:
```sh
fluttergen
```
or run `Command: Generate data, domain` from launch
or run `make gen`

## Multi client flow

 1. Chạy file init_env.sh để tạo id tại tất cả các máy (init_env.sh)
 2. Tại máy chính kéo dữ liệu của user và group. Chia danh sách user id và group id thành các file nhỏ theo id của máy được đọc trong file computer_ids.txt. Push các file đã đc chia (copy_splited_data.sh)
 3. Copy database vào project folder và đẩy lên git (copy_base_db.sh)
 4. Tại mỗi máy copy database và file chia user và group từ project folder về thư mục tại local (copy_data_to_local.sh)
 5. Mỗi máy đọc file danh sách user, group theo file đã được chia theo computer id, lấy bài post, nhóm chat, tin nhắn theo user, group id đã đọc từ file
 6. Copy db tại local vào project folder thư mục "data/{computer_id}" sau đó push lên git (copy_db.sh)
 7. Tại máy chính pull các db về sau đó merge dữ liệu từ db các máy khác (chạy file merge_db.sh trước)
