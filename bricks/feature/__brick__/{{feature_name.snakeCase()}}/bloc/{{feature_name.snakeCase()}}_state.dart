import 'package:gp_core_v2/base/bloc/bloc.dart';
{{#use_frezeed}}
import 'package:freezed_annotation/freezed_annotation.dart';

part '{{feature_name.snakeCase()}}_state.freezed.dart';

@Freezed(
  copyWith: true,
  equal: true,
  fromJson: false,
  toJson: false,
  toStringOverride: true,
)
/// State của {{feature_name.pascalCase()}}
class {{feature_name.pascalCase()}}State extends CoreV2BaseState
    with _${{feature_name.pascalCase()}}State {
  const factory {{feature_name.pascalCase()}}State() = _{{feature_name.pascalCase()}}State;
}
{{/use_frezeed}}
{{^use_frezeed}}
/// State của `{{feature_name.pascalCase()}}State`
final class {{feature_name.pascalCase()}}State extends CoreV2BaseState {
  const {{feature_name.pascalCase()}}State();
}
{{/use_frezeed}}