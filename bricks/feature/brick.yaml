name: gp_brick_feature
description: Gapo Flutter brick created with the Mason CLI.
version: 0.0.1
repository: 'https://github.com/toannmdev/flutter_architexture'

environment:
  mason: ">=0.1.0-dev.52 <0.1.0"

# Variables specify dynamic values that your brick depends on.
# Zero or more variables can be specified for a given brick.
# Each variable has:
#  * a type (string, number, boolean, enum, or array)
#  * an optional short description
#  * an optional default value
#  * an optional list of default values (array only)
#  * an optional prompt phrase used when asking for the variable
#  * a list of values (enums only)
vars:
  feature_name:
    type: string
    description: Your feature name
    default: login
    prompt: What is your feature name?
  use_frezeed:
    type: boolean
    description: Use frezeed for state
    default: false
    prompt: Use frezeed for state?