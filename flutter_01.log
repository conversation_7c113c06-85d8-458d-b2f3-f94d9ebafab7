Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter assemble --no-version-check --output=build -dTargetPlatform=windows-x64 -dTrackWidgetCreation=true -dBuildMode=debug -dTargetFile=C:\Softwares\gapoflutter-crawler\lib/main.production.dart -dTreeShakeIcons="false" -dDartObfuscation=false --DartDefines=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9hNzk0Y2YyNjgxYzZjOWZlN2IyNjBlMGU4NGRlOTYyOThkYzljMThiLw== debug_bundle_windows_assets

## exception

PathAccessException: PathAccessException: Cannot delete file, path = 'C:\Softwares\gapoflutter-crawler\build\flutter_assets\packages/gp_assets/images/3.0x/check-w.png' (OS Error: Access is denied.
, errno = 5)

```
#0      _File.throwIfError (dart:io/file_impl.dart:675:7)
#1      _File._deleteSync (dart:io/file_impl.dart:329:5)
#2      FileSystemEntity.deleteSync (dart:io/file_system_entity.dart:424:7)
#3      ForwardingFileSystemEntity.deleteSync (package:file/src/forwarding/forwarding_file_system_entity.dart:70:16)
#4      ForwardingFileSystemEntity.deleteSync (package:file/src/forwarding/forwarding_file_system_entity.dart:70:16)
#5      ErrorHandlingFileSystem.deleteIfExists (package:flutter_tools/src/base/error_handling_io.dart:80:12)
#6      FlutterBuildSystem.trackSharedBuildDirectory (package:flutter_tools/src/build_system/build_system.dart:756:33)
#7      FlutterBuildSystem.build (package:flutter_tools/src/build_system/build_system.dart:644:5)
<asynchronous suspension>
#8      AssembleCommand.runCommand (package:flutter_tools/src/commands/assemble.dart:315:32)
<asynchronous suspension>
#9      FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1297:27)
<asynchronous suspension>
#10     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:150:19)
<asynchronous suspension>
#11     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#12     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:339:9)
<asynchronous suspension>
#13     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:150:19)
<asynchronous suspension>
#14     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:285:5)
<asynchronous suspension>
#15     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:115:9)
<asynchronous suspension>
#16     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:150:19)
<asynchronous suspension>
#17     main (package:flutter_tools/executable.dart:90:3)
<asynchronous suspension>
```

## flutter doctor

```
[!] Flutter (Channel [user-branch], 3.13.6, on Microsoft Windows [Version 10.0.19044.3086], locale en-US)
    ! Flutter version 3.13.6 on channel [user-branch] at C:\Softwares\flutter
      Currently on an unknown channel. Run `flutter channel` to switch to an official channel.
      If that doesn't fix the issue, reinstall Flutter by following instructions at https://flutter.dev/docs/get-started/install.
    ! Upstream repository unknown source is not a standard remote.
      Set environment variable "FLUTTER_GIT_URL" to unknown source to dismiss this error.
    • Framework revision ead455963c (1 year, 5 months ago), 2023-09-26 18:28:17 -0700
    • Engine revision a794cf2681
    • Dart version 3.1.3
    • DevTools version 2.25.0
    • If those were intentional, you can disregard the above warnings; however it is recommended to use "git" directly to perform update checks and upgrades.

[✓] Windows Version (Installed version of Windows is version 10 or higher)

[✗] Android toolchain - develop for Android devices
    ✗ Unable to locate Android SDK.
      Install Android Studio from: https://developer.android.com/studio/index.html
      On first launch it will assist you in installing the Android SDK components.
      (or visit https://flutter.dev/docs/get-started/install/windows#android-setup for detailed instructions).
      If the Android SDK has been installed to a custom location, please use
      `flutter config --android-sdk` to update to that location.


[✓] Chrome - develop for the web
    • Chrome at C:\Program Files\Google\Chrome\Application\chrome.exe

[✓] Visual Studio - develop Windows apps (Visual Studio Community 2022 17.11.5)
    • Visual Studio at C:\Program Files\Microsoft Visual Studio\2022\Community
    • Visual Studio Community 2022 version 17.11.35327.3
    • Windows 10 SDK version 10.0.22621.0

[!] Android Studio (not installed)
    • Android Studio not found; download from https://developer.android.com/studio/index.html
      (or visit https://flutter.dev/docs/get-started/install/windows#android-setup for detailed instructions).

[✓] VS Code (version 1.96.0)
    • VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
    • Flutter extension version 3.102.0

[✓] Connected device (3 available)
    • Windows (desktop) • windows • windows-x64    • Microsoft Windows [Version 10.0.19044.3086]
    • Chrome (web)      • chrome  • web-javascript • Google Chrome 132.0.6834.83
    • Edge (web)        • edge    • web-javascript • Microsoft Edge 131.0.2903.99

[✓] Network resources
    • All expected network resources are available.

! Doctor found issues in 3 categories.
```
