PODS:
  - app_settings (5.1.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.4):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.4)
  - DKImagePickerController/PhotoGallery (4.3.4):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.4)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - emoji_picker_flutter (0.0.1):
    - Flutter
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_udid (0.0.1):
    - Flutter
    - SAMKeychain
  - flutter_video_info (1.1.0):
    - Flutter
  - flutter_vlc_player (3.0.3):
    - Flutter
    - MobileVLCKit (~> 3.6.0b9)
  - gallery_saver (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - image_save (0.0.1):
    - Flutter
  - isar_flutter_libs (1.0.0):
    - Flutter
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - MobileVLCKit (3.6.0b11)
  - MSAL (1.2.5):
    - MSAL/app-lib (= 1.2.5)
  - MSAL/app-lib (1.2.5)
  - msal_flutter (1.0.2):
    - Flutter
    - MSAL (~> 1.2.0)
  - open_filex (0.0.2):
    - Flutter
  - package_info (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.0.0)
  - SAMKeychain (1.5.3)
  - SDWebImage (5.18.5):
    - SDWebImage/Core (= 5.18.5)
  - SDWebImage/Core (5.18.5)
  - Sentry/HybridSDK (8.15.2):
    - SentryPrivate (= 8.15.2)
  - sentry_flutter (0.0.1):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.15.2)
  - SentryPrivate (8.15.2)
  - share (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.4)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player (0.0.1):
    - Flutter
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - emoji_picker_flutter (from `.symlinks/plugins/emoji_picker_flutter/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_udid (from `.symlinks/plugins/flutter_udid/ios`)
  - flutter_video_info (from `.symlinks/plugins/flutter_video_info/ios`)
  - flutter_vlc_player (from `.symlinks/plugins/flutter_vlc_player/ios`)
  - gallery_saver (from `.symlinks/plugins/gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - image_save (from `.symlinks/plugins/image_save/ios`)
  - isar_flutter_libs (from `.symlinks/plugins/isar_flutter_libs/ios`)
  - msal_flutter (from `.symlinks/plugins/msal_flutter/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share (from `.symlinks/plugins/share/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player (from `.symlinks/plugins/video_player/ios`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - libwebp
    - MobileVLCKit
    - MSAL
    - ReachabilitySwift
    - SAMKeychain
    - SDWebImage
    - Sentry
    - SentryPrivate
    - SwiftyGif

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  emoji_picker_flutter:
    :path: ".symlinks/plugins/emoji_picker_flutter/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_udid:
    :path: ".symlinks/plugins/flutter_udid/ios"
  flutter_video_info:
    :path: ".symlinks/plugins/flutter_video_info/ios"
  flutter_vlc_player:
    :path: ".symlinks/plugins/flutter_vlc_player/ios"
  gallery_saver:
    :path: ".symlinks/plugins/gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  image_save:
    :path: ".symlinks/plugins/image_save/ios"
  isar_flutter_libs:
    :path: ".symlinks/plugins/isar_flutter_libs/ios"
  msal_flutter:
    :path: ".symlinks/plugins/msal_flutter/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share:
    :path: ".symlinks/plugins/share/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player:
    :path: ".symlinks/plugins/video_player/ios"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"

SPEC CHECKSUMS:
  app_settings: 017320c6a680cdc94c799949d95b84cb69389ebc
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  device_info_plus: 7545d84d8d1b896cb16a4ff98c19f07ec4b298ea
  DKImagePickerController: b512c28220a2b8ac7419f21c491fc8534b7601ac
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  emoji_picker_flutter: fe2e6151c5b548e975d546e6eeb567daf0962a58
  file_picker: 09aa5ec1ab24135ccd7a1621c46c84134bfd6655
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  flutter_udid: a2482c67a61b9c806ef59dd82ed8d007f1b7ac04
  flutter_video_info: a932e1b0da369bba95c3a14b7f61d5f8b0d03280
  flutter_vlc_player: 2e318881576450ff081eb023d47c289f2c6681eb
  gallery_saver: 9fc173c9f4fcc48af53b2a9ebea1b643255be542
  image_picker_ios: 99dfe1854b4fa34d0364e74a78448a0151025425
  image_save: 66ab41fa2c60bc9569e55563ea287cced7b79fd1
  isar_flutter_libs: b69f437aeab9c521821c3f376198c4371fa21073
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  MobileVLCKit: d20b1e96a93446d97abd377a1daa230202147619
  MSAL: 5149daaa19228c2c27d81987634da15b50981cef
  msal_flutter: 6affbfc5214d05ab2654adadd0c6f11714c57ddf
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  pasteboard: 982969ebaa7c78af3e6cc7761e8f5e77565d9ce0
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  SAMKeychain: 483e1c9f32984d50ca961e26818a534283b4cd5c
  SDWebImage: 7ac2b7ddc5e8484c79aa90fc4e30b149d6a2c88f
  Sentry: 6f5742b4c47c17c9adcf265f6f328cf4a0ed1923
  sentry_flutter: 2c309a1d4b45e59d02cfa15795705687f1e2081b
  SentryPrivate: b2f7996f37781080f04a946eb4e377ff63c64195
  share: 0b2c3e82132f5888bccca3351c504d0003b3b410
  share_plus: c3fef564749587fc939ef86ffb283ceac0baf9f5
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  SwiftyGif: 93a1cc87bf3a51916001cf8f3d63835fb64c819f
  url_launcher_ios: bbd758c6e7f9fd7b5b1d4cde34d2b95fcce5e812
  video_player: 9cc823b1d9da7e8427ee591e8438bfbcde500e6e
  video_thumbnail: c4e2a3c539e247d4de13cd545344fd2d26ffafd1

PODFILE CHECKSUM: 7be2f5f74864d463a8ad433546ed1de7e0f29aef

COCOAPODS: 1.13.0
