import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/base.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';

import 'app_config_event.dart';
import 'app_config_state.dart';

@Singleton()
final class AppConfigBloc
    extends CoreV2BaseBloc<CoreV2BaseEvent, AppConfigState>
    with _VariablesMixin {
  AppConfigBloc()
      : super(AppConfigState(
          appConfigEntity: AppConfigEntity(
            appLocale: GPAppLocale.vi,
            appVersion: '',
          ),
        )) {
    on<AppConfigInitialEvent>(
      _onAppConfigDefaultEvent,
    );

    on<AppConfigUpdateEvent>(
      _onAppConfigUpdateEvent,
    );

    on<AppConfigLanguageChangedEvent>(
      _onAppConfigLanguageChangedEvent,
    );
  }

  FutureOr _onAppConfigDefaultEvent(
    AppConfigInitialEvent event,
    Emitter<AppConfigState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        final AppConfigEntity appConfigEntity = await appConfigUseCase.execute(
          const WorkPlaceAppConfigInput(),
        );

        emit(
          state.copyWith(
            isLoading: false,
            appConfigEntity: appConfigEntity,
          ),
        );
      },
    );
  }

  FutureOr _onAppConfigLanguageChangedEvent(
    AppConfigLanguageChangedEvent event,
    Emitter<AppConfigState> emit,
  ) async {
    await _saveAppConfigEntitysToStorage();

    final newAppConfigEntity = state.appConfigEntity.copyWith(
      appLocale: event.locale,
    );
    emit(
      state.copyWith(
        appConfigEntity: newAppConfigEntity,
      ),
    );
  }

  FutureOr _onAppConfigUpdateEvent(
    AppConfigUpdateEvent event,
    Emitter<AppConfigState> emit,
  ) async {
    String token = event.token;

    if (token.isEmpty) {
      token =
          "DQWRJRzY1S3dMNWpIdWpmNk9BZAFR6RWw4cHM5UjhpaHdqenlZAZAEdFWnVDUFFidDRtRk5hdDRCZATkxQTNZAM1B6blN4NEkzV01zZAkpLZAGxic2VCYmZAtSzVrT3VOallxNkhGZAHU5ZAnE0Q05jaW9ycTNqX2wwUFlXOXRIVjdMRUc0S0VVRFphdHdkbG5od3RpVFhzM3pKWmU2bWVPRXJjNFNRLVdoRkhHajlzaVpKMVRoak5USE1MRlRwM09MN0luclQ3TjZAERGVQNEY2ZAUdreXBTX0d3";
    }

    final appConfigEntity = state.appConfigEntity.copyWith(
      appVersion: '1.0.0',
      workPlaceToken: token,
    );
    await isar.writeTxn(() async {
      await isar.appConfigEntitys.put(appConfigEntity);
    });
  }

  Future _saveAppConfigEntitysToStorage() async {
    await isar.writeTxn(() async {
      await isar.appConfigEntitys.put(state.appConfigEntity);
    });
  }
}

mixin _VariablesMixin {
  late final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');

  late final WorkPlaceGetAppConfigUseCase appConfigUseCase =
      GetIt.I<WorkPlaceGetAppConfigUseCase>();
}
