import 'package:gp_core_v2/base/bloc/bloc.dart';
import 'package:gp_fbwp_crawler/domain/entity/base/app/locale_enum.dart';

final class AppConfigEvent extends CoreV2BaseEvent {
  const AppConfigEvent();
}

final class AppConfigInitialEvent extends CoreV2BaseEvent {
  const AppConfigInitialEvent();
}

final class AppConfigLanguageChangedEvent extends CoreV2BaseEvent {
  const AppConfigLanguageChangedEvent({required this.locale});
  final GPAppLocale locale;
}

final class AppConfigUpdateEvent extends CoreV2BaseEvent {
  const AppConfigUpdateEvent({required this.token});
  final String token;
}
