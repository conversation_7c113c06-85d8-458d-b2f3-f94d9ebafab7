import 'package:gp_core_v2/base/bloc/bloc.dart';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_fbwp_crawler/domain/entity/base/app/app_config.entity.dart';

part 'app_config_state.freezed.dart';

@Freezed(
  copyWith: true,
  equal: true,
  fromJson: false,
  toJson: false,
  toStringOverride: true,
)

/// State của AppConfig
class AppConfigState extends CoreV2BaseState with _$AppConfigState {
  const factory AppConfigState({
    @Default(true) bool isLoading,
    required AppConfigEntity appConfigEntity,
  }) = _AppConfigState;
}
