import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:isar/isar.dart';

import '../../../../domain/entity/base/log/log.entity.dart';
import '../curl_ext.dart';

class ErrorLoggerInterceptor extends Interceptor {
  ErrorLoggerInterceptor();

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    try {
      final cUrlStr = err.requestOptions.cURLRepresentationRaw();

      String path = err.requestOptions.path;
      if (path.contains('/')) {
        path = '/${path.split('/').last}';
      }

      final logType = GPLogType.values.firstWhereOrNull((element) {
        return element.paths.contains(path);
      });

      final String errorStr = (err.error ?? err.message).toString();

      final params = err.requestOptions.queryParameters;
      params.removeWhere(
          (key, value) => key == 'limit' || key == 'after' || key == 'fields');
      final data =
          (err.requestOptions.data ?? '${err.requestOptions.path}?$params')
              .toString();

      final resp = err.response?.data.toString();

      await _saveLog(GPLogEntity(
        cUrl: cUrlStr,
        path: path,
        error: errorStr,
        createdAt: DateTime.now(),
        logType: logType ?? GPLogType.other,
        data: "$data || Response: $resp",
      ));
    } catch (ex) {
      logDebug('ErrorLoggerInterceptor onError ex -> $ex');
    }

    handler.next(err);
  }
}

final Isar _isar = GetIt.I<Isar>(instanceName: 'kIsar');

Future _saveLog(GPLogEntity logEntity) async {
  await _isar.writeTxn(() async {
    final result = await _isar.gPLogEntitys.put(logEntity);
    logDebug('_saveLog with id = $result');
  });
}
