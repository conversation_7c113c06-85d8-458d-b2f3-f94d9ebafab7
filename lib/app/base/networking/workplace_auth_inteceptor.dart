import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_fbwp_crawler/domain/entity/base/app/app_config.entity.dart';
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_app_config.usecase.dart';

class WorkPlaceAuthenticationInterceptor extends Interceptor {
  const WorkPlaceAuthenticationInterceptor();

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    options.headers.addAll(await authHeaders());

    handler.next(options);
  }
}

Future<Map<String, String>> authHeaders() async {
  final AppConfigEntity appConfigEntity =
      await GetIt.I<WorkPlaceGetAppConfigUseCase>()
          .execute(const WorkPlaceAppConfigInput());

  Map<String, String> headers = {};

  if (appConfigEntity.workPlaceToken?.isNotEmpty == true) {
    headers.addAll({
      "Authorization": "Bearer ${appConfigEntity.workPlaceToken}",
      "Accept": "application/json",
      "content-type": "application/json",
    });
  }

  return headers;
}
