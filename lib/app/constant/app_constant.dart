import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

class AppConstants {
  /// Các thread import sẽ được đánh dấu đã đọc
  static bool isReadConversation = true;
  static bool needRunOnGapoWork = true;
  static bool deleteUploadedLocalFile = true;
  // static String kDefaultPassword = 'Gapo@123';
  /// Bật nếu sử dụng đăng nhập SSO
  static bool useRandomPassword = false;
  static bool runInMultiClients = true;
  static bool isMainComputer = true;
  static bool showDashboard = true;
  static String computerId = '';
  static String pcName = '';
  static String listIdFilePath = "data/computer_ids.txt";
  static String databaseName = "ezlaw";
  static String baseDatabaseName = "base";
  static String resourceFolderName = "ezlaw-resources";
  static String workspaceId = "585099952973624";

  /// post time > since
  static DateTime? getFeedSinceTime;

  /// until > post time
  static DateTime? getFeedUntilTime;

  /// lấy message từ hiện tại đến thời gian này
  static DateTime? getMessageSinceTime;
  static String token =
      "DQWRVVEtHWk8tMTFTYWlkT1A0dVJfdV9WeGY1U2ROZAXhGUjBDZA3ltTWx0SmtfdFN1eVUxRFBIS2IzX2lFTC1ieXFjck1vU2lsQkYwQ3ZAfd0JmWTVsM2J5SmljeGcwU0VmMTJDMko5UTFvakZAqcGZAGN01aR2lqQjRUUy1SMzdXN2JRMDQzeGFqYWtSVUJJamFDZAERuNWVoZAHVyYS13bzlUREM2SmhuSFdvU0lPa0xiR0RRZAmxiSkRWeDhfdy1QM3ZA6VE93NkZA5SG5xZAzhRWjRaT1RHYwZDZD";
  static List<CollectionSchema<dynamic>> dbSchemas = [
    // base
    AppConfigEntitySchema,
    GPBaseCrawlLogEntitySchema,
    CrawlCheckpointSchema,
    // member
    WorkPlaceCommunityMemberEntitySchema,
    // group
    WorkPlaceGroupEntitySchema,
    // feed
    WorkPlaceFeedEntitySchema,
    // thread
    WorkPlaceConversationEntitySchema,
    WorkPlaceMessagesEntitySchema,
    WorkPlaceConversationAttachmentsEntitySchema,
    // other
    WorkPlaceAttachmentEntitySchema,
    WorkPlaceCommentEntitySchema,
    WorkPlaceStickerEntitySchema,
    // community
    WorkPlaceCommunityEntitySchema,
    GPLogEntitySchema,
    GPDashboardEntitySchema,
    // WorkPlaceGroupFeedEntitySchema,
    // WorkPlaceUserConversationsEntitySchema,
    // WorkPlaceUserFeedsEntitySchema,
    // WorkPlaceReactionEntitySchema,
  ];
  // static bool enableEmailFilter = false;

  static List<String> filterEmails = [
    // "<EMAIL>",
    // "<EMAIL>",
  ];

  // static List<String> filterEmails = [
  //   "<EMAIL>"
  // ];

  static List<String> userIds = [
    // '100023834177621'
    // '100076177455357',
    // '100066980774793',
    // '100068131471427',
    // '61559004009084',
    // '100068013916649',
    // '61551606293761',
    // '100067798907123',
  ];

  static final List<String> threadIds = [
    // 't_8293610154052754'
  ];

  static List<String> groupIds = [
    // '472037969048809',
  ];

  static List<String> excludeGroupIds = [
    // '198056230834919',
    // '1838419953114847',
    // '1950249471960131',
  ];
}
