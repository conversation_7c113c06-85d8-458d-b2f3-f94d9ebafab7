/*
 * Created Date: Monday, 10th June 2024, 17:38:58
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 11th June 2024 16:06:51
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

/// GPConstants
final class GPConstants {
  static const String kGapoWorkDomainStag = 'https://staging-api.gapowork.vn';
  static const String kGapoWorkDomainProd = 'https://api.gapowork.vn';
  static const String kGapoWorkDomainUat = 'https://uat-api.gapowork.vn';
  static const String kGapoWorkUploadDomainStag =
      'https://staging-upload.gapowork.vn';
  static const String kGapoWorkUploadDomainUat =
      'https://uat-upload.gapowork.vn';
  static const String kGapoWorkUploadDomainProd = 'https://upload.gapowork.vn';

  // ---------- Upload file: https://gapowork.redoc.ly/tag/Upload-Service_image ---------- \\
  static const String kGapoWorkMediaUrl = '/media/v1.0';
  static const String kGapoWorkFiles = '$kGapoWorkMediaUrl/files';
  static const String kGapoWorkVideos = '$kGapoWorkMediaUrl/videos';
  static const String kGapoWorkImages = '$kGapoWorkMediaUrl/images';
  static const String kGapoWorkAudios = '$kGapoWorkMediaUrl/audios';

  // ---------- Auth  ---------- \\
  static const String kGapoWorkAuthUrl = '/auth/v3.1';
  static const String kGapoWorkEmailPath = '$kGapoWorkAuthUrl/check-email';
  static const String kGapoWorkPhonePath = '$kGapoWorkAuthUrl/check-phone-number';
  static const String kGapoWorkAuthLogin = '$kGapoWorkAuthUrl/login';
  static const String kGapoWorkSignup = '$kGapoWorkAuthUrl/signup-verify-otp';
  static const String kGapoWorkSetUserInfo =
      '$kGapoWorkAuthUrl/signup-complete-user-info';

  // ---------- Workspace  ---------- \\
  static const String kGapoWorkWorkspaceUrl = '/workspace/v1.0';
  static const String kGapoWorkWorkspaceInviteEmail = '$kGapoWorkWorkspaceUrl/invitations/invite-by-email';
  static const String kGapoWorkWorkspaceInvitePhone = '$kGapoWorkWorkspaceUrl/invitations/invite-by-phone';
  // ---------- Group  ---------- \\
  static const String kGapoWorkGroupUrl = '/group/v1.1';
  static const String kGapoWorkGroup = '$kGapoWorkGroupUrl/groups';

  // ---------- Membership  ---------- \\
  static const String kGapoWorkMembershipUrl = '/membership/v1.1';
  static const String kGapoWorkMembershipGroup =
      '$kGapoWorkMembershipUrl/groups';
  static const String kGapoWorkGroupAdjustMemberRole = '/adjust_member_role';
  static const String kGapoWorkGroupLeave = '/leave';

  // ---------- User  ---------- \\
  static const String kGapoWorkUserProfileUrl = '/user-profile/v1.0';
  static const String kGapoWorkUserProfileUpdate = '$kGapoWorkUserProfileUrl/profile';
}
