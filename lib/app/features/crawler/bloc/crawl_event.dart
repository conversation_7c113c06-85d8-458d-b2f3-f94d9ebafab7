import 'package:gp_core_v2/lib.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/bloc/crawl_bloc.dart';

final class CrawlInitialEvent extends CoreV2BaseEvent {
  CrawlInitialEvent({
    required this.queueBlocs,
  });

  final List<CrawlQueueBloc> queueBlocs;
}

final class CrawlGetWorkplaceDataEvent extends CoreV2BaseEvent {
  const CrawlGetWorkplaceDataEvent();
}

final class CrawlExportEvent extends CoreV2BaseEvent {
  const CrawlExportEvent({required this.csv, required this.filename});
  final String csv;
  final String filename;
}
