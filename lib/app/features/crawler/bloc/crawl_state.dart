import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_core_v2/base/bloc/bloc.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/bloc/crawl_bloc.dart';

part 'crawl_state.freezed.dart';

final class CrawlState extends CoreV2BaseState {
  CrawlState({
    required this.queueBlocs,
  });

  final List<CrawlQueueBloc> queueBlocs;
}

class CrawlQueueState extends CoreV2BaseState {
  const CrawlQueueState({
    required this.exportData,
  });

  final WorkPlaceExport exportData;
}

class WorkPlaceExport {
  const WorkPlaceExport({
    required this.queuesInfo,
  });

  final List<WorkPlaceQueueInfo> queuesInfo;
}

@Freezed(
  copyWith: true,
  fromJson: false,
  toJson: false,
)
class WorkPlaceQueueInfo with _$WorkPlaceQueueInfo {
  const factory WorkPlaceQueueInfo({
    /// unique
    @Default('') String message,
    String? csv,
    String? error,
    @Default(1) int level,
    required DateTime time,
    Duration? runningDuration,
    @Default(WPTaskStatus.todo) WPTaskStatus taskStatus,
  }) = _WorkPlaceQueueInfo;
}

enum WPTaskStatus { todo, inProgress, done, error }
