// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'crawl_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WorkPlaceQueueInfo {
  /// unique
  String get message => throw _privateConstructorUsedError;
  String? get csv => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  DateTime get time => throw _privateConstructorUsedError;
  Duration? get runningDuration => throw _privateConstructorUsedError;
  WPTaskStatus get taskStatus => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WorkPlaceQueueInfoCopyWith<WorkPlaceQueueInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkPlaceQueueInfoCopyWith<$Res> {
  factory $WorkPlaceQueueInfoCopyWith(
          WorkPlaceQueueInfo value, $Res Function(WorkPlaceQueueInfo) then) =
      _$WorkPlaceQueueInfoCopyWithImpl<$Res, WorkPlaceQueueInfo>;
  @useResult
  $Res call(
      {String message,
      String? csv,
      String? error,
      int level,
      DateTime time,
      Duration? runningDuration,
      WPTaskStatus taskStatus});
}

/// @nodoc
class _$WorkPlaceQueueInfoCopyWithImpl<$Res, $Val extends WorkPlaceQueueInfo>
    implements $WorkPlaceQueueInfoCopyWith<$Res> {
  _$WorkPlaceQueueInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? csv = freezed,
    Object? error = freezed,
    Object? level = null,
    Object? time = null,
    Object? runningDuration = freezed,
    Object? taskStatus = null,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      csv: freezed == csv
          ? _value.csv
          : csv // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime,
      runningDuration: freezed == runningDuration
          ? _value.runningDuration
          : runningDuration // ignore: cast_nullable_to_non_nullable
              as Duration?,
      taskStatus: null == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as WPTaskStatus,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkPlaceQueueInfoImplCopyWith<$Res>
    implements $WorkPlaceQueueInfoCopyWith<$Res> {
  factory _$$WorkPlaceQueueInfoImplCopyWith(_$WorkPlaceQueueInfoImpl value,
          $Res Function(_$WorkPlaceQueueInfoImpl) then) =
      __$$WorkPlaceQueueInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? csv,
      String? error,
      int level,
      DateTime time,
      Duration? runningDuration,
      WPTaskStatus taskStatus});
}

/// @nodoc
class __$$WorkPlaceQueueInfoImplCopyWithImpl<$Res>
    extends _$WorkPlaceQueueInfoCopyWithImpl<$Res, _$WorkPlaceQueueInfoImpl>
    implements _$$WorkPlaceQueueInfoImplCopyWith<$Res> {
  __$$WorkPlaceQueueInfoImplCopyWithImpl(_$WorkPlaceQueueInfoImpl _value,
      $Res Function(_$WorkPlaceQueueInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? csv = freezed,
    Object? error = freezed,
    Object? level = null,
    Object? time = null,
    Object? runningDuration = freezed,
    Object? taskStatus = null,
  }) {
    return _then(_$WorkPlaceQueueInfoImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      csv: freezed == csv
          ? _value.csv
          : csv // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime,
      runningDuration: freezed == runningDuration
          ? _value.runningDuration
          : runningDuration // ignore: cast_nullable_to_non_nullable
              as Duration?,
      taskStatus: null == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as WPTaskStatus,
    ));
  }
}

/// @nodoc

class _$WorkPlaceQueueInfoImpl implements _WorkPlaceQueueInfo {
  const _$WorkPlaceQueueInfoImpl(
      {this.message = '',
      this.csv,
      this.error,
      this.level = 1,
      required this.time,
      this.runningDuration,
      this.taskStatus = WPTaskStatus.todo});

  /// unique
  @override
  @JsonKey()
  final String message;
  @override
  final String? csv;
  @override
  final String? error;
  @override
  @JsonKey()
  final int level;
  @override
  final DateTime time;
  @override
  final Duration? runningDuration;
  @override
  @JsonKey()
  final WPTaskStatus taskStatus;

  @override
  String toString() {
    return 'WorkPlaceQueueInfo(message: $message, csv: $csv, error: $error, level: $level, time: $time, runningDuration: $runningDuration, taskStatus: $taskStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkPlaceQueueInfoImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.csv, csv) || other.csv == csv) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.runningDuration, runningDuration) ||
                other.runningDuration == runningDuration) &&
            (identical(other.taskStatus, taskStatus) ||
                other.taskStatus == taskStatus));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, csv, error, level, time,
      runningDuration, taskStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkPlaceQueueInfoImplCopyWith<_$WorkPlaceQueueInfoImpl> get copyWith =>
      __$$WorkPlaceQueueInfoImplCopyWithImpl<_$WorkPlaceQueueInfoImpl>(
          this, _$identity);
}

abstract class _WorkPlaceQueueInfo implements WorkPlaceQueueInfo {
  const factory _WorkPlaceQueueInfo(
      {final String message,
      final String? csv,
      final String? error,
      final int level,
      required final DateTime time,
      final Duration? runningDuration,
      final WPTaskStatus taskStatus}) = _$WorkPlaceQueueInfoImpl;

  @override

  /// unique
  String get message;
  @override
  String? get csv;
  @override
  String? get error;
  @override
  int get level;
  @override
  DateTime get time;
  @override
  Duration? get runningDuration;
  @override
  WPTaskStatus get taskStatus;
  @override
  @JsonKey(ignore: true)
  _$$WorkPlaceQueueInfoImplCopyWith<_$WorkPlaceQueueInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
