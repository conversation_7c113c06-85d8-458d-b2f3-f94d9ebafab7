import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/bloc/common/common_bloc.dart';
import 'package:gp_fbwp_crawler/app/app.dart';

class CrawlSyncPage extends StatefulWidget {
  const CrawlSyncPage({super.key});

  @override
  State<CrawlSyncPage> createState() => _CrawlSyncPageState();
}

class _CrawlSyncPageState extends State<CrawlSyncPage> {
  late final commonBloc = GetIt.I<CommonBloc>(instanceName: 'kCommonBloc');
  late final communityBloc = CrawlCommunityBloc(commonBloc: commonBloc);
  late final memberBloc = CrawlMemberBloc(commonBloc: commonBloc);
  late final groupBloc = CrawlGroupBloc(commonBloc: commonBloc);
  late final feedBloc = CrawlFeedBloc(commonBloc: commonBloc);
  late final threadBloc = CrawlThreadBloc(commonBloc: commonBloc);

  late final queueBlocs = <CrawlQueueBloc>[
    if (AppConstants.isMainComputer) ...{
      // Chạy từng thứ 1 rồi manual check oke thì chạy tiếp
      communityBloc,
      memberBloc,
      groupBloc,
      // threadBloc,
    } else ...{
      communityBloc,
      memberBloc,
      // groupBloc,
      feedBloc,
      // threadBloc,
    }
  ];

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<CrawlCommunityBloc>(
          create: (context) => communityBloc,
        ),
        BlocProvider<CrawlMemberBloc>(
          create: (context) => memberBloc,
        ),
        BlocProvider<CrawlGroupBloc>(
          create: (context) => groupBloc,
        ),
        BlocProvider<CrawlFeedBloc>(
          create: (context) => feedBloc,
        ),
        BlocProvider<CrawlThreadBloc>(
          create: (context) => threadBloc,
        ),
        BlocProvider<CrawlBloc>(
          create: (context) => CrawlBloc()
            ..add(
              CrawlInitialEvent(queueBlocs: queueBlocs),
            ),
        ),
        RepositoryProvider(create: (context) => queueBlocs)
      ],
      child: BlocBuilder<AppConfigBloc, AppConfigState>(
        builder: (context, state) {
          return Scaffold(
            body: BlocBuilder<CrawlBloc, CrawlState>(
                builder: (context, crawlState) {
              return CrawlItemsPage();
            }),
            // return const CrawlDetail();
          );
        },
      ),
    );
  }
}
