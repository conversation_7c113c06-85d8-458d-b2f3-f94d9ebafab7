import 'dart:convert';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/entity/entity.dart';

mixin CheckpointMixin {
  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  Future<CrawlCheckpoint?> getCheckpoint(GPBaseCrawlType type) async {
    return localService.latestCheckpoint(type);
  }

  Future<CrawlCheckpoint?> getCheckpointById(String id) async {
    return localService.latestCheckpointById(id);
  }

  Future _saveCheckpoint(CrawlCheckpoint checkpoint) async {
    return localService.saveCheckpoint(checkpoint);
  }

  Future<List<T>> inputByCheckpoint<T>(
      GPBaseCrawlType type, List<BaseCrawlEntity> input) async {
    List<BaseCrawlEntity> output = input;
    final checkpoint = await getCheckpoint(type);
    if (checkpoint != null) {
      if (checkpoint.isDone) return [];
      final index =
          input.indexWhere((element) => element.id == checkpoint.checkpointId);
      if (index != -1) {
        output = input.sublist(index);
      }
    }
    return List<T>.from(output);
  }

  Future<List<T>> inputByCheckpointMultiThread<T>(
      {required int index,
      required List<BaseCrawlEntity> input,
      required GPBaseCrawlType type}) async {
    List<BaseCrawlEntity> output = input;
    final id = "${index}_${type.name}";
    final checkpoint = await getCheckpointById(id);
    if (checkpoint != null) {
      if (checkpoint.isDone &&
          (checkpoint.crawlType != GPBaseCrawlType.comment &&
              checkpoint.crawlType != GPBaseCrawlType.reaction)) {
        print("[${DateTime.now()}]Rerun $index _ $type");
        output = input;
      } else {
        final itemIndex = input
            .indexWhere((element) => element.id == checkpoint.checkpointId);
        if (itemIndex != -1) {
          output = input.sublist(itemIndex);
        } else {
          output = input;
        }
      }
    }
    return List<T>.from(output);
  }

  Future setDoneCheckpoint(GPBaseCrawlType type) async {
    final checkpoint = CrawlCheckpoint(crawlType: type, isDone: true);
    await _saveCheckpoint(checkpoint);
    print("[${DateTime.now()}]Done $type");
  }

  Future saveCheckpointMultiThread({
    required GPBaseCrawlType type,
    required int index,
    required int itemLength,
    required String checkpointId,
    int? indexCurrentItem,
    Map<String, String>? nextQuery,
  }) async {
    final id = "${index}_${type.name}";
    final checkpoint = await getCheckpointById(id);
    final newCheckpoint = checkpoint == null
        ? CrawlCheckpoint(
            crawlType: type,
            checkpointId: checkpointId,
            id: id,
            itemLength: itemLength,
            createdAt: DateTime.now(),
            nextQueries: nextQuery != null ? jsonEncode(nextQuery) : null,
          )
        : checkpoint.copyWith(
            isDone: false,
            checkpointId: checkpointId,
            updatedAt: DateTime.now(),
            nextQueries: nextQuery != null ? jsonEncode(nextQuery) : null);
    await _saveCheckpoint(newCheckpoint);
    print(
        "Save checkpoint $id | ${indexCurrentItem != null ? (indexCurrentItem / itemLength * 100).toStringAsFixed(1) : "..."}% | $indexCurrentItem / $itemLength | $checkpointId");
  }

  Future setDoneCheckpointMultiThread({
    required GPBaseCrawlType type,
    required int index,
    required int itemIndex,
  }) async {
    final id = "${index}_${type.name}";
    final checkpoint = await getCheckpointById(id);
    if (checkpoint != null) {
      if (itemIndex != checkpoint.itemLength) {
        print(
            "[${DateTime.now()}]Not Done $id multi-thread - Index: $itemIndex/${checkpoint.itemLength}");
        return;
      }
      await _saveCheckpoint(checkpoint.copyWith(
          isDone: true, updatedAt: DateTime.now(), lastDoneAt: DateTime.now()));
      print("[${DateTime.now()}]Done $id multi-thread");
    }
  }

  Future<int?> getLastDateCheckpoint(GPBaseCrawlType type, int index) async {
    final id = "${index}_${type.name}";
    final checkpoint = await getCheckpointById(id);
    if (checkpoint != null) {
      final date = checkpoint.lastDoneAt;
      if (date != null) return (date.millisecondsSinceEpoch / 1000).round();
    }
    return null;
  }

  Future<Map<String, String>?> getNextQuery(
      GPBaseCrawlType type, int index) async {
    final id = "${index}_${type.name}";
    final checkpoint = await getCheckpointById(id);
    if (checkpoint != null) {
      final nextQuery = checkpoint.nextQueries;
      if (nextQuery != null) {
        final query = jsonDecode(nextQuery);
        final map = query.map<String, String>(
            (key, value) => MapEntry<String, String>(key, value.toString()));
        return map;
      }
      return null;
    }
    return null;
  }
}
