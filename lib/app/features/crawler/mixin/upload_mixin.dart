import 'dart:io';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/constant/constant.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/file_helper.dart';
import 'package:gp_fbwp_crawler/mapper/gp_mapper.dart';
import 'package:isar/isar.dart';
import 'package:path/path.dart';
import 'package:retry/retry.dart';

mixin AttachmentHandlerMixin {
  final downloadUseCase = GetIt.I<DownloadFileUseCase>();

  final uploadUseCase = GetIt.I<GPUploadUseCase>();

  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  Future<bool> _hasLocalFile(String wpUrl) async {
    final filePath = await FileHelper.getFilePathFromUrl(wpUrl);
    // final decodedFilePath = Uri.decodeFull(filePath);

    // return await File(filePath).exists() ||
    //     await File(decodedFilePath).exists();
    return await File(filePath).exists();
  }

  Future<DownloadFileOutput?> downloadAttachment({
    required BaseCrawlEntity entity,
    required String wpUrl,
  }) async {
    if (wpUrl.isEmpty) return null;

    final filePath = await FileHelper.getFilePathFromUrl(wpUrl);

    if (await _hasLocalFile(wpUrl)) {
      entity.changeToDownloadStatus(
        status: BaseCrawlDownloadStatusEnum.downloaded,
        message: 'downloaded',
      );

      return DownloadFileOutput(
        isFileDownloaded: true,
        localFilePath: basename(filePath),
      );
    }

    entity.changeToDownloadStatus(
      status: BaseCrawlDownloadStatusEnum.downloading,
      message: 'downloading',
    );

    return await retry(
      () async {
        final output = await downloadUseCase.execute(
          DownloadFileInput(
            params:
                GPDownloadParams(downloadUrl: wpUrl, savedFilePath: filePath),
          ),
        );

        entity.changeToDownloadStatus(
          status: BaseCrawlDownloadStatusEnum.downloaded,
          message: 'downloaded',
        );

        await localService.updateTotalDownloadSize(filePath);

        return output;
      },
      onRetry: (e) {
        entity.changeToDownloadStatus(
          status: BaseCrawlDownloadStatusEnum.downloadFailed,
          message: e.toString(),
        );
      },
      maxAttempts: 3,
    );
  }

  Future<UploadFileResponseModelWrapper?> uploadAttachment({
    required BaseCrawlEntity entity,
    required DownloadFileOutput downloadOutput,
    required String wpUrl,
    required Function(String uploadUrl, String id) onUploaded,
    Function(String path)? onDownloadFileSuccess,
  }) async {
    if (wpUrl.isEmpty) return null;

    // if (downloadOutput.localFilePath.isEmpty) return null;

    String filePath = "";

    if (downloadOutput.localFilePath.isNotEmpty) {
      try {
        filePath = Uri.decodeFull(
          await FileHelper.downloadDirectoryPath(downloadOutput.localFilePath),
        );
      } catch (e) {
        filePath = await FileHelper.downloadDirectoryPath(
            downloadOutput.localFilePath);
      }
    }

    // String filePath = downloadOutput.localFilePath.isNotEmpty ? Uri.decodeFull(
    //   await FileHelper.downloadDirectoryPath(basename(downloadOutput.localFilePath)),
    // ) : "";

    final File file = File(filePath);

    if (filePath.isEmpty || !file.existsSync()) {
      final newDownloadOutput =
          await downloadAttachment(entity: entity, wpUrl: wpUrl);

      if (newDownloadOutput != null) {
        downloadOutput.localFilePath = newDownloadOutput.localFilePath;
        try {
          filePath = Uri.decodeFull(
            await FileHelper.downloadDirectoryPath(
                downloadOutput.localFilePath),
          );
        } catch (e) {
          filePath = await FileHelper.downloadDirectoryPath(
              downloadOutput.localFilePath);
        }
        onDownloadFileSuccess?.call(filePath);
      }
    }

    entity.changeToSyncStatus(
      status: BaseCrawlSyncStatusEnum.syncing,
    );

    return await retry(
      () async {
        if (downloadOutput.isFileDownloaded || filePath.isNotEmpty) {
          // await FileHelper.getFilePathFromUrl(wpUrl);

          final fileName = basename(filePath);
          // FileHelper.getFileNameFromUrl(wpUrl) ?? "";
          final uploadType = FileHelper.apiUploadType(fileName);

          // Upload
          final fileInput = [
            GPUploadFileInput(
              file: File(filePath),
              uploadType: uploadType,
            )
          ];

          final uploadResponseWrapper = await uploadUseCase.execute(
            GPUploadUseCaseInput(
                uploadInput: GPUploadInput(fileInputs: fileInput)),
          );

          String? uploadUrl;
          String? uploadId;

          if (uploadResponseWrapper.uploadImageResponseModels.isNotEmpty) {
            uploadUrl =
                uploadResponseWrapper.uploadImageResponseModels.first.src;
            uploadId = uploadResponseWrapper.uploadImageResponseModels.first.id;
          } else if (uploadResponseWrapper
              .uploadFileResponseModels.isNotEmpty) {
            uploadUrl =
                uploadResponseWrapper.uploadFileResponseModels.first.fileLink;
            uploadId = uploadResponseWrapper.uploadFileResponseModels.first.id;
          }

          if (uploadUrl != null) {
            onUploaded.call(uploadUrl, uploadId ?? '');
          }

          entity.changeToSyncStatus(
            status: BaseCrawlSyncStatusEnum.synced,
          );

          if (AppConstants.deleteUploadedLocalFile) {
            await File(filePath).delete();
          }

          return uploadResponseWrapper;
        }

        return null;
      },
      onRetry: (e) {
        entity.changeToSyncStatus(
          status: BaseCrawlSyncStatusEnum.serverMapperFailed,
          message: e.toString(),
        );
      },
      maxAttempts: 3,
    );
  }
}

class UploadResponseHandler with GPMapperMixin {
  late final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');

  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  void updateUploadResponse(
    dynamic attachment,
    UploadFileResponseModelWrapper response,
  ) async {
    final image = response.uploadImageResponseModels;
    int? size;

    if (image.isNotEmpty) {
      final imageResponse =
          convert<GPUploadImageResponseModel, UploadResponseEntity>(
              image.first);

      attachment.uploadResponse = imageResponse;

      size = imageResponse.size ?? 0;
    } else {
      final file = response.uploadFileResponseModels;

      if (file.isNotEmpty) {
        final fileResponse =
            convert<GPUploadFileResponseModel, UploadResponseEntity>(
                file.first);

        attachment.uploadResponse = fileResponse;

        size = fileResponse.size ?? 0;
      }
    }

    await localService.updateTotalUploadSize(size);
  }
}
