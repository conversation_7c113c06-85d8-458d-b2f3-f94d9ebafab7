import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/bloc/crawl_bloc.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/bloc/crawl_state.dart';

class GPBaseQueue {
  GPBaseQueue({
    required this.crawlQueueBloc,
    required this.commonBloc,
  });

  final CrawlQueueBloc crawlQueueBloc;

  final CommonBloc commonBloc;

  void emitInfo(
    String message,
    WPTaskStatus taskStatus, {
    String? csv,
    String? error,
    int level = 1,
  }) {
    crawlQueueBloc.emitInfo(
      WorkPlaceQueueInfo(
        message: message,
        taskStatus: taskStatus,
        csv: csv,
        error: error,
        level: level,
        time: DateTime.now(),
      ),
    );
  }

  Future<T?> addToQueue<T>({
    required String message,
    required Future<T> Function() job,
    int level = 1,
    bool skipCatch = false,
    bool exportCsv = false,
  }) async {
    emitInfo(level: level, message, WPTaskStatus.inProgress);

    try {
      final result = await job.call();

      if (exportCsv) {
        emitInfo(
            csv: result.toString(), level: level, message, WPTaskStatus.done);
      } else {
        emitInfo(level: level, message, WPTaskStatus.done);
      }

      return result;
    } catch (e) {
      if (skipCatch) {
        emitInfo(level: level, message, WPTaskStatus.done);
        return null;
      }

      emitInfo(
        level: level,
        message,
        error: getMessageFromException(e),
        WPTaskStatus.error,
      );
    }

    return null;
  }

  T? addToQueueSync<T>({
    required String message,
    required dynamic Function() job,
    int level = 1,
    bool skipCatch = false,
    bool exportCsv = false,
  }) {
    emitInfo(level: level, message, WPTaskStatus.inProgress);

    try {
      final result = job.call();

      if (exportCsv) {
        emitInfo(
            csv: result.toString(), level: level, message, WPTaskStatus.done);
      } else {
        emitInfo(level: level, message, WPTaskStatus.done);
      }

      return result;
    } catch (e) {
      if (skipCatch) {
        emitInfo(level: level, message, WPTaskStatus.done);
        return null;
      }

      emitInfo(
        level: level,
        message,
        error: getMessageFromException(e),
        WPTaskStatus.error,
      );
    }

    return null;
  }

  String getMessageFromException(Object? e) {
    if (e == null) return "Unknow exception";

    String error = e.toString();
    if (e is RemoteException) {
      final rootException = e.rootException;
      if (rootException is DioException) {
        if (rootException.response?.data.containsKey('error')) {
          error = rootException.response?.data['error']['message'];
        } else {
          error = rootException.response?.data['message'];
        }
      }
    }

    return error;
  }

  Future runInThreads<T>({
    required List<T> inputs,
    required Future Function(List<T> inputs, int index) actionPerThread,
    int startIndex = 0,
    int numberOfThreads = 5,
  }) async {
    if (inputs.isEmpty) return [];

    // return await actionPerThread(inputs);

    int start = startIndex;
    final int end = inputs.length;

    if (numberOfThreads > end) {
      numberOfThreads = end;
    }

    final int itemPerThreads = ((end - start) / numberOfThreads).ceil();

    int totalPages = (end / itemPerThreads).ceil();
    int maxLoops = numberOfThreads;

    if (totalPages < numberOfThreads) {
      maxLoops = totalPages;
    }

    final futures = <Future>[];

    for (var i = 1; i <= maxLoops; i++) {
      if (i != 1) {
        start = itemPerThreads * (i - 1);
      }

      int takeCount = itemPerThreads;
      if ((end - start) < itemPerThreads) {
        takeCount = end - start;
      }

      final newInputs = inputs.skip(start).take(takeCount).toList();

      logDebug(
          'DEBUG:start,newInputs,itemPerThreads,takeCount;end -> $start, ${newInputs.length}, $itemPerThreads, $takeCount $end');

      futures.add(actionPerThread(newInputs, i));
    }

    return await Future.wait(futures);
  }
}
