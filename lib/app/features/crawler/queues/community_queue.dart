import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/bloc/crawl_state.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/queues/base_queue.dart';
import 'package:gp_fbwp_crawler/app/features/home/<USER>';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/mapper/gp_mapper.dart';
import 'package:isar/isar.dart';

final class CommunityQueue extends GPBaseQueue
    with GPMapperMixin, _VariablesMixin {
  CommunityQueue({
    required super.crawlQueueBloc,
    required super.commonBloc,
  });

  FutureOr getWorkPlaceCommunity() async {
    // begin get workplace community
    await _getWorkPlaceCommunity();
  }

  Future<void> _getWorkPlaceCommunity() async {
    final community = await _getCommunity();

    final communityEntity =
        convert<FaceBookCommunityResponse, WorkPlaceCommunityEntity>(community);

    await _saveCommunity(communityEntity);

    emitInfo(
      l10n.crawl_community(communityEntity.name ?? ""),
      WPTaskStatus.done,
    );
  }

  Future<FaceBookCommunityResponse> _getCommunity() async {
    emitInfo(
      l10n.crawling_community,
      WPTaskStatus.inProgress,
    );
    final community = await wpCommunityUseCase.execute(
      const FaceBookCommunityInput(),
    );

    emitInfo(
      l10n.crawling_community,
      WPTaskStatus.done,
    );

    return community;
  }

  Future _saveCommunity(
    WorkPlaceCommunityEntity community,
  ) async {
    await localService.saveCommunity(community);
  }
}

mixin _VariablesMixin {
  final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');
  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  final wpCommunityUseCase = GetIt.I<FaceBookGetCommunityUseCase>();
}
