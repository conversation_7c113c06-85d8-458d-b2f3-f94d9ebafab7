import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/mixin/mixin.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/queues/base_queue.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:isar/isar.dart';

import '../../../../../mapper/gp_mapper.dart';

class BaseFeedQueue extends GPBaseQueue
    with
        GPMapperMixin,
        AttachmentHandlerMixin,
        CheckpointMixin,
        _DbHandlerMixin,
        _VariablesMixin,
        RunMultiClients {
  BaseFeedQueue({required super.crawlQueueBloc, required super.commonBloc});

  final uploadResponseHandler = UploadResponseHandler();
}

mixin _DbHandlerMixin {
  final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');
  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  Future<List<WorkPlaceGroupEntity>> getAllGroups() {
    return localService.getAllGroups();
  }

  Future<List<WorkPlaceCommunityMemberEntity>> getAllMembers() {
    return localService.getAllMembers();
  }

  Future<List<WorkPlaceFeedEntity>> getAllFeeds() {
    return localService.getAllFeeds();
  }

  Future<List<WorkPlaceCommentEntity>> getAllComments() {
    return localService.getAllComments();
  }

  Future saveFeeds(List<WorkPlaceFeedEntity> feeds) {
    return localService.saveFeeds(feeds);
  }

  Future updateFeed(WorkPlaceFeedEntity feedEntity) {
    return localService.updateFeed(feedEntity);
  }

  Future updateFeedComment(WorkPlaceFeedEntity feedEntity) {
    return localService.updateFeedComment(feedEntity);
  }

  Future reloadFeed(WorkPlaceFeedEntity feedEntity,
      {bool exceptAttachments = false}) {
    return localService.reload(feedEntity,
        exceptAttachments: exceptAttachments);
  }

  Future saveComments(List<WorkPlaceCommentEntity> comments) {
    return localService.saveComments(comments);
  }

  Future updateComment(WorkPlaceCommentEntity commentEntity) {
    return localService.updateComment(commentEntity);
  }

  Future reloadComment(WorkPlaceCommentEntity commentEntity) {
    return localService.reloadComment(commentEntity);
  }

  Future saveAttachment(WorkPlaceAttachmentEntity attachmentEntity) {
    return localService.saveAttachment(attachmentEntity);
  }

  Future saveAttachments(List<WorkPlaceAttachmentEntity> attachments) {
    return localService.saveAttachments(attachments);
  }

  Future flatternAttachments(
    WorkPlaceFeedEntity feed,
  ) {
    return localService.flatternAttachments(feed);
  }

  Future saveLog(String message, GPLogType type) async {
    return localService.saveLog(message, type);
  }
}

mixin _VariablesMixin {
  final wpGroupFeedsUseCase = GetIt.I<WorkPlaceGroupFeedsUseCase>();
  final wpUserFeedsUseCase = GetIt.I<WorkPlaceUserFeedsUseCase>();

  final commentsUseCase = GetIt.I<WorkPlacePostCommentsUseCase>();
  final replyCommentsUseCase = GetIt.I<WorkPlaceReplyCommentsUseCase>();

  /// Số comment tối đa tính cả reply
  final maxComments = 1000;
  int numComments = 0;

  final seenUseCase = GetIt.I<WorkPlacePostSeenUseCase>();
  final reactionUseCase = GetIt.I<WorkPlacePostReactionsUseCase>();
  final commentReactionUseCase = GetIt.I<WorkPlaceCommentReactionsUseCase>();
}
