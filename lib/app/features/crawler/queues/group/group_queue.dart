import 'dart:async';

import 'package:gp_fbwp_crawler/app/constant/constant.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/bloc/crawl_state.dart';
import 'package:gp_fbwp_crawler/app/features/home/<USER>';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/csv.dart';

import 'base_queue.dart';

final class GroupQueue extends BaseGroupQueue {
  GroupQueue({
    required super.crawlQueueBloc,
    required super.commonBloc,
  });

  Future<void> getWorkPlaceGroups() async {
    await _getWorkPlaceGroups();

    final groupEntitys = await getAllGroups();

    await updateGroup(groupEntitys);

    return groupEntitys;
  }

  Future getWorkPlaceMembers(List<WorkPlaceGroupEntity> groupEntitys) async {
    await _getGroupMembers(groupEntitys);

    _filterGroup(groupEntitys);

    return groupEntitys;
  }

  Future<List<WorkPlaceGroupEntity>> downloadWorkPlaceCover(
      List<WorkPlaceGroupEntity> groupEntitys) async {
    await addToQueue(
      message: l10n.crawl_group_upload_cover(groupEntitys.length),
      job: () async {
        for (WorkPlaceGroupEntity element in groupEntitys) {
          await _downloadCovers(element);

          await saveGroup(element);
        }
      },
    );

    return groupEntitys;
  }

  String saveCSV(
    List<WorkPlaceGroupEntity> groups,
  ) {
    return addToQueueSync<String>(
          exportCsv: true,
          message: l10n.crawl_group_save_csv,
          job: () {
            final gpGroup = groups.map((e) {
              final group = convert<WorkPlaceGroupEntity, GPGroup>(e);
              final json = group.toJson();
              return json;
            }).toList();

            groups.changeToSyncStatus(
              status: BaseCrawlSyncStatusEnum.synced,
            );

            return CsvHelper.toCSV(gpGroup);
          },
        ) ??
        '';
  }

  void _filterGroup(List<WorkPlaceGroupEntity> groups) {
    // if (AppConstants.enableEmailFilter) {
    //   groups.removeWhere((element) {
    //     final email = element.members.map((e) => e.email).toList();
    //     final hasFilterMember =
    //         email.any((element) => AppConstants.filterEmails.contains(element));
    //     return hasFilterMember == false;
    //   });
    // }
  }

  Future _getWorkPlaceGroups() async {
    final checkpoint =
        await localService.latestCheckpoint(GPBaseCrawlType.group);
    if (checkpoint?.isDone == true) return;
    emitInfo(
      l10n.crawl_group_get_groups,
      WPTaskStatus.inProgress,
    );

    final WorkPlaceCommunityEntity community = await getCommunity();

    WorkPlaceListReponse<WorkPlaceGroupResponse> groupResponses;

    // if (AppConstants.groupIds.isNotEmpty) {
    //   groupResponses = await wpGetAllGroupsByIdsUseCase.execute(
    //     WorkPlaceGroupsInput(
    //         communityId: community.id,
    //         groupIds: AppConstants.groupIds,
    //         saveData: (data, _) async {
    //           await _saveGroupsToLocal(data);
    //         }),
    //   );
    // } else {
      groupResponses = await wpGetAllGroupsUseCase.execute(
        WorkPlaceGroupsInput(
            communityId: community.id,
            saveData: (data, _) async {
              await _saveGroupsToLocal(data);
            }),
      );
    // }

    await setDoneCheckpoint(GPBaseCrawlType.group);

    emitInfo(
      l10n.crawl_group_get_groups,
      WPTaskStatus.done,
    );

    return groupResponses;
  }

  Future _getGroupMembers(List<WorkPlaceGroupEntity> groups) async {
    List<WorkPlaceGroupEntity> groupsGetData =
        await inputByCheckpoint<WorkPlaceGroupEntity>(
            GPBaseCrawlType.groupMember, groups);
    await addToQueue(
      message: l10n.crawl_group_member_wp,
      job: () async {
        await Future.forEach(groupsGetData, (group) async {
          await addToQueue(
            level: 2,
            message: l10n.crawl_group_member(group.name ?? group.id),
            job: () async {
              bool ignoreGroup = false;

              final members = await _getMembersFromAGroup(group);
              group.members.addAll(members);

              // if (AppConstants.enableEmailFilter) {
              //   final email = members.map((e) => e.email).toList();
              //   final hasFilterMember = email.any(
              //       (element) => AppConstants.filterEmails.contains(element));
              //   if (hasFilterMember == false) {
              //     ignoreGroup = true;
              //   }
              // }

              if (ignoreGroup == false) {
                await saveGroups([group]);
                await saveGroup(group);
                await updateGroup([group]);
              }
            },
          );
        });
      },
    );
    await setDoneCheckpoint(GPBaseCrawlType.groupMember);
  }

  Future<List<WorkPlaceCommunityMemberEntity>> _getMembersFromAGroup(
    WorkPlaceGroupEntity groupEntity,
  ) async {
    final WorkPlaceListReponse<WorkPlaceUser> members =
        await groupMembersUseCase.execute(
      WorkPlaceGroupMembersInput(groupId: groupEntity.id),
    );

    final memberEntities =
        convertList<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(
            members.data);

    return memberEntities;
  }

  Future _downloadCovers(WorkPlaceGroupEntity groupEntity) async {
    if (groupEntity.cover?.localFilePath != null) {
      return;
    }

    if (needToDownloadOrUpload(groupEntity)) {
      final wpUrl = groupEntity.cover?.source ?? '';

      final downloadOutput = await downloadAttachment(
        wpUrl: wpUrl,
        entity: groupEntity,
      );

      if (downloadOutput == null) return;

      groupEntity.cover?.localFilePath = downloadOutput.localFilePath;
    }

    return groupEntity;

    // if (groupEntity.cover != null) {
    //   final bool hasSource = groupEntity.cover?.source?.isNotEmpty ?? false;
    //   final bool alreadyDownloaded =
    //       groupEntity.cover!.gpCoverLink?.isNotEmpty ?? false;

    //   if (!hasSource || alreadyDownloaded) {
    //     return;
    //   }

    // final wpUrl = groupEntity.cover?.source ?? '';

    // final downloadOutput = await downloadAttachment(
    //   wpUrl: wpUrl,
    //   entity: groupEntity,
    // );

    // if (downloadOutput == null) return;

    // groupEntity.cover?.localFilePath = downloadOutput.localFilePath;

    //   // if (groupEntity.gpCoverLink?.isNotEmpty == true ||
    //   //     groupEntity.cover?.gpCoverLink?.isNotEmpty == true) {
    //   //   return groupEntity;
    //   // }
    // }

    // return groupEntity;
  }

  Future _saveGroupsToLocal(List<WorkPlaceGroupResponse> groups) async {
    final data = List<WorkPlaceGroupResponse>.from(groups);
    if (AppConstants.groupIds.isNotEmpty) {
      data.removeWhere((element) =>
          AppConstants.groupIds.contains(element.id) == false);
    }
    final groupEntitys =
        convertList<WorkPlaceGroupResponse, WorkPlaceGroupEntity>(data);

    await saveGroups(groupEntitys);

    for (var element in groupEntitys) {
      saveGroup(element);
    }
  }
}
