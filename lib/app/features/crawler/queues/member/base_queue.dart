import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:isar/isar.dart';

import '../../../../../data/data_source/local/workplace_local.service.dart';
import '../../../../../mapper/gp_mapper.dart';

abstract class BaseMemberQueue extends GPBaseQueue
    with GPMapperMixin, _VariablesMixin {
  BaseMemberQueue({required super.crawlQueueBloc, required super.commonBloc});

  Future saveMembers(
    List<WorkPlaceCommunityMemberEntity> members,
  ) async {
    await addToQueue(
      message: l10n.crawl_user_save_members(members.length),
      job: () async {
        await localService.saveMembers(members);
      },
    );
  }
}

mixin _VariablesMixin {
  final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');
  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  final wpCommunityMembersUseCase = GetIt.I<WorkPlaceCommunityMembersUseCase>();
  final wpCommunityMembersByIdsUseCase =
      GetIt.I<WorkPlaceCommunityMemberByIdUseCase>();

  final gpSignupUseCase = GetIt.I<GapoSignupUseCase>();
  final gpSetPasswordUseCase = GetIt.I<GPAuthSetPasswordUseCase>();
  final gpInviteEmailWorkspaceUseCase =
      GetIt.I<GPInviteEmailWorkspaceUseCase>();
  final gpInvitePhoneWorkspaceUseCase =
      GetIt.I<GPInvitePhoneWorkspaceUseCase>();
  final gpAuthCheckMailUseCase = GetIt.I<AuthCheckMailUseCase>();
  final gpAuthCheckPhoneUseCase = GetIt.I<AuthCheckPhoneUseCase>();
  final gpUserProfileUseCase = GetIt.I<GPUserProfileUseCase>();

  final uploadResponseHandler = UploadResponseHandler();
}
