import 'dart:async';

import 'package:gp_fbwp_crawler/app/features/crawler/mixin/mixin.dart';
import 'package:gp_fbwp_crawler/app/features/home/<USER>';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';

import 'base_queue.dart';

final class GPThreadQueue extends BaseThreadQueue {
  GPThreadQueue({
    required super.crawlQueueBloc,
    required super.commonBloc,
  });

  final uploadResponseHandler = UploadResponseHandler();

  bool isUploadingAttachments = false;

  Future uploadMessageAttachment(WorkPlaceMessagesEntity message) async {
    if (message.attachments.isEmpty && message.sticker.value == null) return;

    await addToQueue(
      message: l10n.crawl_thread_upload_message_attachment(message.id),
      level: 3,
      job: () async {
        final List<WorkPlaceConversationAttachmentsEntity> attachments = [
          ...message.attachments
        ];

        await _uploadAttachmentsFromMessage(attachments);

        if (message.sticker.value != null) {
          await _uploadStickersFromMessage(message.sticker.value!);
        }

        await updateMessage(message);
      },
    );
  }
}

extension _UploadAttachmentExt on GPThreadQueue {
  Future _uploadAttachmentsFromMessage(
    List<WorkPlaceConversationAttachmentsEntity> attachments,
  ) async {
    for (WorkPlaceConversationAttachmentsEntity attachment in attachments) {
      try {
        final wpUrl = attachment.src;

        if (wpUrl == null || wpUrl.isEmpty) continue;

        if (attachment.gpLink?.isNotEmpty == true ||
            attachment.uploadResponse != null) continue;

        final response = await uploadAttachment(
          wpUrl: wpUrl,
          entity: attachment,
          downloadOutput: DownloadFileOutput(
            isFileDownloaded: true,
            localFilePath: attachment.localFilePath ?? '',
          ),
          onUploaded: (uploadUrl, uploadId) {
            attachment.gpLink = uploadUrl;
          },
          onDownloadFileSuccess: (path) {
            attachment.localFilePath = path;
          },
        );

        if (response != null) {
          uploadResponseHandler.updateUploadResponse(attachment, response);
        }
      } catch (e) {
        saveLog('Error upload attachment message: $e', GPLogType.upload);
      }
    }

    await saveMessageAttachments(attachments);

    return attachments;
  }
}

extension _UploadStickerExt on GPThreadQueue {
  Future _uploadStickersFromMessage(
    WorkPlaceStickerEntity sticker,
  ) async {
    if (sticker.gpLink?.isNotEmpty ?? false) return sticker;
    if (sticker.url == null) return sticker;

    final response = await uploadAttachment(
      wpUrl: sticker.url!,
      entity: sticker,
      downloadOutput: DownloadFileOutput(
        isFileDownloaded: true,
        localFilePath: sticker.localFilePath ?? '',
      ),
      onUploaded: (uploadUrl, uploadId) {
        sticker.gpLink = uploadUrl;
      },
      onDownloadFileSuccess: (path) {
        sticker.localFilePath = path;
      },
    );

    if (response != null) {
      final image = response.uploadImageResponseModels;
      final imageResponse =
          convert<GPUploadImageResponseModel, UploadResponseEntity>(
              image.first);
      sticker.uploadResponse = imageResponse;
    }

    await saveSticker(sticker);

    return sticker;
  }
}
