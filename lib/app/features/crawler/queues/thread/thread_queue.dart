import 'dart:async';
import 'dart:developer';

import 'package:gp_fbwp_crawler/app/features/home/<USER>';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/helpers.dart';

import '../../../../constant/app_constant.dart';
import 'base_queue.dart';
import 'gp_thread_queue.dart';

final class ThreadQueue extends BaseThreadQueue {
  ThreadQueue({
    required super.crawlQueueBloc,
    required super.commonBloc,
  });

  late final GPThreadQueue gpQueue = GPThreadQueue(
    crawlQueueBloc: crawlQueueBloc,
    commonBloc: commonBloc,
  );

  Future<List<WorkPlaceConversationEntity>>
      getWorkPlaceUserConversations() async {
    final List<WorkPlaceConversationEntity> conversationEntities =
        await _getConversations();

    print("Done get threads");

    return conversationEntities;
  }

  Future<List<WorkPlaceMessagesEntity>> getConversationEmptyMessages() async {
    final List<WorkPlaceMessagesEntity> messageEntities = [];

    final conversations = await multiClientGetThreads();

    _removeInvalidThread(conversations);

    conversations.removeWhere((element) => element.messages.isNotEmpty);
    print('Thread no message: ${conversations.length}');

    if (conversations.isNotEmpty) {
      // messages
      final results = await _getMessages(
        conversations,
        (data) {
          messageEntities.addAll(data);
        },
        isGetThreadEmptyMessage: true,
      );

      // add lai cho chac, phong khi callback co van de
      if (messageEntities.isEmpty) {
        messageEntities.addAll(results);
      }
    }

    print("Done get thread empty message");

    return messageEntities;
  }

  Future<List<WorkPlaceMessagesEntity>> getWorkPlaceConversationMessages(
    List<WorkPlaceConversationEntity> conversationEntities,
  ) async {
    final List<WorkPlaceMessagesEntity> messageEntities = [];

    final conversations = await multiClientGetThreads();

    _removeInvalidThread(conversations);

    if (AppConstants.threadIds.isNotEmpty) {
      conversations.removeWhere(
          (element) => AppConstants.threadIds.contains(element.id) == false);
    }

    if (conversations.isNotEmpty) {
      // messages
      final results = await _getMessages(
        conversations,
        (data) {
          messageEntities.addAll(data);
        },
      );

      // add lai cho chac, phong khi callback co van de
      if (messageEntities.isEmpty) {
        messageEntities.addAll(results);
      }
    }

    print("Done get messages");

    return messageEntities;
  }

  Future<List<WorkPlaceMessagesEntity>> downloadMessageAttachments({
    List<WorkPlaceMessagesEntity>? messages,
  }) async {
    messages ??= await getAllMessages();
    for (var element in messages) {
      await reloadMessage(element);
    }

    Future actionPerThread(List<WorkPlaceMessagesEntity> messages) async {
      // upload attachments each conversation
      await addToQueue(
        message: l10n.crawl_thread_get_conversation_attachment,
        job: () async {
          await Future.forEach(
            messages,
            (message) async {
              // if (message.from.value?.gpUserId == null) return;
              await addToQueue(
                level: 2,
                message: l10n
                    .crawl_thread_get_conversation_from_a_thread(message.id),
                job: () async {
                  try {
                    log('downloadMessageAttachments message id = ${message.id}');

                    await _downloadMessageAttachment(message);
                    await _downloadMessageSticker(message);

                    if (AppConstants.needRunOnGapoWork) {
                      await gpQueue.uploadMessageAttachment(message);
                    }

                    await updateMessage(message);
                  } catch (e) {
                    saveLog('Error download message attachment: $e',
                        GPLogType.wpMessages);
                  }
                },
              );
            },
          );
        },
      );
    }

    await runInThreads(
        inputs: messages,
        actionPerThread: (inputs, index) async {
          return await actionPerThread(inputs);
        },
        numberOfThreads: 5);

    return messages;
  }

  Future done() async {
    // _saveConversationCSV();

    // _saveMessageCSV();
  }

  Future<String> _saveConversationCSV() async {
    return await addToQueue<String>(
          exportCsv: true,
          message: l10n.crawl_thread_save_thread_csv,
          job: () async {
            final conversations = await getAllThreads();
            for (var element in conversations) {
              await reloadThread(element);
            }
            final List<GPThread> threads =
                convertList<WorkPlaceConversationEntity, GPThread>(
                    conversations);
            return CsvHelper.toCSV(threads.map((e) => e.toJson()).toList());
          },
        ) ??
        '';
  }

  Future<String> _saveMessageCSV() async {
    return await addToQueue<String>(
          exportCsv: true,
          message: l10n.crawl_thread_save_message_csv,
          job: () async {
            final messages = await getAllMessages();
            for (var element in messages) {
              await reloadMessage(element);
            }
            final List<GPMessage> gpMessages =
                convertList<WorkPlaceMessagesEntity, GPMessage>(messages);

            gpMessages.removeWhere((element) => element.isInvalid);

            final reversedMessage = gpMessages.reversed;

            return CsvHelper.toCSV(
                reversedMessage.map((e) => e.toJson()).toList());
          },
        ) ??
        '';
  }
}

extension _ThreadExt on ThreadQueue {
  Future<List<WorkPlaceConversationEntity>> _getConversations() async {
    final List<WorkPlaceCommunityMemberEntity> userEntities =
        await getAllMembers();

    if (AppConstants.userIds.isNotEmpty) {
      userEntities.removeWhere(
          (element) => AppConstants.userIds.contains(element.id) == false);
    }

    if (userEntities.isEmpty) return [];

    return await addToQueue<List<WorkPlaceConversationEntity>>(
          message: l10n.crawl_thread_get_conversations,
          job: () async {
            List<WorkPlaceConversationEntity> output = [];
            await _getConversationsFromUsers(userEntities);

            await addToQueue(
              message: l10n.crawl_thread_save_thread,
              job: () async {
                final allThreads = await getAllThreads();

                for (var element in allThreads) {
                  await reloadThread(element);
                }

                output.addAll(allThreads);
              },
            );

            return output;
          },
        ) ??
        [];
  }

  Future _getConversationsFromUsers(
      List<WorkPlaceCommunityMemberEntity> userEntities) async {
    // final output = <WorkPlaceConversationEntity>[];
    // final users = await inputByCheckpoint<WorkPlaceCommunityMemberEntity>(
    //     GPBaseCrawlType.thread, userEntities);

    Future actionPerThread(
        List<WorkPlaceCommunityMemberEntity> users, int index) async {
      final userCheckpoint =
          await inputByCheckpointMultiThread<WorkPlaceCommunityMemberEntity>(
              index: index, input: users, type: GPBaseCrawlType.thread);
      final checkpointNextQuery =
          await getNextQuery(GPBaseCrawlType.thread, index);
      int itemIndex =
          users.indexWhere((element) => element.id == userCheckpoint.first.id);
      await Future.forEach(
        userCheckpoint,
        (user) async {
          await saveCheckpointMultiThread(
              type: GPBaseCrawlType.thread,
              index: index,
              itemLength: users.length,
              checkpointId: user.id,
              indexCurrentItem: itemIndex);
          await addToQueue(
            level: 2,
            message:
                l10n.crawl_thread_get_conversation_from_user(user.name ?? ''),
            job: () async {
              try {
                await wpUserConversationsUseCase.execute(
                  WorkPlaceUserConversationInput(
                    userId: user.id,
                    nextQuery: checkpointNextQuery,
                    saveData: (data, nextQuery) async {
                      final result = data.map((e) {
                        final conversation = convert<
                            WorkPlaceUserConversationsResponse,
                            WorkPlaceConversationEntity>(e);
                        conversation.wpUserId = user.id;
                        conversation.gpUserId = user.gpUserId;
                        conversation.threadId = e.id;

                        return conversation;
                      }).toList();

                      await saveThread(result);

                      for (var element in result) {
                        await updateThread(element);
                      }
                      await saveCheckpointMultiThread(
                          type: GPBaseCrawlType.thread,
                          index: index,
                          itemLength: users.length,
                          checkpointId: user.id,
                          indexCurrentItem: itemIndex,
                          nextQuery: nextQuery);
                    },
                  ),
                );
              } catch (e) {
                saveLog(
                    'Error get conversation: $e', GPLogType.wpConversations);
              }
            },
          );
          itemIndex++;
        },
      );
      await setDoneCheckpointMultiThread(
          type: GPBaseCrawlType.thread, index: index, itemIndex: itemIndex);
    }

    await runInThreads<WorkPlaceCommunityMemberEntity>(
      inputs: userEntities,
      actionPerThread: (inputs, index) async {
        await actionPerThread(inputs, index);
      },
    );

    // await setDoneCheckpoint(GPBaseCrawlType.thread);
    // final uniqueThreads = output.toSet();

    // return uniqueThreads.toList();
  }

  // Future _updateThreadId(
  //     List<WorkPlaceConversationEntity> conversationEntities) async {
  //   for (var con in conversationEntities) {
  //     for (var message in con.messages) {
  //       message.wpThreadId = con.threadId;
  //     }
  //     await updateThread(con);
  //   }
  // }
}

extension _MessageExt on ThreadQueue {
  void _removeInvalidThread(
      List<WorkPlaceConversationEntity> conversationEntities) {
    // lọc thread direct chat với bot
    conversationEntities.removeWhere((element) {
      final participants = element.participants
          .where((element) => element.email == null)
          .toList();
      return participants.length == 1 &&
          element.type == WorkPlaceThreadType.direct;
    });
  }

  Future<List<WorkPlaceMessagesEntity>> _getMessages(
      List<WorkPlaceConversationEntity> conversationEntities,
      Function(List<WorkPlaceMessagesEntity> data) onActionPerThreadCallback,
      {bool isGetThreadEmptyMessage = false}) async {
    // final List<WorkPlaceConversationEntity> conversations =
    //     await inputByCheckpoint<WorkPlaceConversationEntity>(
    //         GPBaseCrawlType.message, conversationEntities);

    Future<List<WorkPlaceMessagesEntity>> actionPerThread(
        List<WorkPlaceConversationEntity> conversations, int index) async {
      final conversationCheckpoint =
          await inputByCheckpointMultiThread<WorkPlaceConversationEntity>(
              index: index,
              input: conversations,
              type: GPBaseCrawlType.message);
      final result = await addToQueue<List<WorkPlaceMessagesEntity>>(
            message: l10n.crawl_thread_get_messages,
            job: () async {
              final List<WorkPlaceMessagesEntity> messageEntities = [];
              int itemIndex = conversations.indexWhere(
                  (element) => element.id == conversationCheckpoint.first.id);
              await addToQueue(
                level: 2,
                message: l10n.crawl_thread_save_message,
                job: () async {
                  for (WorkPlaceConversationEntity con
                      in conversationCheckpoint) {
                    await saveCheckpointMultiThread(
                        type: GPBaseCrawlType.message,
                        index: index,
                        itemLength: conversations.length,
                        checkpointId: con.id,
                        indexCurrentItem: itemIndex);
                    final lastDate = AppConstants.getMessageSinceTime != null
                        ? (AppConstants.getMessageSinceTime!
                                    .millisecondsSinceEpoch /
                                1000)
                            .round()
                        : await getLastDateCheckpoint(
                            GPBaseCrawlType.message, index);
                    final messages = await _getMessage(
                      con,
                      since: lastDate,
                      isGetThreadEmptyMessage: isGetThreadEmptyMessage,
                    );
                    messageEntities.addAll(messages);

                    try {
                      await downloadMessageAttachments(messages: messages);
                    } catch (e) {
                      saveLog('Error downloadMessageAttachments: $e',
                          GPLogType.wpAttachments);
                    }
                    itemIndex++;
                  }
                },
              );
              await setDoneCheckpointMultiThread(
                  type: GPBaseCrawlType.message,
                  index: index,
                  itemIndex: itemIndex);
              return messageEntities;
            },
          ) ??
          [];
      return result;
    }

    final result = await runInThreads<WorkPlaceConversationEntity>(
      inputs: conversationEntities,
      actionPerThread: (inputs, index) async {
        final results = await actionPerThread(inputs, index);

        onActionPerThreadCallback(results);

        return results;
      },
    );

    // await setDoneCheckpoint(GPBaseCrawlType.message);

    return List<WorkPlaceMessagesEntity>.from(result);
  }

  Future<List<WorkPlaceMessagesEntity>> _getMessage(
      WorkPlaceConversationEntity conversationEntity,
      {int? since,
      bool isGetThreadEmptyMessage = false}) async {
    return await addToQueue<List<WorkPlaceMessagesEntity>>(
            level: 3,
            message: l10n.crawl_thread_get_message(conversationEntity.id),
            job: () async {
              List<WorkPlaceMessagesEntity> output = [];
              // final wpUser = await getWpUser(conversationEntity.gpUserId);

              // String? wpUserId = StringHelper.getConversationIdFromLink(
              //     conversationEntity.link ?? '');

              // final wpUser = await getUserById(wpUserId);
              final participants = conversationEntity.participants
                  .where((element) => element.gpUserId != null)
                  .toList();
              if (participants.length <= 1) return output;

              final secondaryWPUser = conversationEntity.participants.first.id;

              await wpMessageUseCase.execute(
                WorkPlaceUserMessageInput(
                  conversationId: conversationEntity.id,
                  user: conversationEntity.wpUserId ?? secondaryWPUser,
                  secondaryUsers:
                      conversationEntity.participants.map((e) => e.id).toList(),
                  saveData: (data, nextQuery) async {
                    bool isDone = false;
                    final messageEntities =
                        convertList<Messages, WorkPlaceMessagesEntity>(data);

                    for (var element in messageEntities) {
                      element.wpThreadId = conversationEntity.id;
                    }

                    final splitedMessageEntities =
                        _splitLongMessage(messageEntities);

                    await saveMessages(splitedMessageEntities);

                    for (var element in splitedMessageEntities) {
                      if (element.attachments.isNotEmpty) {
                        await saveMessageAttachments([...element.attachments]);
                      }
                      if (element.sticker.value != null) {
                        await saveSticker(element.sticker.value!);
                      }
                      await updateMessage(element);
                      if (isGetThreadEmptyMessage == false) {
                        if (since != null && element.createdTime != null) {
                          final sinceDate =
                              DateTime.fromMillisecondsSinceEpoch(since * 1000);
                          if (element.createdTime!.isBefore(sinceDate)) {
                            isDone = true;
                          }
                        }
                      }
                    }

                    conversationEntity.messages.addAll(splitedMessageEntities);

                    await updateThread(conversationEntity);
                    output.addAll(splitedMessageEntities);
                    if (isDone) return true;
                  },
                ),
              );

              return output;
            }) ??
        [];
  }

  List<WorkPlaceMessagesEntity> _splitLongMessage(
      List<WorkPlaceMessagesEntity> messages) {
    final result = List<WorkPlaceMessagesEntity>.of(messages);
    for (var message in messages) {
      final shortString =
          StringHelper.splitIntoShortStrings(message.message ?? '');
      if (shortString.length > 1) {
        final shortMessages = shortString
            .map<WorkPlaceMessagesEntity>((e) => message.copyWith(message: e));
        // reverse để đúng thứ tự tin nhắn
        final reversed = shortMessages.toList().reversed;
        final index = result.indexOf(message);
        result.replaceRange(index, index + 1, reversed);
      }
    }
    return result;
  }
}

extension _UploadMessageAttachmentExt on ThreadQueue {
  Future _downloadMessageAttachment(WorkPlaceMessagesEntity message) async {
    if (message.attachments.isEmpty) return;

    await addToQueue(
      message: l10n.crawl_thread_get_attachment_from_a_message(message.id),
      level: 3,
      job: () async {
        final List<WorkPlaceConversationAttachmentsEntity> attachments = [
          ...message.attachments
        ];

        await _downloadAttachmentsFromMessage(attachments);

        await updateMessage(message);
      },
    );
  }

  Future<List<WorkPlaceConversationAttachmentsEntity>>
      _downloadAttachmentsFromMessage(
    List<WorkPlaceConversationAttachmentsEntity> attachments,
  ) async {
    for (WorkPlaceConversationAttachmentsEntity attachment in attachments) {
      final wpUrl = attachment.src;

      if (wpUrl == null || wpUrl.isEmpty) continue;

      if (attachment.gpLink?.isNotEmpty == true ||
          attachment.uploadResponse != null) continue;

      final downloadOutput =
          await downloadAttachment(entity: attachment, wpUrl: wpUrl);

      if (downloadOutput == null) continue;

      attachment.localFilePath = downloadOutput.localFilePath;
    }

    await saveMessageAttachments(attachments);

    return attachments;
  }
}

extension _StickerExt on ThreadQueue {
  Future _downloadMessageSticker(WorkPlaceMessagesEntity message) async {
    if (message.sticker.value == null) return;

    await addToQueue(
      message: l10n.crawl_thread_get_sticker_from_a_message(message.id),
      level: 3,
      job: () async {
        await _downloadSticker(message.sticker.value!);

        await updateMessage(message);
      },
    );
  }

  Future<WorkPlaceStickerEntity> _downloadSticker(
      WorkPlaceStickerEntity sticker) async {
    if (sticker.gpLink?.isNotEmpty ?? false) return sticker;
    if (sticker.url == null) return sticker;
    final downloadOutput = await downloadAttachment(
      entity: sticker,
      wpUrl: sticker.url!,
    );

    if (downloadOutput == null) return sticker;

    sticker.localFilePath = downloadOutput.localFilePath;

    await saveSticker(sticker);

    return sticker;
  }
}
