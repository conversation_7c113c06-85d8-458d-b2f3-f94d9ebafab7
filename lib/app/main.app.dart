import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:gp_core/core.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/app/app_config/app_config.dart';
import 'package:gp_fbwp_crawler/l10n/app_localizations.dart';
import 'package:gp_fbwp_crawler/route/route.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

final _goRouter = GoRouter(
  navigatorKey:
      GetIt.I<GlobalKey<NavigatorState>>(instanceName: 'kNavigatorKey'),
  debugLogDiagnostics: kDebugMode,
  routes: $appRoutes,
  initialLocation: kSplash,
  observers: [],
);

class GPCrawlerApp extends StatelessWidget {
  const GPCrawlerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AppConfigBloc>(
      create: (context) => AppConfigBloc()..add(const AppConfigInitialEvent()),
      child: BlocBuilder<AppConfigBloc, AppConfigState>(
        builder: (BuildContext context, AppConfigState state) {
          return ResponsiveSizer(
            builder: (context, orientation, screenType) {
              return MaterialApp.router(
                localizationsDelegates: const [
                  S.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                debugShowCheckedModeBanner: false,
                theme: AppThemes.theme(),
                darkTheme: AppThemes.darktheme(),
                themeMode: AppThemes().init(),
                locale: state.appConfigEntity.appLocale.locale,
                routerConfig: _goRouter,
                scaffoldMessengerKey:
                    GetIt.I<GlobalKey<ScaffoldMessengerState>>(
                  instanceName: 'kScaffoldMessengerState',
                ),
              );
            },
          );
        },
      ),
    );
  }
}
