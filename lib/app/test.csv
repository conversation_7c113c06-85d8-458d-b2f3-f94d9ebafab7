'id';'first_name';'last_name';'email';'picture';'link';'locale';'name';'name_format';'account_invite_time';'account_claim_time';'work_locale';'active';'test_md'
'**************';'nguyenmanhtoan';'nguyenmanhtoan';'<EMAIL>';'https://gapo-work-image.s3.vn-hcm.zetaby.com/images/b5a38f37-dd09-471b-9595-d4101de998a7/toannm_fbwp.jpeg';'https://gwtest092.workplace.com/app_scoped_user_id/**************/';'en_US';'nguyenmanhtoan';'{first} {last}';'2024-05-31T03:24:09+0000';'2024-05-31T03:25:51+0000';'en_US';'true';'## \''Agenda\''\nHỗ trợ \"custom\" subdomain cho khách hàng VD khách hàng muốn sử dụng [lpb.gapowork.vn](http://lpb.gapowork.vn/) [bidv.gapowork.vn](http://bidv.gapowork.vn/)\n## Mục tiêu\n1. Xác định các đầu việc cần thiết\n2. Đánh giá tính khả thi khi triển khai\n3. Lên kế hoạch thực hiện\n\n## Một số đầu việc\n- Config subdomain cho từng workspace\n- Web sẽ lấy được thông tin config này để redirect sang subdomain của ws. tức là khi đc config subdomain [aaaa.gapowork.vn](http://aaaa.gapowork.vn/) thì user vào [gapowork.vn](http://gapowork.vn/)  sẽ tự redirect sang [aaaa.gapowork.vn](http://aaaa.gapowork.vn/)\n- Xử lý việc 1 user thuộc nhiều workspaceuser đang ở ws A có domain: [aaaa.gapowork.vn](http://aaaa.gapowork.vn/) click vào link của ws B [bbbb.gapowork.vn](http://bbbb.gapowork.vn/) thì sẽ xử lý thế nào? web sẽ detect để redirect sang [aaaa.gapowork.vn ](http://aaaa.gapowork.vn/)mobile thì chỉ cần hỗ trợ subdomain (*.gapowork.vn) thì sẽ chạy như luồng hiện tại\n- Các service có gen link sẽ cần get thêm ws để gen đúng với subdomain được config\n    - Post\n    - Task\n    - Workspace\n    - Auth\n    - Profile'