import 'dart:async';
import 'dart:isolate';
import 'dart:math';

import 'package:async_task/async_task.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart' hide Assets;
import 'package:responsive_sizer/responsive_sizer.dart';

List<AsyncTask> _taskTypeRegister() => [
      _GPAsynTask(
        input: const _AsyncTaskCallback(id: '', waitSecond: 0),
        sendPort: _receivePort.sendPort,
      )
    ];

final ValueNotifier<String> _rxAsyncResult = ValueNotifier<String>('');
final ReceivePort _receivePort = ReceivePort();

Future _testAsyncTask() async {
  var tasks = <_GPAsynTask>[];

  const int max = 20;

  for (var i = 0; i < max; i++) {
    tasks.add(
      _GPAsynTask(
        input: _AsyncTaskCallback(
          id: '$i',
          waitSecond: i,
        ),
        sendPort: _receivePort.sendPort,
      ),
    );
  }

  var asyncExecutor = AsyncExecutor(
    sequential: false, // Non-sequential tasks.
    parallelism: 20, // Concurrency with 2 threads.
    taskTypeRegister:
        _taskTypeRegister, // The top-level function to register the tasks types.
  );

  // Enable logging output:
  asyncExecutor.logger.enabled = true;
  // Enable logging output with execution information:
  asyncExecutor.logger.enabledExecution = true;

  _receivePort.listen((message) {
    _rxAsyncResult.value = message;
  }, onError: (error) {
    _rxAsyncResult.value = 'Error';
  });

  // Execute all tasks:
  var executions = asyncExecutor.executeAll(tasks);

  await Future.wait(executions);

  // Close the executor.
  await asyncExecutor.close();
}

class _GPAsynTask extends AsyncTask<_AsyncTaskCallback, dynamic> {
  _GPAsynTask({
    required this.input,
    required this.sendPort,
  });

  final _AsyncTaskCallback input;
  final SendPort sendPort;

  @override
  AsyncTaskChannel? channelInstantiator() {
    return AsyncTaskChannel(
      messageHandler: (message, fromExecutingContext) {
        sendPort.send(message);
      },
    );
  }

  @override
  AsyncTask<_AsyncTaskCallback, dynamic> instantiate(
    _AsyncTaskCallback parameters, [
    Map<String, SharedData>? sharedData,
  ]) {
    return _GPAsynTask(
      input: parameters,
      sendPort: sendPort,
    );
  }

  @override
  _AsyncTaskCallback parameters() {
    return input;
  }

  @override
  FutureOr run() async {
    if (input.id == '') {
      logDebug('aaaaaaa');
    }

    final int second = Random().nextInt(6);

    await Future.delayed(
      Duration(seconds: second
          //input.waitSecond
          ),
    );

    var channel = channelResolved()!;
    channel.send('____Done ${input.waitSecond}');
  }
}

class _AsyncTaskCallback {
  const _AsyncTaskCallback({
    required this.id,
    required this.waitSecond,
  });

  final String id;
  final int waitSecond;
}

class TestAsyncPage extends StatelessWidget {
  const TestAsyncPage({super.key});

  @override
  Widget build(BuildContext context) {
    _testAsyncTask();
    return Scaffold(
      appBar: AppBar(),
      body: Center(
        child: ValueListenableBuilder(
          valueListenable: _rxAsyncResult,
          builder: (context, value, child) {
            return Text(
              _rxAsyncResult.value,
              style: TextStyle(color: GPColor.red, fontSize: 20.sp),
            );
          },
        ),
      ),
    );
  }
}
