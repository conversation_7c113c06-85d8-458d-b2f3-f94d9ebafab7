import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:injectable/injectable.dart';

abstract class AppConfig {
  GPEnvironment get environment;

  bool get isStaging =>
      this is AppConfigStaging && environment == GPEnvironment.staging;

  bool get isUAT => this is AppConfigUAT && environment == GPEnvironment.uat;

  bool get isProduction =>
      this is AppConfigProd && environment == GPEnvironment.production;

  Environment getEnvironmentInjectable() {
    if (isStaging) return kFlavorSaasDevelopment;
    if (isUAT) return kFlavorSaasUAT;
    if (isProduction) return kFlavorSaasProduction;

    return kFlavorSaasDevelopment;
  }
}

final class AppConfigStaging extends AppConfig {
  @override
  GPEnvironment get environment => GPEnvironment.staging;
}

final class AppConfigUAT extends AppConfig {
  @override
  GPEnvironment get environment => GPEnvironment.uat;
}

final class AppConfigProd extends AppConfig {
  @override
  GPEnvironment get environment => GPEnvironment.production;
}

enum GPEnvironment { staging, uat, production }
