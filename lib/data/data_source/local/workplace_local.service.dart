/*
 * Created Date: Tuesday, 4th June 2024, 09:42:07
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Sunday, 15th September 2024 18:48:33
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';
import 'dart:io';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_fbwp_crawler/app/constant/constant.dart';
import 'package:gp_fbwp_crawler/data/model/workplace/enums/workplace_enums.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart';

class _BaseWorkPlaceLocalService {
  const _BaseWorkPlaceLocalService(
    this.isar,
  );

  final Isar isar;
}

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kWorkPlaceLocalService')
final class WorkPlaceLocalService extends _BaseWorkPlaceLocalService
    with
        _AppConfigMixin,
        _CommunityServiceMixin,
        _MemberServiceMixin,
        _GroupServiceMixin,
        _FeedServiceMixin,
        _CommentServiceMixin,
        _ThreadServiceMixin,
        _CommentServiceMixin,
        _AttachmentServiceMixin,
        _CrawlCheckpointMixin,
        _GPDashBoardMixin,
        _GPLog {
  @FactoryMethod()
  const WorkPlaceLocalService(
    @Named('kIsar') super.isar,
  );
}

mixin _AppConfigMixin on _BaseWorkPlaceLocalService {
  Future<AppConfigEntity> getCurrentAppConfig() async {
    AppConfigEntity ret;

    final appVersionStr = await getAppVersion();

    getEnvConfig();

    final AppConfigEntity? appConfigEntityByVersion = await isar
        .appConfigEntitys
        .filter()
        .appVersionEqualTo(appVersionStr)
        .findFirst();

    if (appConfigEntityByVersion == null) {
      final AppConfigEntity? appConfigEntity = await isar.appConfigEntitys
          .where()
          .sortByAppVersionDesc()
          .findFirst();

      ret = AppConfigEntity(
        appLocale: appConfigEntity?.appLocale ?? GPAppLocale.vi,
        appVersion: appVersionStr,
        workPlaceToken: appConfigEntity?.workPlaceToken ?? AppConstants.token,
      );

      await isar.writeTxn(
        () => isar.appConfigEntitys.put(ret),
      );
    } else {
      ret = appConfigEntityByVersion;
    }

    return ret;
  }

  Future<String> getAppVersion() async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  void getEnvConfig() {
    log('Computer ID: ${dotenv.env['COMPUTER_ID']}');
    final mainComputer = dotenv.env["MAIN_COMPUTER"];
    final computerId = dotenv.env["COMPUTER_ID"];
    final pcName = dotenv.env["PCNAME"];
    AppConstants.computerId = computerId ?? '';
    AppConstants.pcName = pcName ?? '';
    AppConstants.isMainComputer = mainComputer == "true";
  }
}

mixin _CommunityServiceMixin on _BaseWorkPlaceLocalService {
  Future saveCommunity(
    WorkPlaceCommunityEntity community,
  ) async {
    await isar.writeTxn(() async {
      await isar.workPlaceCommunityEntitys.put(community);
    });
  }

  Future getCommunity() async {
    return await isar.workPlaceCommunityEntitys.where().findFirst();
  }
}

mixin _MemberServiceMixin on _BaseWorkPlaceLocalService {
  Future saveMembers(
    List<WorkPlaceCommunityMemberEntity> members,
  ) async {
    await isar.writeTxn(() async {
      for (var element in members) {
        final member = await isar.workPlaceCommunityMemberEntitys
            .filter()
            .idEqualTo(element.id)
            .findFirst();
        if (member != null) {
          element.insertedAt = member.insertedAt ?? DateTime.now();
        } else {
          element.insertedAt = DateTime.now();
        }
        await isar.workPlaceCommunityMemberEntitys.put(element);
      }
    });
  }

  Future<WorkPlaceCommunityMemberEntity?> getWpUser(int? gpUserId) async {
    if (gpUserId == null) {
      return null;
    }
    return await isar.workPlaceCommunityMemberEntitys
        .filter()
        .gpUserIdEqualTo(gpUserId)
        .findFirst();
  }

  Future<WorkPlaceCommunityMemberEntity?> getUserById(String? id) async {
    if (id == null) {
      return null;
    }
    return await isar.workPlaceCommunityMemberEntitys
        .filter()
        .idContains(id)
        .findFirst();
  }
}
mixin _GroupServiceMixin on _BaseWorkPlaceLocalService {
  Future<List<WorkPlaceGroupEntity>> getAllGroups() async {
    return await isar.workPlaceGroupEntitys
        .where()
        .sortByInsertedAt()
        .findAll();
  }

  Future<List<WorkPlaceCommunityMemberEntity>> getAllMembers() async {
    return await isar.workPlaceCommunityMemberEntitys
        .where()
        .sortByInsertedAt()
        .findAll();
  }

  Future<int> getMemberWithoutGPUserId() async {
    return await isar.workPlaceCommunityMemberEntitys
        .filter()
        .gpUserIdIsNull()
        .count();
  }

  Future<int> getFeedWithoutGroup() async {
    return await isar.workPlaceFeedEntitys
        .filter()
        .isUserFeedEqualTo(false)
        .and()
        .groupIsNull()
        .count();
  }

  Future<List<int>> getUploadedFiles() async {
    final totalUploadedAttachments = await isar.workPlaceAttachmentEntitys
        .filter()
        .gpLinkIsNotEmpty()
        .count();

    final totalConversationUploadedAttachment = await isar
        .workPlaceConversationAttachmentsEntitys
        .filter()
        .gpLinkIsNotEmpty()
        .count();

    return [
      totalUploadedAttachments,
      totalConversationUploadedAttachment,
    ];
    // return totalUploadedAttachments + totalConversationUploadedAttachment;
  }

  Future<List<WorkPlaceFeedEntity>> getAllFeeds() async {
    return await isar.workPlaceFeedEntitys.where().sortByInsertedAt().findAll();
  }

  Future<List<WorkPlaceCommentEntity>> getAllComments() async {
    return await isar.workPlaceCommentEntitys.where().findAll();
  }

  Future<List<WorkPlaceConversationEntity>> getAllConversations() async {
    return await isar.workPlaceConversationEntitys
        .where()
        .sortByInsertedAt()
        .findAll();
  }

  Future<List<WorkPlaceMessagesEntity>> getAllMessages() async {
    return await isar.workPlaceMessagesEntitys.where().findAll();
  }

  Future<List<WorkPlaceConversationAttachmentsEntity>>
      getAllConversationAttachments() async {
    return await isar.workPlaceConversationAttachmentsEntitys.where().findAll();
  }

  Future<List<WorkPlaceAttachmentEntity>> getAllAttachments() async {
    return await isar.workPlaceAttachmentEntitys.where().findAll();
  }

  Future<List<WorkPlaceStickerEntity>> getAllStickers() async {
    return await isar.workPlaceStickerEntitys.where().findAll();
  }

  Future saveGroups(List<WorkPlaceGroupEntity> groups) async {
    await isar.writeTxn(() async {
      for (var element in groups) {
        final group = await isar.workPlaceGroupEntitys
            .filter()
            .idEqualTo(element.id)
            .findFirst();
        if (group != null) {
          element.insertedAt = group.insertedAt ?? DateTime.now();
        } else {
          element.insertedAt = DateTime.now();
        }
        await isar.workPlaceGroupEntitys.put(element);
      }
    });
  }

  Future saveGroup(WorkPlaceGroupEntity groupEntity) async {
    await isar.writeTxn(() async {
      await groupEntity.owner.save();
      await groupEntity.admins.save();
      await groupEntity.members.save();
    });
  }

  Future updateGroup(List<WorkPlaceGroupEntity> groupEntitys) async {
    for (WorkPlaceGroupEntity element in groupEntitys) {
      if (element.owner.value != null) {
        await _updateGroupByAUser(element.owner.value!, element);
      }

      for (var user in element.admins) {
        await _updateGroupByAUser(user, element);
      }

      for (var user in element.members) {
        await _updateGroupByAUser(user, element);
      }

      await isar.writeTxn(() async {
        await element.owner.load();
        await element.admins.load();
        await element.members.load();
      });
    }
  }

  Future _updateGroupByAUser(
    WorkPlaceCommunityMemberEntity userEntity,
    WorkPlaceGroupEntity groupEntity,
  ) async {
    // final userEntityById = await _findMemberById(userEntity.id);

    // await isar.writeTxn(() async {
    //   userEntityById?.groups.add(groupEntity);
    //   await userEntityById?.groups.save();
    // });
  }

  // Future<WorkPlaceCommunityMemberEntity?> _findMemberById(String? id) async {
  //   if (id == null) return null;
  //   return isar.workPlaceCommunityMemberEntitys
  //       .filter()
  //       .idEqualTo(id)
  //       .findFirstSync();
  // }
}

mixin _FeedServiceMixin on _BaseWorkPlaceLocalService {
  Future<List<WorkPlaceFeedEntity>> getAllFeeds() async {
    return await isar.workPlaceFeedEntitys.where().sortByInsertedAt().findAll();
  }

  Future saveFeeds(List<WorkPlaceFeedEntity> feeds) async {
    await isar.writeTxn(() async {
      for (var element in feeds) {
        final feed = await isar.workPlaceFeedEntitys
            .filter()
            .idEqualTo(element.id)
            .findFirst();
        if (feed != null) {
          element.insertedAt = feed.insertedAt ?? DateTime.now();
        } else {
          element.insertedAt = DateTime.now();
        }
        await isar.workPlaceFeedEntitys.put(element);
      }
    });
  }

  Future updateFeed(WorkPlaceFeedEntity feedEntity) async {
    await isar.writeTxn(() async {
      await feedEntity.attachments.save();
      await feedEntity.comments.save();
      await feedEntity.to.save();
      await feedEntity.from.save();
      await feedEntity.group.save();
      await feedEntity.seen.save();
    });
  }

  Future updateFeedComment(WorkPlaceFeedEntity feedEntity) async {
    await isar.writeTxn(() async {
      await feedEntity.comments.save();
    });
  }

  Future reload(WorkPlaceFeedEntity feed,
      {bool exceptAttachments = false}) async {
    await isar.writeTxn(() async {
      if (!exceptAttachments) {
        await feed.attachments.load();
      }
      await feed.comments.load();
      await feed.group.load();
      await feed.from.load();
      await feed.seen.load();
    });
  }

  Future<Iterable<WorkPlaceAttachmentEntity>> missingAttachments() async {
    final feeds = await getAllFeeds();
    final output = <WorkPlaceAttachmentEntity>[];

    for (var feed in feeds) {
      if (feed.attachments.isEmpty && feed.comments.isEmpty) continue;

      for (var element in feed.attachments) {
        if (element.localFilePath == null || element.gpLink == null) {
          final wpUrl = element.urlDownload();

          if (wpUrl.isNotEmpty) {
            output.add(element);
          }
        }
      }

      for (var element in feed.comments) {
        if (element.attachment.value == null) continue;

        if (element.attachment.value?.gpLink == null ||
            element.attachment.value?.localFilePath == null) {
          final wpUrl = element.attachment.value?.urlDownload();

          if (wpUrl?.isNotEmpty == true) {
            output.add(element.attachment.value!);
          }
        }
      }
    }

    return output.toSet();
  }
}

mixin _CommentServiceMixin on _BaseWorkPlaceLocalService {
  Future<List<WorkPlaceCommentEntity>> getAllComments() async {
    return await isar.workPlaceCommentEntitys.where().findAll();
  }

  Future saveComments(List<WorkPlaceCommentEntity> comments) async {
    await isar.writeTxn(() async {
      await isar.workPlaceCommentEntitys.putAll(comments);
    });
  }

  Future saveComment(WorkPlaceCommentEntity commentEntity) async {
    await isar.writeTxn(() async {
      await isar.workPlaceCommentEntitys.put(commentEntity);
    });
  }

  Future updateComment(WorkPlaceCommentEntity commentEntity) async {
    await isar.writeTxn(() async {
      await commentEntity.attachment.save();
      await commentEntity.from.save();
      await commentEntity.replies.save();
    });
  }

  Future reloadComment(WorkPlaceCommentEntity commentEntity) async {
    await isar.writeTxn(() async {
      await commentEntity.attachment.load();
      await commentEntity.from.load();
      await commentEntity.replies.load();
    });
  }
}

mixin _ThreadServiceMixin on _BaseWorkPlaceLocalService {
  Future saveThreads(List<WorkPlaceConversationEntity> threads) async {
    await isar.writeTxn(() async {
      for (var element in threads) {
        final thread = await isar.workPlaceConversationEntitys
            .filter()
            .idEqualTo(element.id)
            .findFirst();
        if (thread != null) {
          element.insertedAt = thread.insertedAt ?? DateTime.now();
        } else {
          element.insertedAt = DateTime.now();
        }
        await isar.workPlaceConversationEntitys.put(element);
      }
    });
  }

  Future<List<WorkPlaceConversationEntity>> getAllThreads() async {
    return await isar.workPlaceConversationEntitys
        .where()
        .sortByInsertedAt()
        .findAll();
  }

  Future removeAllThreads() async {
    await isar.writeTxn(() async {
      await isar.workPlaceConversationEntitys.where().deleteAll();
    });
  }

  Future<List<WorkPlaceMessagesEntity>> getAllMessages() async {
    return await isar.workPlaceMessagesEntitys
        .where()
        .sortByWpThreadId()
        .thenByCreatedTimeDesc()
        .findAll();
  }

  Future<List<WorkPlaceMessagesEntity>> getMessagesByThreadId(String id) async {
    return await isar.workPlaceMessagesEntitys
        .filter()
        .wpThreadIdEqualTo(id)
        .sortByCreatedTimeDesc()
        .findAll();
  }

  Future<WorkPlaceMessagesEntity?> getLatestMessagesByThreadId(String id) async {
    return await isar.workPlaceMessagesEntitys
        .filter()
        .wpThreadIdEqualTo(id)
        .sortByCreatedTimeDesc()
        .findFirst();
  }

  Future<List<WorkPlaceMessagesEntity>> getMessagesLimit(int offset) async {
    return await isar.workPlaceMessagesEntitys
        .where()
        .sortByWpThreadId()
        .thenByCreatedTimeDesc()
        .offset(offset)
        .limit(200000)
        .findAll();
  }

  Future<List<WorkPlaceConversationEntity>> getThreadsLimit(int offset) async {
    return await isar.workPlaceConversationEntitys
        .where()
        .offset(offset)
        .limit(1)
        .findAll();
  }

  Future saveMessages(List<WorkPlaceMessagesEntity> messageEntities) async {
    await isar.writeTxn(() async {
      await isar.workPlaceMessagesEntitys.putAll(messageEntities);
    });
  }

  Future saveMessageAttachments(
      List<WorkPlaceConversationAttachmentsEntity> attachments) async {
    await isar.writeTxn(() async {
      await isar.workPlaceConversationAttachmentsEntitys.putAll(attachments);
    });
  }

  Future saveSticker(WorkPlaceStickerEntity sticker) async {
    await isar.writeTxn(() async {
      await isar.workPlaceStickerEntitys.put(sticker);
    });
  }

  Future updateThread(WorkPlaceConversationEntity conversationEntity) async {
    await isar.writeTxn(() async {
      await conversationEntity.messages.save();
      await conversationEntity.participants.save();
    });
  }

  Future reloadThread(WorkPlaceConversationEntity conversationEntity) async {
    await isar.writeTxn(() async {
      await conversationEntity.messages.load();
      await conversationEntity.participants.load();
    });
  }

  Future updateMessage(WorkPlaceMessagesEntity messagesEntity) async {
    await isar.writeTxn(() async {
      await messagesEntity.to.save();
      await messagesEntity.from.save();
      await messagesEntity.attachments.save();
      await messagesEntity.sticker.save();
    });
  }

  Future reloadMessage(WorkPlaceMessagesEntity messagesEntity) async {
    await isar.writeTxn(() async {
      await messagesEntity.attachments.load();
      await messagesEntity.from.load();
      await messagesEntity.to.load();
      await messagesEntity.sticker.load();
    });
  }

  Future<Iterable<WorkPlaceConversationEntity>>
      missingConversationAttachments() async {
    final threads = await getAllThreads();
    // final messages = await getAllMessages();
    final output = <WorkPlaceConversationEntity>[];

    for (var thread in threads) {
      if (thread.messages.isEmpty) continue;

      for (var message in thread.messages) {
        if (message.attachments.isEmpty && message.sticker.value == null) {
          continue;
        }

        for (var element in message.attachments) {
          if (element.localFilePath == null || element.gpLink == null) {
            output.add(thread);
          }
        }

        if (message.sticker.value?.localFilePath == null ||
            message.sticker.value?.gpLink == null) {
          output.add(thread);
        }
      }
    }

    return output.toSet();
  }

  Future<Iterable<WorkPlaceMessagesEntity>> missingMessageAttachments() async {
    final messages = await getAllMessages();
    final output = <WorkPlaceMessagesEntity>[];

    for (var message in messages) {
      if (message.attachments.isEmpty && message.sticker.value == null) {
        continue;
      }

      for (var element in message.attachments) {
        if (element.localFilePath == null || element.gpLink == null) {
          output.add(message);
        }
      }

      if (message.sticker.value?.localFilePath == null ||
          message.sticker.value?.gpLink == null) {
        output.add(message);
      }
    }

    return output.toSet();
  }
}

mixin _AttachmentServiceMixin on _BaseWorkPlaceLocalService {
  Future<List<WorkPlaceAttachmentEntity>> flatternAttachments(
    WorkPlaceFeedEntity feed,
  ) async {
    return feed.attachments
        .where((element) => element.type != AttachmentType.question)
        .toList();
  }

  Future saveAttachments(List<WorkPlaceAttachmentEntity> attachments) async {
    await isar.writeTxn(() async {
      await isar.workPlaceAttachmentEntitys.putAll(attachments);
    });
  }

  Future saveAttachment(WorkPlaceAttachmentEntity attachmentEntity) async {
    await isar.writeTxn(() async {
      await isar.workPlaceAttachmentEntitys.put(attachmentEntity);
    });
  }

  Future clearAttachments() async {
    await isar.writeTxn(() async {
      await isar.workPlaceAttachmentEntitys.where().deleteAll();
    });
  }

  Future updateToRelativeFilePath() async {
    final attachments = await isar.workPlaceAttachmentEntitys
        .filter()
        .localFilePathIsNotEmpty()
        .findAll();

    for (var element in attachments) {
      if (element.localFilePath != null &&
          element.localFilePath?.isNotEmpty == true) {
        element.localFilePath = basename(element.localFilePath ?? '');
      }
    }

    await isar.writeTxn(() async {
      await isar.workPlaceAttachmentEntitys.putAll(attachments);
    });
  }
}

mixin _CrawlCheckpointMixin on _BaseWorkPlaceLocalService {
  Future saveCheckpoint(CrawlCheckpoint checkpoint) async {
    await isar.writeTxn(() async {
      await isar.crawlCheckpoints.put(checkpoint);
    });
  }

  Future<CrawlCheckpoint?> latestCheckpoint(GPBaseCrawlType crawlType) async {
    return await isar.crawlCheckpoints
        .filter()
        .crawlTypeEqualTo(crawlType)
        .findFirst();
  }

  Future<List<CrawlCheckpoint>?> getNotDoneCheckpoint(GPBaseCrawlType crawlType) async {
    return await isar.crawlCheckpoints
        .filter()
        .crawlTypeEqualTo(crawlType)
        .isDoneEqualTo(false)
        .findAll();
  }

  Future<CrawlCheckpoint?> latestCheckpointById(String id) async {
    return await isar.crawlCheckpoints.filter().idEqualTo(id).findFirst();
  }

  Future clearCheckpoint(GPBaseCrawlType crawlType) async {
    await isar.writeTxn(() async {
      await isar.crawlCheckpoints
          .filter()
          .crawlTypeEqualTo(crawlType)
          .deleteAll();
    });
  }
}

mixin _GPDashBoardMixin on _BaseWorkPlaceLocalService {
  Future<GPDashboardEntity?> getGPDashBoardEntity() async {
    var entity = await isar.gPDashboardEntitys.where().findFirst();

    if (entity == null) {
      entity = GPDashboardEntity();
      await isar.writeTxn(
        () async => isar.gPDashboardEntitys.put(entity!),
      );
    }

    return entity;
  }

  Future updateTotalUploadSize(int? size) async {
    if (size == null) return;

    try {
      final dashboardEntity = await getGPDashBoardEntity();

      if (dashboardEntity != null) {
        dashboardEntity.totalUploadFile++;
        dashboardEntity.totalUploadSize += size;

        await isar.writeTxn(
          () => isar.gPDashboardEntitys.put(dashboardEntity),
        );
      }
    } catch (ex) {
      log('_updateTotalDownloadSize error -> $ex');
    }
  }

  Future updateTotalDownloadSize(String filePath) async {
    try {
      final dashboardEntity = await getGPDashBoardEntity();

      if (dashboardEntity != null) {
        final File file = File(filePath);
        final bool isFileExists = file.existsSync();

        if (isFileExists) {
          dashboardEntity.totalDownloadFile++;
          dashboardEntity.totalDownloadSize += file.lengthSync();

          await isar.writeTxn(
            () => isar.gPDashboardEntitys.put(dashboardEntity),
          );
        }
      }
    } catch (ex) {
      log('_updateTotalDownloadSize error -> $ex');
    }
  }
}

mixin _GPLog on _BaseWorkPlaceLocalService {
  Future saveLog(String message, GPLogType type) async {
    await isar.writeTxn(() async {
      await isar.gPLogEntitys.put(GPLogEntity(
          cUrl: '',
          path: '',
          error: message,
          createdAt: DateTime.now(),
          logType: type,
          data: ''));
    });
  }
}
