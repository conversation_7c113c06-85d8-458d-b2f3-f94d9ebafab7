/*
 * Created Date: Monday, 10th June 2024, 15:57:40
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 11th June 2024 17:59:22
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:dio/dio.dart' hide Headers;
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/app/constant/gapo_url.constants.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'workspace.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kGapoWorkWorkspaceService')
@RestApi()
abstract class WorkSpaceService {
  @FactoryMethod()
  factory WorkSpaceService(
    @Named('kGapoWorkDio') Dio dio, {
    @Named('kGapoWorkD<PERSON>in') String? baseUrl,
  }) = _WorkSpaceService;

  @POST(GPConstants.kGapoWorkWorkspaceInviteEmail)
  Future<ApiResponseV2<InviteWorkspaceResponse>> inviteEmail({
    @Body() required GPInviteWsParams params,
  });

  @POST(GPConstants.kGapoWorkWorkspaceInvitePhone)
  Future<ApiResponseV2<InviteWorkspaceResponse>> invitePhone({
    @Body(nullToAbsent: true) required GPInviteWsParams params,
  });
}
