// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'download_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPDownloadParams {
  String get downloadUrl => throw _privateConstructorUsedError;
  String get savedFilePath => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $GPDownloadParamsCopyWith<GPDownloadParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPDownloadParamsCopyWith<$Res> {
  factory $GPDownloadParamsCopyWith(
          GPDownloadParams value, $Res Function(GPDownloadParams) then) =
      _$GPDownloadParamsCopyWithImpl<$Res, GPDownloadParams>;
  @useResult
  $Res call({String downloadUrl, String savedFilePath});
}

/// @nodoc
class _$GPDownloadParamsCopyWithImpl<$Res, $Val extends GPDownloadParams>
    implements $GPDownloadParamsCopyWith<$Res> {
  _$GPDownloadParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? downloadUrl = null,
    Object? savedFilePath = null,
  }) {
    return _then(_value.copyWith(
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      savedFilePath: null == savedFilePath
          ? _value.savedFilePath
          : savedFilePath // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPDownloadParamsImplCopyWith<$Res>
    implements $GPDownloadParamsCopyWith<$Res> {
  factory _$$GPDownloadParamsImplCopyWith(_$GPDownloadParamsImpl value,
          $Res Function(_$GPDownloadParamsImpl) then) =
      __$$GPDownloadParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String downloadUrl, String savedFilePath});
}

/// @nodoc
class __$$GPDownloadParamsImplCopyWithImpl<$Res>
    extends _$GPDownloadParamsCopyWithImpl<$Res, _$GPDownloadParamsImpl>
    implements _$$GPDownloadParamsImplCopyWith<$Res> {
  __$$GPDownloadParamsImplCopyWithImpl(_$GPDownloadParamsImpl _value,
      $Res Function(_$GPDownloadParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? downloadUrl = null,
    Object? savedFilePath = null,
  }) {
    return _then(_$GPDownloadParamsImpl(
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      savedFilePath: null == savedFilePath
          ? _value.savedFilePath
          : savedFilePath // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GPDownloadParamsImpl implements _GPDownloadParams {
  const _$GPDownloadParamsImpl(
      {required this.downloadUrl, this.savedFilePath = ''});

  @override
  final String downloadUrl;
  @override
  @JsonKey()
  final String savedFilePath;

  @override
  String toString() {
    return 'GPDownloadParams(downloadUrl: $downloadUrl, savedFilePath: $savedFilePath)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPDownloadParamsImpl &&
            (identical(other.downloadUrl, downloadUrl) ||
                other.downloadUrl == downloadUrl) &&
            (identical(other.savedFilePath, savedFilePath) ||
                other.savedFilePath == savedFilePath));
  }

  @override
  int get hashCode => Object.hash(runtimeType, downloadUrl, savedFilePath);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPDownloadParamsImplCopyWith<_$GPDownloadParamsImpl> get copyWith =>
      __$$GPDownloadParamsImplCopyWithImpl<_$GPDownloadParamsImpl>(
          this, _$identity);
}

abstract class _GPDownloadParams implements GPDownloadParams {
  const factory _GPDownloadParams(
      {required final String downloadUrl,
      final String savedFilePath}) = _$GPDownloadParamsImpl;

  @override
  String get downloadUrl;
  @override
  String get savedFilePath;
  @override
  @JsonKey(ignore: true)
  _$$GPDownloadParamsImplCopyWith<_$GPDownloadParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
