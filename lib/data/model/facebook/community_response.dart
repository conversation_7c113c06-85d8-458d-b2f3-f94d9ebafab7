import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:json_annotation/json_annotation.dart';

part 'community_response.g.dart';

@JsonSerializable(createToJson: false)
class FaceBookCommunityResponse extends GPBaseOutput {
  FaceBookCommunityResponse({
    this.name,
    this.privacy,
    required this.id,
  });

  String? name;
  String? privacy;
  String id;

  factory FaceBookCommunityResponse.fromJson(Map<String, dynamic> json) =>
      _$FaceBookCommunityResponseFromJson(json);
}
