/*
 * Created Date: 5/12/2023 16:05:26
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 28th December 2023 15:30:33
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2023 GAPO
 */

import 'package:gp_core_v2/base/usecase/usecase.dart';
import 'package:json_annotation/json_annotation.dart';

part 'auth_check_email_request.g.dart';

@JsonSerializable()
class AuthCheckEmailRequest extends GPBaseInput {
  AuthCheckEmailRequest(
    this.email,
    this.phoneNumber,
  );

  final String email;

  @JsonKey(name: "phone_number")
  final String phoneNumber;

  factory AuthCheckEmailRequest.fromJson(Map<String, dynamic> json) =>
      _$AuthCheckEmailRequestFromJson(json);
      
  Map<String, dynamic> toJson() => _$AuthCheckEmailRequestToJson(this);
}
