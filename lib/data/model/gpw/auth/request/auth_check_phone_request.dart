/*
 * Created Date: 5/12/2023 16:05:26
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 28th December 2023 15:30:33
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2023 GAPO
 */

import 'package:gp_core_v2/base/usecase/usecase.dart';
import 'package:json_annotation/json_annotation.dart';

part 'auth_check_phone_request.g.dart';

@JsonSerializable()
class AuthCheckPhoneRequest extends GPBaseInput {
  AuthCheckPhoneRequest(
    this.phoneNumber,
  );

  @JsonKey(name: "phone_number")
  final String phoneNumber;

  factory AuthCheckPhoneRequest.fromJson(Map<String, dynamic> json) =>
      _$AuthCheckPhoneRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AuthCheckPhoneRequestToJson(this);
}
