// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_auth_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthParams {
  @JsonKey(name: 'client_id')
  String get clientId => throw _privateConstructorUsedError;
  @JsonKey(name: 'device_model')
  String get deviceModel => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  @JsonKey(name: 'trusted_device')
  bool get trustedDevice => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'device_id')
  String get deviceId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AuthParamsCopyWith<AuthParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthParamsCopyWith<$Res> {
  factory $AuthParamsCopyWith(
          AuthParams value, $Res Function(AuthParams) then) =
      _$AuthParamsCopyWithImpl<$Res, AuthParams>;
  @useResult
  $Res call(
      {@JsonKey(name: 'client_id') String clientId,
      @JsonKey(name: 'device_model') String deviceModel,
      String password,
      @JsonKey(name: 'trusted_device') bool trustedDevice,
      String email,
      @JsonKey(name: 'device_id') String deviceId});
}

/// @nodoc
class _$AuthParamsCopyWithImpl<$Res, $Val extends AuthParams>
    implements $AuthParamsCopyWith<$Res> {
  _$AuthParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? clientId = null,
    Object? deviceModel = null,
    Object? password = null,
    Object? trustedDevice = null,
    Object? email = null,
    Object? deviceId = null,
  }) {
    return _then(_value.copyWith(
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
      deviceModel: null == deviceModel
          ? _value.deviceModel
          : deviceModel // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      trustedDevice: null == trustedDevice
          ? _value.trustedDevice
          : trustedDevice // ignore: cast_nullable_to_non_nullable
              as bool,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthParamsImplCopyWith<$Res>
    implements $AuthParamsCopyWith<$Res> {
  factory _$$AuthParamsImplCopyWith(
          _$AuthParamsImpl value, $Res Function(_$AuthParamsImpl) then) =
      __$$AuthParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'client_id') String clientId,
      @JsonKey(name: 'device_model') String deviceModel,
      String password,
      @JsonKey(name: 'trusted_device') bool trustedDevice,
      String email,
      @JsonKey(name: 'device_id') String deviceId});
}

/// @nodoc
class __$$AuthParamsImplCopyWithImpl<$Res>
    extends _$AuthParamsCopyWithImpl<$Res, _$AuthParamsImpl>
    implements _$$AuthParamsImplCopyWith<$Res> {
  __$$AuthParamsImplCopyWithImpl(
      _$AuthParamsImpl _value, $Res Function(_$AuthParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? clientId = null,
    Object? deviceModel = null,
    Object? password = null,
    Object? trustedDevice = null,
    Object? email = null,
    Object? deviceId = null,
  }) {
    return _then(_$AuthParamsImpl(
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
      deviceModel: null == deviceModel
          ? _value.deviceModel
          : deviceModel // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      trustedDevice: null == trustedDevice
          ? _value.trustedDevice
          : trustedDevice // ignore: cast_nullable_to_non_nullable
              as bool,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$AuthParamsImpl implements _AuthParams {
  const _$AuthParamsImpl(
      {@JsonKey(name: 'client_id') required this.clientId,
      @JsonKey(name: 'device_model') required this.deviceModel,
      required this.password,
      @JsonKey(name: 'trusted_device') this.trustedDevice = true,
      required this.email,
      @JsonKey(name: 'device_id') this.deviceId = ''});

  @override
  @JsonKey(name: 'client_id')
  final String clientId;
  @override
  @JsonKey(name: 'device_model')
  final String deviceModel;
  @override
  final String password;
  @override
  @JsonKey(name: 'trusted_device')
  final bool trustedDevice;
  @override
  final String email;
  @override
  @JsonKey(name: 'device_id')
  final String deviceId;

  @override
  String toString() {
    return 'AuthParams(clientId: $clientId, deviceModel: $deviceModel, password: $password, trustedDevice: $trustedDevice, email: $email, deviceId: $deviceId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthParamsImpl &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId) &&
            (identical(other.deviceModel, deviceModel) ||
                other.deviceModel == deviceModel) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.trustedDevice, trustedDevice) ||
                other.trustedDevice == trustedDevice) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, clientId, deviceModel, password,
      trustedDevice, email, deviceId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthParamsImplCopyWith<_$AuthParamsImpl> get copyWith =>
      __$$AuthParamsImplCopyWithImpl<_$AuthParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthParamsImplToJson(
      this,
    );
  }
}

abstract class _AuthParams implements AuthParams {
  const factory _AuthParams(
      {@JsonKey(name: 'client_id') required final String clientId,
      @JsonKey(name: 'device_model') required final String deviceModel,
      required final String password,
      @JsonKey(name: 'trusted_device') final bool trustedDevice,
      required final String email,
      @JsonKey(name: 'device_id') final String deviceId}) = _$AuthParamsImpl;

  @override
  @JsonKey(name: 'client_id')
  String get clientId;
  @override
  @JsonKey(name: 'device_model')
  String get deviceModel;
  @override
  String get password;
  @override
  @JsonKey(name: 'trusted_device')
  bool get trustedDevice;
  @override
  String get email;
  @override
  @JsonKey(name: 'device_id')
  String get deviceId;
  @override
  @JsonKey(ignore: true)
  _$$AuthParamsImplCopyWith<_$AuthParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
