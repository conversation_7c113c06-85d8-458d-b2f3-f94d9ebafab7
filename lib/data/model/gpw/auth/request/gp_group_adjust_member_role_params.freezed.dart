// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_group_adjust_member_role_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPGroupAdjustMemberRoleParams {
  @JsonKey(name: 'user_id')
  String get userId => throw _privateConstructorUsedError;
  GPGroupRole get role => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GPGroupAdjustMemberRoleParamsCopyWith<GPGroupAdjustMemberRoleParams>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPGroupAdjustMemberRoleParamsCopyWith<$Res> {
  factory $GPGroupAdjustMemberRoleParamsCopyWith(
          GPGroupAdjustMemberRoleParams value,
          $Res Function(GPGroupAdjustMemberRoleParams) then) =
      _$GPGroupAdjustMemberRoleParamsCopyWithImpl<$Res,
          GPGroupAdjustMemberRoleParams>;
  @useResult
  $Res call({@JsonKey(name: 'user_id') String userId, GPGroupRole role});
}

/// @nodoc
class _$GPGroupAdjustMemberRoleParamsCopyWithImpl<$Res,
        $Val extends GPGroupAdjustMemberRoleParams>
    implements $GPGroupAdjustMemberRoleParamsCopyWith<$Res> {
  _$GPGroupAdjustMemberRoleParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? role = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as GPGroupRole,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPGroupAdjustMemberRoleParamsImplCopyWith<$Res>
    implements $GPGroupAdjustMemberRoleParamsCopyWith<$Res> {
  factory _$$GPGroupAdjustMemberRoleParamsImplCopyWith(
          _$GPGroupAdjustMemberRoleParamsImpl value,
          $Res Function(_$GPGroupAdjustMemberRoleParamsImpl) then) =
      __$$GPGroupAdjustMemberRoleParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'user_id') String userId, GPGroupRole role});
}

/// @nodoc
class __$$GPGroupAdjustMemberRoleParamsImplCopyWithImpl<$Res>
    extends _$GPGroupAdjustMemberRoleParamsCopyWithImpl<$Res,
        _$GPGroupAdjustMemberRoleParamsImpl>
    implements _$$GPGroupAdjustMemberRoleParamsImplCopyWith<$Res> {
  __$$GPGroupAdjustMemberRoleParamsImplCopyWithImpl(
      _$GPGroupAdjustMemberRoleParamsImpl _value,
      $Res Function(_$GPGroupAdjustMemberRoleParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? role = null,
  }) {
    return _then(_$GPGroupAdjustMemberRoleParamsImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as GPGroupRole,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$GPGroupAdjustMemberRoleParamsImpl
    implements _GPGroupAdjustMemberRoleParams {
  const _$GPGroupAdjustMemberRoleParamsImpl(
      {@JsonKey(name: 'user_id') required this.userId, required this.role});

  @override
  @JsonKey(name: 'user_id')
  final String userId;
  @override
  final GPGroupRole role;

  @override
  String toString() {
    return 'GPGroupAdjustMemberRoleParams(userId: $userId, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPGroupAdjustMemberRoleParamsImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.role, role) || other.role == role));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userId, role);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPGroupAdjustMemberRoleParamsImplCopyWith<
          _$GPGroupAdjustMemberRoleParamsImpl>
      get copyWith => __$$GPGroupAdjustMemberRoleParamsImplCopyWithImpl<
          _$GPGroupAdjustMemberRoleParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPGroupAdjustMemberRoleParamsImplToJson(
      this,
    );
  }
}

abstract class _GPGroupAdjustMemberRoleParams
    implements GPGroupAdjustMemberRoleParams {
  const factory _GPGroupAdjustMemberRoleParams(
      {@JsonKey(name: 'user_id') required final String userId,
      required final GPGroupRole role}) = _$GPGroupAdjustMemberRoleParamsImpl;

  @override
  @JsonKey(name: 'user_id')
  String get userId;
  @override
  GPGroupRole get role;
  @override
  @JsonKey(ignore: true)
  _$$GPGroupAdjustMemberRoleParamsImplCopyWith<
          _$GPGroupAdjustMemberRoleParamsImpl>
      get copyWith => throw _privateConstructorUsedError;
}
