// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'gp_group_invite_params.freezed.dart';
part 'gp_group_invite_params.g.dart';

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class GPGroupInviteParams with _$GPGroupInviteParams {
  const factory GPGroupInviteParams({
    @JsonKey(name: 'group_id') required String groupId,
    required List<String> users,
  }) = _GPGroupInviteParams;
}
