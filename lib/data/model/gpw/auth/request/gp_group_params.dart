// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_fbwp_crawler/domain/entity/enums/enums.dart';

part 'gp_group_params.freezed.dart';
part 'gp_group_params.g.dart';

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class GPGroupParams with _$GPGroupParams {
  const factory GPGroupParams({
    required String name,
    @Default('') String cover,
    @Default(false) @Json<PERSON>ey(name: 'link_chat') bool linkChat,
    String? description,
    @JsonKey(toJson: _gpPrivacyToJson)
    @Default(GPGroupPrivacy.public) GPGroupPrivacy privacy,
    @JsonKey(toJson: _gpDiscoverabilityToJson)
    @Default(GPGroupDiscoverability.visible)
    GPGroupDiscoverability discoverability,
  }) = _GPGroupParams;
}

int _gpDiscoverabilityToJson(GPGroupDiscoverability input) {
  switch (input) {
    case GPGroupDiscoverability.visible:
      return 1;
    case GPGroupDiscoverability.hidden:
      return 0;
  }
}

int _gpPrivacyToJson(GPGroupPrivacy input) {
  switch (input) {
    case GPGroupPrivacy.public:
      return 2;
    case GPGroupPrivacy.closed:
      return 1;
  }
}
