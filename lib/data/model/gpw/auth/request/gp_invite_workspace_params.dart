import 'package:freezed_annotation/freezed_annotation.dart';

part 'gp_invite_workspace_params.freezed.dart';
part 'gp_invite_workspace_params.g.dart';

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class GPInviteWsParams with _$GPInviteWsParams {
  const factory GPInviteWsParams({
    String? email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'display_name') String? displayName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'phone_number') String? phoneNumber,
  }) = _GPInviteWsParams;
}