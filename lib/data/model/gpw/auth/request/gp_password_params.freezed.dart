// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_password_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPSetPasswordParams {
  String get password => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GPSetPasswordParamsCopyWith<GPSetPasswordParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPSetPasswordParamsCopyWith<$Res> {
  factory $GPSetPasswordParamsCopyWith(
          GPSetPasswordParams value, $Res Function(GPSetPasswordParams) then) =
      _$GPSetPasswordParamsCopyWithImpl<$Res, GPSetPasswordParams>;
  @useResult
  $Res call({String password});
}

/// @nodoc
class _$GPSetPasswordParamsCopyWithImpl<$Res, $Val extends GPSetPasswordParams>
    implements $GPSetPasswordParamsCopyWith<$Res> {
  _$GPSetPasswordParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? password = null,
  }) {
    return _then(_value.copyWith(
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPSetPasswordParamsImplCopyWith<$Res>
    implements $GPSetPasswordParamsCopyWith<$Res> {
  factory _$$GPSetPasswordParamsImplCopyWith(_$GPSetPasswordParamsImpl value,
          $Res Function(_$GPSetPasswordParamsImpl) then) =
      __$$GPSetPasswordParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String password});
}

/// @nodoc
class __$$GPSetPasswordParamsImplCopyWithImpl<$Res>
    extends _$GPSetPasswordParamsCopyWithImpl<$Res, _$GPSetPasswordParamsImpl>
    implements _$$GPSetPasswordParamsImplCopyWith<$Res> {
  __$$GPSetPasswordParamsImplCopyWithImpl(_$GPSetPasswordParamsImpl _value,
      $Res Function(_$GPSetPasswordParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? password = null,
  }) {
    return _then(_$GPSetPasswordParamsImpl(
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$GPSetPasswordParamsImpl implements _GPSetPasswordParams {
  const _$GPSetPasswordParamsImpl({required this.password});

  @override
  final String password;

  @override
  String toString() {
    return 'GPSetPasswordParams(password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPSetPasswordParamsImpl &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, password);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPSetPasswordParamsImplCopyWith<_$GPSetPasswordParamsImpl> get copyWith =>
      __$$GPSetPasswordParamsImplCopyWithImpl<_$GPSetPasswordParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPSetPasswordParamsImplToJson(
      this,
    );
  }
}

abstract class _GPSetPasswordParams implements GPSetPasswordParams {
  const factory _GPSetPasswordParams({required final String password}) =
      _$GPSetPasswordParamsImpl;

  @override
  String get password;
  @override
  @JsonKey(ignore: true)
  _$$GPSetPasswordParamsImplCopyWith<_$GPSetPasswordParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
