// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'gp_signup_params.freezed.dart';
part 'gp_signup_params.g.dart';

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class GPSignupParams with _$GPSignupParams {
  const factory GPSignupParams({
    String? email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'phone_number') String? phoneNumber,
    @Default('777777') String otp,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'client_id')
    @Default('cuxlp0ugglm3krp1ab81')
    String clientId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'trusted_device') @Default(false) bool trustedDevice,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'device_id')
    @Default('e108c54e-a6d5-4ad5-80e8-7c556f49991e')
    String deviceId,
  }) = _GPSignupParams;
}
