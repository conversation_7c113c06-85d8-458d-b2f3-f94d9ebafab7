// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_signup_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPSignupParams {
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'phone_number')
  String? get phoneNumber => throw _privateConstructorUsedError;
  String get otp => throw _privateConstructorUsedError;
  @JsonKey(name: 'client_id')
  String get clientId => throw _privateConstructorUsedError;
  @JsonKey(name: 'trusted_device')
  bool get trustedDevice => throw _privateConstructorUsedError;
  @JsonKey(name: 'device_id')
  String get deviceId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GPSignupParamsCopyWith<GPSignupParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPSignupParamsCopyWith<$Res> {
  factory $GPSignupParamsCopyWith(
          GPSignupParams value, $Res Function(GPSignupParams) then) =
      _$GPSignupParamsCopyWithImpl<$Res, GPSignupParams>;
  @useResult
  $Res call(
      {String? email,
      @JsonKey(name: 'phone_number') String? phoneNumber,
      String otp,
      @JsonKey(name: 'client_id') String clientId,
      @JsonKey(name: 'trusted_device') bool trustedDevice,
      @JsonKey(name: 'device_id') String deviceId});
}

/// @nodoc
class _$GPSignupParamsCopyWithImpl<$Res, $Val extends GPSignupParams>
    implements $GPSignupParamsCopyWith<$Res> {
  _$GPSignupParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? otp = null,
    Object? clientId = null,
    Object? trustedDevice = null,
    Object? deviceId = null,
  }) {
    return _then(_value.copyWith(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
      trustedDevice: null == trustedDevice
          ? _value.trustedDevice
          : trustedDevice // ignore: cast_nullable_to_non_nullable
              as bool,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPSignupParamsImplCopyWith<$Res>
    implements $GPSignupParamsCopyWith<$Res> {
  factory _$$GPSignupParamsImplCopyWith(_$GPSignupParamsImpl value,
          $Res Function(_$GPSignupParamsImpl) then) =
      __$$GPSignupParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? email,
      @JsonKey(name: 'phone_number') String? phoneNumber,
      String otp,
      @JsonKey(name: 'client_id') String clientId,
      @JsonKey(name: 'trusted_device') bool trustedDevice,
      @JsonKey(name: 'device_id') String deviceId});
}

/// @nodoc
class __$$GPSignupParamsImplCopyWithImpl<$Res>
    extends _$GPSignupParamsCopyWithImpl<$Res, _$GPSignupParamsImpl>
    implements _$$GPSignupParamsImplCopyWith<$Res> {
  __$$GPSignupParamsImplCopyWithImpl(
      _$GPSignupParamsImpl _value, $Res Function(_$GPSignupParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? otp = null,
    Object? clientId = null,
    Object? trustedDevice = null,
    Object? deviceId = null,
  }) {
    return _then(_$GPSignupParamsImpl(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
      trustedDevice: null == trustedDevice
          ? _value.trustedDevice
          : trustedDevice // ignore: cast_nullable_to_non_nullable
              as bool,
      deviceId: null == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$GPSignupParamsImpl implements _GPSignupParams {
  const _$GPSignupParamsImpl(
      {this.email,
      @JsonKey(name: 'phone_number') this.phoneNumber,
      this.otp = '777777',
      @JsonKey(name: 'client_id') this.clientId = 'cuxlp0ugglm3krp1ab81',
      @JsonKey(name: 'trusted_device') this.trustedDevice = false,
      @JsonKey(name: 'device_id')
      this.deviceId = 'e108c54e-a6d5-4ad5-80e8-7c556f49991e'});

  @override
  final String? email;
  @override
  @JsonKey(name: 'phone_number')
  final String? phoneNumber;
  @override
  @JsonKey()
  final String otp;
  @override
  @JsonKey(name: 'client_id')
  final String clientId;
  @override
  @JsonKey(name: 'trusted_device')
  final bool trustedDevice;
  @override
  @JsonKey(name: 'device_id')
  final String deviceId;

  @override
  String toString() {
    return 'GPSignupParams(email: $email, phoneNumber: $phoneNumber, otp: $otp, clientId: $clientId, trustedDevice: $trustedDevice, deviceId: $deviceId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPSignupParamsImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.otp, otp) || other.otp == otp) &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId) &&
            (identical(other.trustedDevice, trustedDevice) ||
                other.trustedDevice == trustedDevice) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, email, phoneNumber, otp, clientId, trustedDevice, deviceId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPSignupParamsImplCopyWith<_$GPSignupParamsImpl> get copyWith =>
      __$$GPSignupParamsImplCopyWithImpl<_$GPSignupParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPSignupParamsImplToJson(
      this,
    );
  }
}

abstract class _GPSignupParams implements GPSignupParams {
  const factory _GPSignupParams(
          {final String? email,
          @JsonKey(name: 'phone_number') final String? phoneNumber,
          final String otp,
          @JsonKey(name: 'client_id') final String clientId,
          @JsonKey(name: 'trusted_device') final bool trustedDevice,
          @JsonKey(name: 'device_id') final String deviceId}) =
      _$GPSignupParamsImpl;

  @override
  String? get email;
  @override
  @JsonKey(name: 'phone_number')
  String? get phoneNumber;
  @override
  String get otp;
  @override
  @JsonKey(name: 'client_id')
  String get clientId;
  @override
  @JsonKey(name: 'trusted_device')
  bool get trustedDevice;
  @override
  @JsonKey(name: 'device_id')
  String get deviceId;
  @override
  @JsonKey(ignore: true)
  _$$GPSignupParamsImplCopyWith<_$GPSignupParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
