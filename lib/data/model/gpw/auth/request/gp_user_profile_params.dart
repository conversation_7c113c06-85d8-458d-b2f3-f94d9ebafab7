// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'gp_user_profile_params.freezed.dart';
part 'gp_user_profile_params.g.dart';

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class GPUserProfileParams with _$GPUserProfileParams {
  const factory GPUserProfileParams({
    @JsonKey(name: 'display_name') required String displayName,
    String? avatar,
    @Json<PERSON>ey(name: 'avatar_data') GPUserProfilePictureData? avatarData,
    @J<PERSON><PERSON><PERSON>(name: 'cover_data') GPUserProfilePictureData? coverData,
  }) = _GPUserProfileParams;
}

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class GPUserProfilePictureData with _$GPUserProfilePictureData {
  const factory GPUserProfilePictureData({
    String? id,
    String? source,
    String? src,
    int? width,
    int? height,
  }) = _GPUserProfilePictureData;
}
