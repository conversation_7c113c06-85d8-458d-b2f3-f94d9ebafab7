/*
 * Created Date: 5/12/2023 16:05:26
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 14:53:31
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:json_annotation/json_annotation.dart';

part 'auth_check_phone_response.g.dart';

@JsonSerializable()
class AuthCheckPhoneResponse {
  AuthCheckPhoneResponse({
    required this.userId,
    this.salt,
  });

  @JsonKey(name: 'user_id')
  final int userId;

  final String? salt;

  factory AuthCheckPhoneResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthCheckPhoneResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AuthCheckPhoneResponseToJson(this);
}
