import 'package:json_annotation/json_annotation.dart';

part 'invite_ws_reponse.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, createToJson: false)
class InviteWorkspaceResponse {
  InviteWorkspaceResponse({
    this.user,
  });

  final UserInvite? user;

  factory InviteWorkspaceResponse.fromJson(Map<String, dynamic> json) =>
      _$InviteWorkspaceResponseFromJson(json);
}

@JsonSerializable(fieldRename: FieldRename.snake, createToJson: false)
class UserInvite {
  UserInvite({
    this.id,
  });
  final int? id;

  factory UserInvite.fromJson(Map<String, dynamic> json) =>
      _$UserInviteFromJson(json);
}
