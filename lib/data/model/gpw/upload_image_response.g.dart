// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_image_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPUploadImageResponseModel _$GPUploadImageResponseModelFromJson(
        Map<String, dynamic> json) =>
    GPUploadImageResponseModel(
      id: json['id'] as String?,
      userId: json['user_id'] as String?,
      src: json['src'] as String?,
      url: json['url'] == null
          ? null
          : UploadFileURLResponseModel.fromJson(
              json['url'] as Map<String, dynamic>),
      type: json['type'] as String?,
      source: json['source'] as String?,
      size: (json['size'] as num?)?.toInt(),
      fileType: json['file_type'] as String?,
      category: json['category'] as String?,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      quality: json['quality'] as String?,
      name: json['name'] as String?,
    )
      ..fileName = json['fileName'] as String?
      ..fileLink = json['file_link'] as String?;

Map<String, dynamic> _$GPUploadImageResponseModelToJson(
        GPUploadImageResponseModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'src': instance.src,
      'url': instance.url,
      'type': instance.type,
      'source': instance.source,
      'size': instance.size,
      'file_type': instance.fileType,
      'category': instance.category,
      'width': instance.width,
      'height': instance.height,
      'quality': instance.quality,
      'fileName': instance.fileName,
      'file_link': instance.fileLink,
      'name': instance.name,
    };
