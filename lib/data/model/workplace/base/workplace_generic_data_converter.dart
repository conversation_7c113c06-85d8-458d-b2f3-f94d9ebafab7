import 'package:gp_core_v2/base/base.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:json_annotation/json_annotation.dart';

class WorkPlaceModelConverter<T> implements JsonConverter<T, Object?> {
  const WorkPlaceModelConverter();

  @override
  T fromJson(Object? json) {
    if (json is List<T>) {
      final data =
          (json).map((e) => genericParser(e as Map<String, dynamic>)).toList();

      return data as T;
    } else if (json is Map<String, dynamic>) {
      return genericParser(json);
    } else {
      throw Exception('');
    }
  }

  T genericParser(Map<String, dynamic> json) {
    if (T == WorkPlaceGroupResponse) {
      return WorkPlaceGroupResponse.fromJson(json) as T;
    } else if (T == WorkPlaceUser) {
      return WorkPlaceUser.fromJson(json) as T;
    } else if (T == WorkPlaceFeedsResponse) {
      return WorkPlaceFeedsResponse.fromJson(json) as T;
    } else if (T == WorkPlacePostAttachmentsResponse) {
      return WorkPlacePostAttachmentsResponse.fromJson(json) as T;
    } else if (T == WorkPlaceCommentsResponse) {
      return WorkPlaceCommentsResponse.fromJson(json) as T;
    } else if (T == WorkPlaceUserConversationsResponse) {
      return WorkPlaceUserConversationsResponse.fromJson(json) as T;
    } else if (T == Messages) {
      return Messages.fromJson(json) as T;
    } else if (T == WorkPlaceConversationAttachmentsResponse) {
      return WorkPlaceConversationAttachmentsResponse.fromJson(json) as T;
    } else if (T == WorkPlaceReaction) {
      return WorkPlaceReaction.fromJson(json) as T;
    } else if (T == WorkPlaceMessageTags) {
      return WorkPlaceMessageTags.fromJson(json) as T;
    }

    throw ParseException(ParseExceptionKind.invalidSourceFormat,
        'Can not parse json for $T type');
  }

  @override
  Map<String, dynamic> toJson(T object) {
    throw ParseException(
        ParseExceptionKind.invalidSourceFormat, 'Cannot serialize $T to JSON');
  }
}
