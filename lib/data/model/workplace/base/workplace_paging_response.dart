import 'package:freezed_annotation/freezed_annotation.dart';

part 'workplace_paging_response.freezed.dart';
part 'workplace_paging_response.g.dart';

@Freezed(copyWith: true, toJson: false)
class WorkPlacePagingResponse with _$WorkPlacePagingResponse {
  factory WorkPlacePagingResponse({
    WorkPlacePagingCursor? cursors,
    String? previous,
    String? next,
  }) = _WorkPlacePagingResponse;

  factory WorkPlacePagingResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkPlacePagingResponseFromJson(json);
}

extension WorkPlacePagingResponseExt on WorkPlacePagingResponse {
  Map<String, String>? get nextQueries {
    if (next == null || next?.isEmpty == true) return null;

    final uri = Uri.parse(next!);

    return uri.queryParameters;

    // return uri.queryParameters['after'] ??
    //     uri.queryParameters['__paging_token'];
  }
}

@JsonSerializable(createToJson: false)
class WorkPlacePagingCursor {
  const WorkPlacePagingCursor({
    required this.before,
    required this.after,
    this.next,
  });
  final String before;
  final String after;
  final String? next;
  factory WorkPlacePagingCursor.fromJson(Map<String, dynamic> json) =>
      _$WorkPlacePagingCursorFromJson(json);
}
