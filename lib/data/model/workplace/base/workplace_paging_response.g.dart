// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_paging_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlacePagingCursor _$WorkPlacePagingCursorFromJson(
        Map<String, dynamic> json) =>
    WorkPlacePagingCursor(
      before: json['before'] as String,
      after: json['after'] as String,
      next: json['next'] as String?,
    );

_$WorkPlacePagingResponseImpl _$$WorkPlacePagingResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$WorkPlacePagingResponseImpl(
      cursors: json['cursors'] == null
          ? null
          : WorkPlacePagingCursor.fromJson(
              json['cursors'] as Map<String, dynamic>),
      previous: json['previous'] as String?,
      next: json['next'] as String?,
    );
