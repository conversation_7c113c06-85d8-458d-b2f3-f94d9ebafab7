import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'message_tags.g.dart';

@JsonSerializable()
class WorkPlaceMessageTags {
  WorkPlaceMessageTags({
    required this.id,
    required this.name,
    required this.length,
    required this.offset,
    this.type = WorkPlaceMessageTagsType.nothing,
  });

  final String id;
  final String name;
  final int length;
  final int offset;
  final WorkPlaceMessageTagsType type;

  factory WorkPlaceMessageTags.fromJson(Map<String, dynamic> json) =>
      _$WorkPlaceMessageTagsFromJson(json);
  Map<String, dynamic> toJson() => _$WorkPlaceMessageTagsToJson(this);
}
