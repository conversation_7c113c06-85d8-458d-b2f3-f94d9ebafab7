// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_tags.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlaceMessageTags _$WorkPlaceMessageTagsFromJson(
        Map<String, dynamic> json) =>
    WorkPlaceMessageTags(
      id: json['id'] as String,
      name: json['name'] as String,
      length: (json['length'] as num).toInt(),
      offset: (json['offset'] as num).toInt(),
      type: $enumDecodeNullable(
              _$WorkPlaceMessageTagsTypeEnumMap, json['type']) ??
          WorkPlaceMessageTagsType.nothing,
    );

Map<String, dynamic> _$WorkPlaceMessageTagsToJson(
        WorkPlaceMessageTags instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'length': instance.length,
      'offset': instance.offset,
      'type': _$WorkPlaceMessageTagsTypeEnumMap[instance.type]!,
    };

const _$WorkPlaceMessageTagsTypeEnumMap = {
  WorkPlaceMessageTagsType.user: 'user',
  WorkPlaceMessageTagsType.page: 'page',
  WorkPlaceMessageTagsType.group: 'group',
  WorkPlaceMessageTagsType.nothing: '',
};
