import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'workplace_reactions.g.dart';

@JsonSerializable()
class WorkPlaceReaction {
  WorkPlaceReaction({
    required this.userId,
    required this.username,
    this.type = WorkPlaceReactionType.none,
  });

  @Json<PERSON>ey(name: 'id')
  final String userId;

  @Json<PERSON>ey(name: 'name')
  final String username;

  final WorkPlaceReactionType type;

  factory WorkPlaceReaction.fromJson(Map<String, dynamic> json) =>
      _$WorkPlaceReactionFromJson(json);
  Map<String, dynamic> toJson() => _$WorkPlaceReactionToJson(this);
}
