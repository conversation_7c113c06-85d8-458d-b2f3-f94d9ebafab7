import 'package:json_annotation/json_annotation.dart';

part 'workplace_conversation_attachment_response.g.dart';

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class WorkPlaceConversationAttachmentsResponse {
  WorkPlaceConversationAttachmentsResponse({
    required this.id,
    this.mimeType,
    this.name,
    this.size,
    this.imageData,
    this.fileUrl,
    this.videoData,
  });

  final String id;

  final String? mimeType;
  final String? name;
  final int? size;

  final AttachmentImageData? imageData;
  final String? fileUrl;
  final AttachmentVideoData? videoData;

  factory WorkPlaceConversationAttachmentsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$WorkPlaceConversationAttachmentsResponseFromJson(json);
}

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class AttachmentImageData {
  AttachmentImageData({
    this.height,
    this.width,
    this.url,
    this.imageType,
  });
  final int? height;
  final int? width;
  final String? url;
  final int? imageType;

  factory AttachmentImageData.fromJson(Map<String, dynamic> json) =>
      _$AttachmentImageDataFromJson(json);
}

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class AttachmentVideoData {
  AttachmentVideoData({
    this.height,
    this.width,
    this.url,
    this.length,
    this.videoType,
  });

  final int? height;
  final int? width;
  final String? url;
  final int? length;
  final int? videoType;

  factory AttachmentVideoData.fromJson(Map<String, dynamic> json) =>
      _$AttachmentVideoDataFromJson(json);
}
