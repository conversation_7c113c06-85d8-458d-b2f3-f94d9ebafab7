// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_conversation_attachment_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlaceConversationAttachmentsResponse
    _$WorkPlaceConversationAttachmentsResponseFromJson(
            Map<String, dynamic> json) =>
        WorkPlaceConversationAttachmentsResponse(
          id: json['id'] as String,
          mimeType: json['mime_type'] as String?,
          name: json['name'] as String?,
          size: (json['size'] as num?)?.toInt(),
          imageData: json['image_data'] == null
              ? null
              : AttachmentImageData.fromJson(
                  json['image_data'] as Map<String, dynamic>),
          fileUrl: json['file_url'] as String?,
          videoData: json['video_data'] == null
              ? null
              : AttachmentVideoData.fromJson(
                  json['video_data'] as Map<String, dynamic>),
        );

AttachmentImageData _$AttachmentImageDataFromJson(Map<String, dynamic> json) =>
    AttachmentImageData(
      height: (json['height'] as num?)?.toInt(),
      width: (json['width'] as num?)?.toInt(),
      url: json['url'] as String?,
      imageType: (json['image_type'] as num?)?.toInt(),
    );

AttachmentVideoData _$AttachmentVideoDataFromJson(Map<String, dynamic> json) =>
    AttachmentVideoData(
      height: (json['height'] as num?)?.toInt(),
      width: (json['width'] as num?)?.toInt(),
      url: json['url'] as String?,
      length: (json['length'] as num?)?.toInt(),
      videoType: (json['video_type'] as num?)?.toInt(),
    );
