import 'package:json_annotation/json_annotation.dart';

enum AttachmentType {
  album,
  @JsonValue('file_upload')
  fileUpload,
  photo,
  video,
  @JsonValue('work_content_attachment')
  workContentAttachment,
  @JsonValue('native_templates')
  nativeTemplates,
  question,
  option,
  sticker,
  @JsonValue('animated_image_share')
  animateImageShare,
  @JsonValue('animated_image_video')
  animateImageVideo,

  @JsonValue('video_inline')
  videoOnline,
  @JsonValue('knowledge_note')
  knowledgeNote,
  share,
  @JsonValue('cover_photo')
  coverPhoto,
  event,
  unknow,
}

enum WorkPlaceFormatting {
  @JsonValue('MARKDOWN')
  markdown,

  @JsonValue('PLAINTEXT')
  plainText,
}

enum WorkPlaceMessageTagsType {
  @JsonValue('user')
  user,

  @JsonValue('page')
  page,

  @JsonValue('group')
  group,

  @JsonValue('')
  nothing,
}

enum WorkPlaceReactionType {
  @JsonValue('LIKE')
  like,

  @JsonValue('LOVE')
  love,

  @JsonValue('ANGRY')
  angry,

  @JsonValue('WOW')
  wow,

  @JsonValue('SAD')
  sad,

  @JsonValue('HAHA')
  haha,

  @JsonValue('CARE')
  care,

  @JsonValue('')
  none,
}
enum WorkPlaceGroupPrivacy {
  @JsonValue('OPEN')
  open,

  @JsonValue('CLOSED')
  closed,

  @JsonValue('SECRET')
  secret,
}

enum WorkPlaceThreadType {
  direct,
  group,
}
