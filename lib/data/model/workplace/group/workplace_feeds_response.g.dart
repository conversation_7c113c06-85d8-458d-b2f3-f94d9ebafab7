// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_feeds_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlaceFeedsResponse _$WorkPlaceFeedsResponseFromJson(
        Map<String, dynamic> json) =>
    WorkPlaceFeedsResponse(
      id: json['id'] as String,
      message: json['message'] as String?,
      updatedTime: _$JsonConverterFromJson<String, DateTime?>(
          json['updated_time'], const DateTimeConverter().fromJson),
      from: json['from'] == null
          ? null
          : WorkPlaceUser.fromJson(json['from'] as Map<String, dynamic>),
      attachments: json['attachments'] == null
          ? null
          : WorkPlaceListReponse<WorkPlacePostAttachmentsResponse>.fromJson(
              json['attachments'] as Map<String, dynamic>),
      to: json['to'] == null
          ? null
          : WorkPlaceListReponse<WorkPlaceUser>.fromJson(
              json['to'] as Map<String, dynamic>),
      formatting:
          $enumDecodeNullable(_$WorkPlaceFormattingEnumMap, json['formatting']),
      createdTime: _$JsonConverterFromJson<String, DateTime?>(
          json['created_time'], const DateTimeConverter().fromJson),
      comments: json['comments'] == null
          ? null
          : WorkPlaceListReponse<WorkPlaceCommentsResponse>.fromJson(
              json['comments'] as Map<String, dynamic>),
    );

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

const _$WorkPlaceFormattingEnumMap = {
  WorkPlaceFormatting.markdown: 'MARKDOWN',
  WorkPlaceFormatting.plainText: 'PLAINTEXT',
};
