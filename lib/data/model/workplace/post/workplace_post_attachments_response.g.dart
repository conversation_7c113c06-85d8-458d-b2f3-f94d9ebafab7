// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_post_attachments_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlacePostAttachmentsResponse _$WorkPlacePostAttachmentsResponseFromJson(
        Map<String, dynamic> json) =>
    WorkPlacePostAttachmentsResponse(
      target: json['target'] == null
          ? null
          : AttachmentTarget.fromJson(json['target'] as Map<String, dynamic>),
      title: json['title'] as String?,
      type: $enumDecodeNullable(_$AttachmentTypeEnumMap, json['type'],
          unknownValue: AttachmentType.unknow),
      url: json['url'] as String?,
      subAttachments: json['subattachments'] == null
          ? null
          : SubAttachments.fromJson(
              json['subattachments'] as Map<String, dynamic>),
      media: json['media'] == null
          ? null
          : AttachmentMedia.fromJson(json['media'] as Map<String, dynamic>),
    );

const _$AttachmentTypeEnumMap = {
  AttachmentType.album: 'album',
  AttachmentType.fileUpload: 'file_upload',
  AttachmentType.photo: 'photo',
  AttachmentType.video: 'video',
  AttachmentType.workContentAttachment: 'work_content_attachment',
  AttachmentType.nativeTemplates: 'native_templates',
  AttachmentType.question: 'question',
  AttachmentType.option: 'option',
  AttachmentType.sticker: 'sticker',
  AttachmentType.animateImageShare: 'animated_image_share',
  AttachmentType.animateImageVideo: 'animated_image_video',
  AttachmentType.videoOnline: 'video_inline',
  AttachmentType.knowledgeNote: 'knowledge_note',
  AttachmentType.share: 'share',
  AttachmentType.coverPhoto: 'cover_photo',
  AttachmentType.event: 'event',
  AttachmentType.unknow: 'unknow',
};

AttachmentTarget _$AttachmentTargetFromJson(Map<String, dynamic> json) =>
    AttachmentTarget(
      id: json['id'] as String?,
      url: json['url'] as String?,
    );

SubAttachments _$SubAttachmentsFromJson(Map<String, dynamic> json) =>
    SubAttachments(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => WorkPlacePostAttachmentsResponse.fromJson(
              e as Map<String, dynamic>))
          .toList(),
      type: $enumDecodeNullable(_$AttachmentTypeEnumMap, json['type']),
    );

AttachmentMedia _$AttachmentMediaFromJson(Map<String, dynamic> json) =>
    AttachmentMedia(
      image: json['image'] == null
          ? null
          : AttachmentMediaImage.fromJson(
              json['image'] as Map<String, dynamic>),
      source: json['source'] as String?,
    );

AttachmentMediaImage _$AttachmentMediaImageFromJson(
        Map<String, dynamic> json) =>
    AttachmentMediaImage(
      height: (json['height'] as num?)?.toInt(),
      width: (json['width'] as num?)?.toInt(),
      src: json['src'] as String?,
    );
