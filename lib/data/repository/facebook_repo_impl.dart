import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: FaceBookRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kFaceBookRepository')
final class FaceBookRepoImpl implements FaceBookRepository {
  const FaceBookRepoImpl(
    @Named('kFaceBookService') this.faceBookService,
  );

  final FaceBookService faceBookService;

  @override
  Future<FaceBookCommunityResponse> getCommunity() {
    return faceBookService.community();
  }
}
