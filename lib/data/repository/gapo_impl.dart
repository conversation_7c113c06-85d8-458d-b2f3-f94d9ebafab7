// ignore_for_file: public_member_api_docs
/*
 * Created Date: 2/01/2024 11:12:45
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 7th March 2024 10:57:15
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/data/model/gpw/auth/request/auth_check_phone_request.dart';
import 'package:gp_fbwp_crawler/data/model/gpw/auth/response/auth_check_phone_response.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: GapoRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kGapoRepository')
final class GapoRepoImpl implements GapoRepository {
  const GapoRepoImpl(
    @Named('kGapoWorkAuthService') this.authService,
    @Named('kGapoWorkUploadService') this.uploadService,
    @Named('kGapoWorkGroupService') this.groupService,
    @Named('kGapoWorkWorkspaceService') this.workspaceService,
    @Named('kGapoWorkUserService') this.userService,
  );

  final AuthService authService;
  final UploadService uploadService;
  final GroupService groupService;
  final WorkSpaceService workspaceService;
  final UserService userService;

  @override
  Future<ApiResponseV2<AuthCheckMailResponse>> checkEmail({
    required AuthCheckEmailRequest checkEmailRequest,
  }) {
    return authService.checkEmail(checkEmailRequest: checkEmailRequest);
  }

  @override
  Future<ApiResponseV2<AuthCheckPhoneResponse>> checkPhone({
    required AuthCheckPhoneRequest checkPhoneRequest,
  }) {
    return authService.checkPhone(checkPhoneRequest: checkPhoneRequest);
  }

  @override
  Future<ApiResponseV2<AuthResponse>> login(AuthParams params) {
    return authService.login(params: params);
  }

  @override
  Future<UploadFileResponseModelWrapper> uploadFiles({
    required GPUploadRepoInput input,
  }) async {
    switch (input.uploadType) {
      case GPApiUploadType.files:
        final response = await uploadService.uploadFiles(
          files: [
            input.file,
          ],
          cancelToken: input.cancelToken,
          receiveProgress: input.receiveProgress,
          sendProgress: input.sendProgress,
        );

        return UploadFileResponseModelWrapper(
          uploadFileResponseModels: [response],
          uploadImageResponseModels: [],
        );
      case GPApiUploadType.video:
        final response = await uploadService.uploadVideos(
          files: [
            input.file,
          ],
          cancelToken: input.cancelToken,
          receiveProgress: input.receiveProgress,
          sendProgress: input.sendProgress,
        );

        return UploadFileResponseModelWrapper(
          uploadFileResponseModels: [response],
          uploadImageResponseModels: [],
        );
      case GPApiUploadType.image:
        final response = await uploadService.uploadImages(
          files: [
            input.file,
          ],
          cancelToken: input.cancelToken,
          receiveProgress: input.receiveProgress,
          sendProgress: input.sendProgress,
        );

        return UploadFileResponseModelWrapper(
          uploadFileResponseModels: [],
          uploadImageResponseModels: [response],
        );
      case GPApiUploadType.audio:
        final response = await uploadService.uploadAudios(
          files: [
            input.file,
          ],
          cancelToken: input.cancelToken,
          receiveProgress: input.receiveProgress,
          sendProgress: input.sendProgress,
        );

        return UploadFileResponseModelWrapper(
          uploadFileResponseModels: [response],
          uploadImageResponseModels: [],
        );
    }
  }

  @override
  Future<ApiResponseV2<AuthResponse>> signUp({required GPSignupParams input}) {
    return authService.signUp(params: input);
  }

  @override
  Future<ApiResponseV2<GroupResponse>> createGroup(
      {required GPGroupParams params}) {
    return groupService.createGroup(groupRequest: params);
  }

  @override
  Future<ApiResponseV2WithoutData> inviteToGroup(
      {required GPGroupInviteParams params}) {
    return groupService.inviteToGroup(
        groupInviteRequest: params, groupId: params.groupId);
  }

  @override
  Future<ApiResponseV2<InviteWorkspaceResponse>> inviteEmailToWorkspace(
      {required GPInviteWsParams params}) {
    return workspaceService.inviteEmail(params: params);
  }

  @override
  Future<ApiResponseV2<InviteWorkspaceResponse>> invitePhoneToWorkspace(
      {required GPInviteWsParams params}) {
    return workspaceService.invitePhone(params: params);
  }

  @override
  Future<ApiResponseV2WithoutData> setPassword(
      {required GPSetPasswordParams params, required String token}) {
    return userService.setPassword(params: params, token: token);
  }

  @override
  Future<ApiResponseV2WithoutData> adjustMemberRole(
      {required GPGroupAdjustMemberRoleParams params,
      required String groupId}) {
    return groupService.adjustMemberRole(
        groupId: groupId, groupAdjustMemberRoleRequest: params);
  }

  @override
  Future<ApiResponseV2WithoutData> leaveGroup(
      {required GPGroupLeaveParams params}) {
    return groupService.leaveGroup(
        groupId: params.groupId, groupLeaveRequest: params);
  }

  @override
  Future<ApiResponseV2WithoutData> updateUserProfile(
      {required GPUserProfileParams params,
      required String userId,
      required String token}) {
    return userService.updateUserProfile(
        userId: userId, groupLeaveRequest: params, token: token);
  }
}
