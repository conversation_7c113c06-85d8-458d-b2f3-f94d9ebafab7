/*
 * Created Date: 5/01/2024 17:22:23
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 4th June 2024 09:36:13
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/config/app_configs.dart';
import 'package:injectable/injectable.dart';

import 'app.component.config.dart';

final GetIt getIt = GetIt.instance;

@InjectableInit(
  externalPackageModules: [
    GpCoreV2PackageModule,
  ],
)
Future configureInjection(AppConfig appConfig) => getIt.init(
      environmentFilter: NoEnvOrContainsAny(
        {
          appConfig.getEnvironmentInjectable().name,
        },
      ),
    );
