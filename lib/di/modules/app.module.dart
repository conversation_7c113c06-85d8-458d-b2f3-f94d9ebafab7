/*
 * Created Date: 5/12/2023 09:47:29
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 12th January 2024 17:31:36
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';
import 'package:talker/talker.dart';

@module
abstract class AppModule {
  @preResolve
  Future<bool> get init async {
    _initIgnoreUsecases();

    return Future.value(true);
  }

  @Singleton(env: kFlavorDevs)
  Talker get talker => Talker(
        settings: TalkerSettings(
          enabled: LogConfig.kEnableTalkerLog,
          maxHistoryItems: 100,
          useHistory: true,
          useConsoleLogs: true,
        ),
        logger: TalkerLogger(),
        observer: GPTalkerObserver(),
      );

  void _initIgnoreUsecases() {
    GetIt.I.get<AppUseCaseManagement>().ignoreEvents.addAll([]);
  }
}
