/*
 * Created Date: Saturday, 1st June 2024, 16:20:04
 * Author: ToanNM
 * -----
 * Last Modified: Thursday, 12th September 2024 22:33:00
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/base/usecase/model/base_output.dart';
import 'package:gp_fbwp_crawler/domain/entity/base/app/locale_enum.dart';
import 'package:isar/isar.dart';

part 'app_config.entity.g.dart';

@Collection()
final class AppConfigEntity extends GPBaseOutput {
  AppConfigEntity({
    required this.appLocale,
    required this.appVersion,
    this.workPlaceToken,
  });

  @enumerated
  GPAppLocale appLocale;

  final String appVersion;

  final String? workPlaceToken;

  @Index(type: IndexType.value)
  late final Id id = appVersion.hashCode;

  AppConfigEntity copyWith({
    GPAppLocale? appLocale,
    String? appVersion,
    String? workPlaceToken,
  }) {
    return AppConfigEntity(
      appLocale: appLocale ?? this.appLocale,
      appVersion: appVersion ?? this.appVersion,
      workPlaceToken: workPlaceToken ?? this.workPlaceToken,
    );
  }
}
