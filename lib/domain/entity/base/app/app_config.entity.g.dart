// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_config.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetAppConfigEntityCollection on Isar {
  IsarCollection<AppConfigEntity> get appConfigEntitys => this.collection();
}

const AppConfigEntitySchema = CollectionSchema(
  name: r'AppConfigEntity',
  id: -6817184198876440529,
  properties: {
    r'appLocale': PropertySchema(
      id: 0,
      name: r'appLocale',
      type: IsarType.byte,
      enumMap: _AppConfigEntityappLocaleEnumValueMap,
    ),
    r'appVersion': PropertySchema(
      id: 1,
      name: r'appVersion',
      type: IsarType.string,
    ),
    r'workPlaceToken': PropertySchema(
      id: 2,
      name: r'workPlaceToken',
      type: IsarType.string,
    )
  },
  estimateSize: _appConfigEntityEstimateSize,
  serialize: _appConfigEntitySerialize,
  deserialize: _appConfigEntityDeserialize,
  deserializeProp: _appConfigEntityDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _appConfigEntityGetId,
  getLinks: _appConfigEntityGetLinks,
  attach: _appConfigEntityAttach,
  version: '3.1.0+1',
);

int _appConfigEntityEstimateSize(
  AppConfigEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.appVersion.length * 3;
  {
    final value = object.workPlaceToken;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _appConfigEntitySerialize(
  AppConfigEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeByte(offsets[0], object.appLocale.index);
  writer.writeString(offsets[1], object.appVersion);
  writer.writeString(offsets[2], object.workPlaceToken);
}

AppConfigEntity _appConfigEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AppConfigEntity(
    appLocale: _AppConfigEntityappLocaleValueEnumMap[
            reader.readByteOrNull(offsets[0])] ??
        GPAppLocale.vi,
    appVersion: reader.readString(offsets[1]),
    workPlaceToken: reader.readStringOrNull(offsets[2]),
  );
  return object;
}

P _appConfigEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (_AppConfigEntityappLocaleValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPAppLocale.vi) as P;
    case 1:
      return (reader.readString(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _AppConfigEntityappLocaleEnumValueMap = {
  'vi': 0,
  'en': 1,
};
const _AppConfigEntityappLocaleValueEnumMap = {
  0: GPAppLocale.vi,
  1: GPAppLocale.en,
};

Id _appConfigEntityGetId(AppConfigEntity object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _appConfigEntityGetLinks(AppConfigEntity object) {
  return [];
}

void _appConfigEntityAttach(
    IsarCollection<dynamic> col, Id id, AppConfigEntity object) {}

extension AppConfigEntityQueryWhereSort
    on QueryBuilder<AppConfigEntity, AppConfigEntity, QWhere> {
  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension AppConfigEntityQueryWhere
    on QueryBuilder<AppConfigEntity, AppConfigEntity, QWhereClause> {
  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AppConfigEntityQueryFilter
    on QueryBuilder<AppConfigEntity, AppConfigEntity, QFilterCondition> {
  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appLocaleEqualTo(GPAppLocale value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'appLocale',
        value: value,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appLocaleGreaterThan(
    GPAppLocale value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'appLocale',
        value: value,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appLocaleLessThan(
    GPAppLocale value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'appLocale',
        value: value,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appLocaleBetween(
    GPAppLocale lower,
    GPAppLocale upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'appLocale',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'appVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'appVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'appVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'appVersion',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'appVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'appVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'appVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'appVersion',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'appVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      appVersionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'appVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'workPlaceToken',
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'workPlaceToken',
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'workPlaceToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'workPlaceToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'workPlaceToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'workPlaceToken',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'workPlaceToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'workPlaceToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'workPlaceToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'workPlaceToken',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'workPlaceToken',
        value: '',
      ));
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterFilterCondition>
      workPlaceTokenIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'workPlaceToken',
        value: '',
      ));
    });
  }
}

extension AppConfigEntityQueryObject
    on QueryBuilder<AppConfigEntity, AppConfigEntity, QFilterCondition> {}

extension AppConfigEntityQueryLinks
    on QueryBuilder<AppConfigEntity, AppConfigEntity, QFilterCondition> {}

extension AppConfigEntityQuerySortBy
    on QueryBuilder<AppConfigEntity, AppConfigEntity, QSortBy> {
  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      sortByAppLocale() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appLocale', Sort.asc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      sortByAppLocaleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appLocale', Sort.desc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      sortByAppVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appVersion', Sort.asc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      sortByAppVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appVersion', Sort.desc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      sortByWorkPlaceToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'workPlaceToken', Sort.asc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      sortByWorkPlaceTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'workPlaceToken', Sort.desc);
    });
  }
}

extension AppConfigEntityQuerySortThenBy
    on QueryBuilder<AppConfigEntity, AppConfigEntity, QSortThenBy> {
  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      thenByAppLocale() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appLocale', Sort.asc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      thenByAppLocaleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appLocale', Sort.desc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      thenByAppVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appVersion', Sort.asc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      thenByAppVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appVersion', Sort.desc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      thenByWorkPlaceToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'workPlaceToken', Sort.asc);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QAfterSortBy>
      thenByWorkPlaceTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'workPlaceToken', Sort.desc);
    });
  }
}

extension AppConfigEntityQueryWhereDistinct
    on QueryBuilder<AppConfigEntity, AppConfigEntity, QDistinct> {
  QueryBuilder<AppConfigEntity, AppConfigEntity, QDistinct>
      distinctByAppLocale() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'appLocale');
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QDistinct>
      distinctByAppVersion({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'appVersion', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AppConfigEntity, AppConfigEntity, QDistinct>
      distinctByWorkPlaceToken({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'workPlaceToken',
          caseSensitive: caseSensitive);
    });
  }
}

extension AppConfigEntityQueryProperty
    on QueryBuilder<AppConfigEntity, AppConfigEntity, QQueryProperty> {
  QueryBuilder<AppConfigEntity, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<AppConfigEntity, GPAppLocale, QQueryOperations>
      appLocaleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'appLocale');
    });
  }

  QueryBuilder<AppConfigEntity, String, QQueryOperations> appVersionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'appVersion');
    });
  }

  QueryBuilder<AppConfigEntity, String?, QQueryOperations>
      workPlaceTokenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'workPlaceToken');
    });
  }
}
