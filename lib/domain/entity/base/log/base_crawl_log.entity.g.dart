// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_crawl_log.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetGPBaseCrawlLogEntityCollection on Isar {
  IsarCollection<GPBaseCrawlLogEntity> get gPBaseCrawlLogEntitys =>
      this.collection();
}

const GPBaseCrawlLogEntitySchema = CollectionSchema(
  name: r'GPBaseCrawlLogEntity',
  id: 6743552956765322605,
  properties: {
    r'crawlDownloadStatus': PropertySchema(
      id: 0,
      name: r'crawlDownloadStatus',
      type: IsarType.object,
      target: r'BaseCrawlDownloadStatus',
    ),
    r'crawlSyncStatus': PropertySchema(
      id: 1,
      name: r'crawlSyncStatus',
      type: IsarType.object,
      target: r'BaseCrawlSyncStatus',
    ),
    r'crawlType': PropertySchema(
      id: 2,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _GPBaseCrawlLogEntitycrawlTypeEnumValueMap,
    ),
    r'lastUpdatedAt': PropertySchema(
      id: 3,
      name: r'lastUpdatedAt',
      type: IsarType.dateTime,
    ),
    r'objectId': PropertySchema(
      id: 4,
      name: r'objectId',
      type: IsarType.string,
    )
  },
  estimateSize: _gPBaseCrawlLogEntityEstimateSize,
  serialize: _gPBaseCrawlLogEntitySerialize,
  deserialize: _gPBaseCrawlLogEntityDeserialize,
  deserializeProp: _gPBaseCrawlLogEntityDeserializeProp,
  idName: r'id',
  indexes: {
    r'objectId': IndexSchema(
      id: -8330937420180998932,
      name: r'objectId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'objectId',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    ),
    r'crawlType': IndexSchema(
      id: 6790982346722937646,
      name: r'crawlType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'crawlType',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {
    r'BaseCrawlDownloadStatus': BaseCrawlDownloadStatusSchema,
    r'BaseCrawlSyncStatus': BaseCrawlSyncStatusSchema
  },
  getId: _gPBaseCrawlLogEntityGetId,
  getLinks: _gPBaseCrawlLogEntityGetLinks,
  attach: _gPBaseCrawlLogEntityAttach,
  version: '3.1.0+1',
);

int _gPBaseCrawlLogEntityEstimateSize(
  GPBaseCrawlLogEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.crawlDownloadStatus;
    if (value != null) {
      bytesCount += 3 +
          BaseCrawlDownloadStatusSchema.estimateSize(
              value, allOffsets[BaseCrawlDownloadStatus]!, allOffsets);
    }
  }
  {
    final value = object.crawlSyncStatus;
    if (value != null) {
      bytesCount += 3 +
          BaseCrawlSyncStatusSchema.estimateSize(
              value, allOffsets[BaseCrawlSyncStatus]!, allOffsets);
    }
  }
  bytesCount += 3 + object.objectId.length * 3;
  return bytesCount;
}

void _gPBaseCrawlLogEntitySerialize(
  GPBaseCrawlLogEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeObject<BaseCrawlDownloadStatus>(
    offsets[0],
    allOffsets,
    BaseCrawlDownloadStatusSchema.serialize,
    object.crawlDownloadStatus,
  );
  writer.writeObject<BaseCrawlSyncStatus>(
    offsets[1],
    allOffsets,
    BaseCrawlSyncStatusSchema.serialize,
    object.crawlSyncStatus,
  );
  writer.writeByte(offsets[2], object.crawlType.index);
  writer.writeDateTime(offsets[3], object.lastUpdatedAt);
  writer.writeString(offsets[4], object.objectId);
}

GPBaseCrawlLogEntity _gPBaseCrawlLogEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = GPBaseCrawlLogEntity(
    crawlDownloadStatus: reader.readObjectOrNull<BaseCrawlDownloadStatus>(
      offsets[0],
      BaseCrawlDownloadStatusSchema.deserialize,
      allOffsets,
    ),
    crawlSyncStatus: reader.readObjectOrNull<BaseCrawlSyncStatus>(
      offsets[1],
      BaseCrawlSyncStatusSchema.deserialize,
      allOffsets,
    ),
    crawlType: _GPBaseCrawlLogEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[2])] ??
        GPBaseCrawlType.user,
    lastUpdatedAt: reader.readDateTime(offsets[3]),
    objectId: reader.readString(offsets[4]),
  );
  return object;
}

P _gPBaseCrawlLogEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readObjectOrNull<BaseCrawlDownloadStatus>(
        offset,
        BaseCrawlDownloadStatusSchema.deserialize,
        allOffsets,
      )) as P;
    case 1:
      return (reader.readObjectOrNull<BaseCrawlSyncStatus>(
        offset,
        BaseCrawlSyncStatusSchema.deserialize,
        allOffsets,
      )) as P;
    case 2:
      return (_GPBaseCrawlLogEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.user) as P;
    case 3:
      return (reader.readDateTime(offset)) as P;
    case 4:
      return (reader.readString(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _GPBaseCrawlLogEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _GPBaseCrawlLogEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};

Id _gPBaseCrawlLogEntityGetId(GPBaseCrawlLogEntity object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _gPBaseCrawlLogEntityGetLinks(
    GPBaseCrawlLogEntity object) {
  return [];
}

void _gPBaseCrawlLogEntityAttach(
    IsarCollection<dynamic> col, Id id, GPBaseCrawlLogEntity object) {}

extension GPBaseCrawlLogEntityQueryWhereSort
    on QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QWhere> {
  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhere>
      anyObjectId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'objectId'),
      );
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhere>
      anyCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'crawlType'),
      );
    });
  }
}

extension GPBaseCrawlLogEntityQueryWhere
    on QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QWhereClause> {
  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      objectIdEqualTo(String objectId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'objectId',
        value: [objectId],
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      objectIdNotEqualTo(String objectId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'objectId',
              lower: [],
              upper: [objectId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'objectId',
              lower: [objectId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'objectId',
              lower: [objectId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'objectId',
              lower: [],
              upper: [objectId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      objectIdGreaterThan(
    String objectId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'objectId',
        lower: [objectId],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      objectIdLessThan(
    String objectId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'objectId',
        lower: [],
        upper: [objectId],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      objectIdBetween(
    String lowerObjectId,
    String upperObjectId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'objectId',
        lower: [lowerObjectId],
        includeLower: includeLower,
        upper: [upperObjectId],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      objectIdStartsWith(String ObjectIdPrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'objectId',
        lower: [ObjectIdPrefix],
        upper: ['$ObjectIdPrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      objectIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'objectId',
        value: [''],
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      objectIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'objectId',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'objectId',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'objectId',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'objectId',
              upper: [''],
            ));
      }
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      crawlTypeEqualTo(GPBaseCrawlType crawlType) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'crawlType',
        value: [crawlType],
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      crawlTypeNotEqualTo(GPBaseCrawlType crawlType) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'crawlType',
              lower: [],
              upper: [crawlType],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'crawlType',
              lower: [crawlType],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'crawlType',
              lower: [crawlType],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'crawlType',
              lower: [],
              upper: [crawlType],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      crawlTypeGreaterThan(
    GPBaseCrawlType crawlType, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'crawlType',
        lower: [crawlType],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      crawlTypeLessThan(
    GPBaseCrawlType crawlType, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'crawlType',
        lower: [],
        upper: [crawlType],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterWhereClause>
      crawlTypeBetween(
    GPBaseCrawlType lowerCrawlType,
    GPBaseCrawlType upperCrawlType, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'crawlType',
        lower: [lowerCrawlType],
        includeLower: includeLower,
        upper: [upperCrawlType],
        includeUpper: includeUpper,
      ));
    });
  }
}

extension GPBaseCrawlLogEntityQueryFilter on QueryBuilder<GPBaseCrawlLogEntity,
    GPBaseCrawlLogEntity, QFilterCondition> {
  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> crawlDownloadStatusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'crawlDownloadStatus',
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> crawlDownloadStatusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'crawlDownloadStatus',
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> crawlSyncStatusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'crawlSyncStatus',
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> crawlSyncStatusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'crawlSyncStatus',
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> lastUpdatedAtEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastUpdatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> lastUpdatedAtGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastUpdatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> lastUpdatedAtLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastUpdatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> lastUpdatedAtBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastUpdatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> objectIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'objectId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> objectIdGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'objectId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> objectIdLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'objectId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> objectIdBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'objectId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> objectIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'objectId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> objectIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'objectId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
          QAfterFilterCondition>
      objectIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'objectId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
          QAfterFilterCondition>
      objectIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'objectId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> objectIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'objectId',
        value: '',
      ));
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
      QAfterFilterCondition> objectIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'objectId',
        value: '',
      ));
    });
  }
}

extension GPBaseCrawlLogEntityQueryObject on QueryBuilder<GPBaseCrawlLogEntity,
    GPBaseCrawlLogEntity, QFilterCondition> {
  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
          QAfterFilterCondition>
      crawlDownloadStatus(FilterQuery<BaseCrawlDownloadStatus> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'crawlDownloadStatus');
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity,
          QAfterFilterCondition>
      crawlSyncStatus(FilterQuery<BaseCrawlSyncStatus> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'crawlSyncStatus');
    });
  }
}

extension GPBaseCrawlLogEntityQueryLinks on QueryBuilder<GPBaseCrawlLogEntity,
    GPBaseCrawlLogEntity, QFilterCondition> {}

extension GPBaseCrawlLogEntityQuerySortBy
    on QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QSortBy> {
  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      sortByLastUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastUpdatedAt', Sort.asc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      sortByLastUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastUpdatedAt', Sort.desc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      sortByObjectId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'objectId', Sort.asc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      sortByObjectIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'objectId', Sort.desc);
    });
  }
}

extension GPBaseCrawlLogEntityQuerySortThenBy
    on QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QSortThenBy> {
  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      thenByLastUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastUpdatedAt', Sort.asc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      thenByLastUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastUpdatedAt', Sort.desc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      thenByObjectId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'objectId', Sort.asc);
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QAfterSortBy>
      thenByObjectIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'objectId', Sort.desc);
    });
  }
}

extension GPBaseCrawlLogEntityQueryWhereDistinct
    on QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QDistinct> {
  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QDistinct>
      distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QDistinct>
      distinctByLastUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastUpdatedAt');
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QDistinct>
      distinctByObjectId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'objectId', caseSensitive: caseSensitive);
    });
  }
}

extension GPBaseCrawlLogEntityQueryProperty on QueryBuilder<
    GPBaseCrawlLogEntity, GPBaseCrawlLogEntity, QQueryProperty> {
  QueryBuilder<GPBaseCrawlLogEntity, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, BaseCrawlDownloadStatus?, QQueryOperations>
      crawlDownloadStatusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlDownloadStatus');
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, BaseCrawlSyncStatus?, QQueryOperations>
      crawlSyncStatusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlSyncStatus');
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, GPBaseCrawlType, QQueryOperations>
      crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, DateTime, QQueryOperations>
      lastUpdatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastUpdatedAt');
    });
  }

  QueryBuilder<GPBaseCrawlLogEntity, String, QQueryOperations>
      objectIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'objectId');
    });
  }
}
