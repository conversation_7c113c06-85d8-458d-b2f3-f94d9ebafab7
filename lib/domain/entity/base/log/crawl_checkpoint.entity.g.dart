// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crawl_checkpoint.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetCrawlCheckpointCollection on Isar {
  IsarCollection<CrawlCheckpoint> get crawlCheckpoints => this.collection();
}

const CrawlCheckpointSchema = CollectionSchema(
  name: r'CrawlCheckpoint',
  id: 768923694335337048,
  properties: {
    r'checkpointId': PropertySchema(
      id: 0,
      name: r'checkpointId',
      type: IsarType.string,
    ),
    r'crawlType': PropertySchema(
      id: 1,
      name: r'crawlType',
      type: IsarType.string,
      enumMap: _CrawlCheckpointcrawlTypeEnumValueMap,
    ),
    r'createdAt': PropertySchema(
      id: 2,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'id': PropertySchema(
      id: 3,
      name: r'id',
      type: IsarType.string,
    ),
    r'isDone': PropertySchema(
      id: 4,
      name: r'isDone',
      type: IsarType.bool,
    ),
    r'itemLength': PropertySchema(
      id: 5,
      name: r'itemLength',
      type: IsarType.long,
    ),
    r'lastDoneAt': PropertySchema(
      id: 6,
      name: r'lastDoneAt',
      type: IsarType.dateTime,
    ),
    r'nextQueries': PropertySchema(
      id: 7,
      name: r'nextQueries',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 8,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _crawlCheckpointEstimateSize,
  serialize: _crawlCheckpointSerialize,
  deserialize: _crawlCheckpointDeserialize,
  deserializeProp: _crawlCheckpointDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _crawlCheckpointGetId,
  getLinks: _crawlCheckpointGetLinks,
  attach: _crawlCheckpointAttach,
  version: '3.1.0+1',
);

int _crawlCheckpointEstimateSize(
  CrawlCheckpoint object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.checkpointId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.crawlType.name.length * 3;
  {
    final value = object.id;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.nextQueries;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _crawlCheckpointSerialize(
  CrawlCheckpoint object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.checkpointId);
  writer.writeString(offsets[1], object.crawlType.name);
  writer.writeDateTime(offsets[2], object.createdAt);
  writer.writeString(offsets[3], object.id);
  writer.writeBool(offsets[4], object.isDone);
  writer.writeLong(offsets[5], object.itemLength);
  writer.writeDateTime(offsets[6], object.lastDoneAt);
  writer.writeString(offsets[7], object.nextQueries);
  writer.writeDateTime(offsets[8], object.updatedAt);
}

CrawlCheckpoint _crawlCheckpointDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = CrawlCheckpoint(
    checkpointId: reader.readStringOrNull(offsets[0]),
    crawlType: _CrawlCheckpointcrawlTypeValueEnumMap[
            reader.readStringOrNull(offsets[1])] ??
        GPBaseCrawlType.user,
    createdAt: reader.readDateTimeOrNull(offsets[2]),
    id: reader.readStringOrNull(offsets[3]),
    isDone: reader.readBoolOrNull(offsets[4]) ?? false,
    itemLength: reader.readLongOrNull(offsets[5]),
    lastDoneAt: reader.readDateTimeOrNull(offsets[6]),
    nextQueries: reader.readStringOrNull(offsets[7]),
    updatedAt: reader.readDateTimeOrNull(offsets[8]),
  );
  return object;
}

P _crawlCheckpointDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (_CrawlCheckpointcrawlTypeValueEnumMap[
              reader.readStringOrNull(offset)] ??
          GPBaseCrawlType.user) as P;
    case 2:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 5:
      return (reader.readLongOrNull(offset)) as P;
    case 6:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _CrawlCheckpointcrawlTypeEnumValueMap = {
  r'user': r'user',
  r'group': r'group',
  r'thread': r'thread',
  r'message': r'message',
  r'feed': r'feed',
  r'userFeed': r'userFeed',
  r'comment': r'comment',
  r'attachment': r'attachment',
  r'community': r'community',
  r'sticker': r'sticker',
  r'reaction': r'reaction',
  r'commentReaction': r'commentReaction',
  r'groupMember': r'groupMember',
  r'postSeen': r'postSeen',
  r'messageAttachment': r'messageAttachment',
};
const _CrawlCheckpointcrawlTypeValueEnumMap = {
  r'user': GPBaseCrawlType.user,
  r'group': GPBaseCrawlType.group,
  r'thread': GPBaseCrawlType.thread,
  r'message': GPBaseCrawlType.message,
  r'feed': GPBaseCrawlType.feed,
  r'userFeed': GPBaseCrawlType.userFeed,
  r'comment': GPBaseCrawlType.comment,
  r'attachment': GPBaseCrawlType.attachment,
  r'community': GPBaseCrawlType.community,
  r'sticker': GPBaseCrawlType.sticker,
  r'reaction': GPBaseCrawlType.reaction,
  r'commentReaction': GPBaseCrawlType.commentReaction,
  r'groupMember': GPBaseCrawlType.groupMember,
  r'postSeen': GPBaseCrawlType.postSeen,
  r'messageAttachment': GPBaseCrawlType.messageAttachment,
};

Id _crawlCheckpointGetId(CrawlCheckpoint object) {
  return object.dbId;
}

List<IsarLinkBase<dynamic>> _crawlCheckpointGetLinks(CrawlCheckpoint object) {
  return [];
}

void _crawlCheckpointAttach(
    IsarCollection<dynamic> col, Id id, CrawlCheckpoint object) {}

extension CrawlCheckpointQueryWhereSort
    on QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QWhere> {
  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterWhere> anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension CrawlCheckpointQueryWhere
    on QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QWhereClause> {
  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterWhereClause> dbIdEqualTo(
      Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterWhereClause>
      dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterWhereClause>
      dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterWhereClause>
      dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterWhereClause> dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension CrawlCheckpointQueryFilter
    on QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QFilterCondition> {
  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'checkpointId',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'checkpointId',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'checkpointId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'checkpointId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'checkpointId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'checkpointId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'checkpointId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'checkpointId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'checkpointId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'checkpointId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'checkpointId',
        value: '',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      checkpointIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'checkpointId',
        value: '',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeEqualTo(
    GPBaseCrawlType value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'crawlType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'crawlType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'crawlType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'crawlType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: '',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      crawlTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'crawlType',
        value: '',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      dbIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      dbIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      dbIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      dbIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      isDoneEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isDone',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      itemLengthIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'itemLength',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      itemLengthIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'itemLength',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      itemLengthEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'itemLength',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      itemLengthGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'itemLength',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      itemLengthLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'itemLength',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      itemLengthBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'itemLength',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      lastDoneAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastDoneAt',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      lastDoneAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastDoneAt',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      lastDoneAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastDoneAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      lastDoneAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastDoneAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      lastDoneAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastDoneAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      lastDoneAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastDoneAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'nextQueries',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'nextQueries',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'nextQueries',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'nextQueries',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'nextQueries',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'nextQueries',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'nextQueries',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'nextQueries',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'nextQueries',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'nextQueries',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'nextQueries',
        value: '',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      nextQueriesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'nextQueries',
        value: '',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension CrawlCheckpointQueryObject
    on QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QFilterCondition> {}

extension CrawlCheckpointQueryLinks
    on QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QFilterCondition> {}

extension CrawlCheckpointQuerySortBy
    on QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QSortBy> {
  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByCheckpointId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'checkpointId', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByCheckpointIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'checkpointId', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy> sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy> sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy> sortByIsDone() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDone', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByIsDoneDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDone', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByItemLength() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemLength', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByItemLengthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemLength', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByLastDoneAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastDoneAt', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByLastDoneAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastDoneAt', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByNextQueries() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextQueries', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByNextQueriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextQueries', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension CrawlCheckpointQuerySortThenBy
    on QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QSortThenBy> {
  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByCheckpointId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'checkpointId', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByCheckpointIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'checkpointId', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy> thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy> thenByIsDone() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDone', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByIsDoneDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDone', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByItemLength() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemLength', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByItemLengthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemLength', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByLastDoneAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastDoneAt', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByLastDoneAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastDoneAt', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByNextQueries() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextQueries', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByNextQueriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextQueries', Sort.desc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension CrawlCheckpointQueryWhereDistinct
    on QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct> {
  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct>
      distinctByCheckpointId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'checkpointId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct> distinctByCrawlType(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct> distinctById(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct> distinctByIsDone() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isDone');
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct>
      distinctByItemLength() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'itemLength');
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct>
      distinctByLastDoneAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastDoneAt');
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct>
      distinctByNextQueries({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'nextQueries', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension CrawlCheckpointQueryProperty
    on QueryBuilder<CrawlCheckpoint, CrawlCheckpoint, QQueryProperty> {
  QueryBuilder<CrawlCheckpoint, int, QQueryOperations> dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<CrawlCheckpoint, String?, QQueryOperations>
      checkpointIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'checkpointId');
    });
  }

  QueryBuilder<CrawlCheckpoint, GPBaseCrawlType, QQueryOperations>
      crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<CrawlCheckpoint, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<CrawlCheckpoint, String?, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<CrawlCheckpoint, bool, QQueryOperations> isDoneProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isDone');
    });
  }

  QueryBuilder<CrawlCheckpoint, int?, QQueryOperations> itemLengthProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'itemLength');
    });
  }

  QueryBuilder<CrawlCheckpoint, DateTime?, QQueryOperations>
      lastDoneAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastDoneAt');
    });
  }

  QueryBuilder<CrawlCheckpoint, String?, QQueryOperations>
      nextQueriesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'nextQueries');
    });
  }

  QueryBuilder<CrawlCheckpoint, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
