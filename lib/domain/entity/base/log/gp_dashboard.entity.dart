/*
 * Created Date: Saturday, 1st June 2024, 12:43:54
 * Author: ToanNM
 * -----
 * Last Modified: Friday, 13th September 2024 21:05:46
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:isar/isar.dart';

part 'gp_dashboard.entity.g.dart';

@Collection()
final class GPDashboardEntity {
  GPDashboardEntity({
    this.totalDownloadSize = 0,
    this.totalDownloadFile = 0,
    //
    this.totalUploadSize = 0,
    this.totalUploadFile = 0,
  });

  final Id id = 1;

  int totalDownloadSize;
  int totalDownloadFile;

  int totalUploadSize;
  int totalUploadFile;
}
