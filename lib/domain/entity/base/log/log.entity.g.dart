// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetGPLogEntityCollection on Isar {
  IsarCollection<GPLogEntity> get gPLogEntitys => this.collection();
}

const GPLogEntitySchema = CollectionSchema(
  name: r'GPLogEntity',
  id: -2791618162607693273,
  properties: {
    r'cUrl': PropertySchema(
      id: 0,
      name: r'cUrl',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 1,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'data': PropertySchema(
      id: 2,
      name: r'data',
      type: IsarType.string,
    ),
    r'error': PropertySchema(
      id: 3,
      name: r'error',
      type: IsarType.string,
    ),
    r'logType': PropertySchema(
      id: 4,
      name: r'logType',
      type: IsarType.byte,
      enumMap: _GPLogEntitylogTypeEnumValueMap,
    ),
    r'path': PropertySchema(
      id: 5,
      name: r'path',
      type: IsarType.string,
    )
  },
  estimateSize: _gPLogEntityEstimateSize,
  serialize: _gPLogEntitySerialize,
  deserialize: _gPLogEntityDeserialize,
  deserializeProp: _gPLogEntityDeserializeProp,
  idName: r'id',
  indexes: {
    r'cUrl': IndexSchema(
      id: 6110733563349305723,
      name: r'cUrl',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cUrl',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    ),
    r'logType': IndexSchema(
      id: -1575070705662519133,
      name: r'logType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'logType',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _gPLogEntityGetId,
  getLinks: _gPLogEntityGetLinks,
  attach: _gPLogEntityAttach,
  version: '3.1.0+1',
);

int _gPLogEntityEstimateSize(
  GPLogEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.cUrl.length * 3;
  bytesCount += 3 + object.data.length * 3;
  bytesCount += 3 + object.error.length * 3;
  bytesCount += 3 + object.path.length * 3;
  return bytesCount;
}

void _gPLogEntitySerialize(
  GPLogEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.cUrl);
  writer.writeDateTime(offsets[1], object.createdAt);
  writer.writeString(offsets[2], object.data);
  writer.writeString(offsets[3], object.error);
  writer.writeByte(offsets[4], object.logType.index);
  writer.writeString(offsets[5], object.path);
}

GPLogEntity _gPLogEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = GPLogEntity(
    cUrl: reader.readString(offsets[0]),
    createdAt: reader.readDateTime(offsets[1]),
    data: reader.readString(offsets[2]),
    error: reader.readString(offsets[3]),
    logType:
        _GPLogEntitylogTypeValueEnumMap[reader.readByteOrNull(offsets[4])] ??
            GPLogType.gpMembership,
    path: reader.readString(offsets[5]),
  );
  return object;
}

P _gPLogEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readString(offset)) as P;
    case 1:
      return (reader.readDateTime(offset)) as P;
    case 2:
      return (reader.readString(offset)) as P;
    case 3:
      return (reader.readString(offset)) as P;
    case 4:
      return (_GPLogEntitylogTypeValueEnumMap[reader.readByteOrNull(offset)] ??
          GPLogType.gpMembership) as P;
    case 5:
      return (reader.readString(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _GPLogEntitylogTypeEnumValueMap = {
  'gpMembership': 0,
  'gpGroup': 1,
  'gpWorkSpace': 2,
  'gpAuth': 3,
  'gpUpload': 4,
  'wpCommunity': 5,
  'wpLike': 6,
  'wpReactions': 7,
  'wpSeen': 8,
  'wpMessages': 9,
  'wpMembers': 10,
  'wpGroups': 11,
  'wpFeed': 12,
  'wpComments': 13,
  'wpAttachments': 14,
  'wpConversations': 15,
  'other': 16,
  'upload': 17,
};
const _GPLogEntitylogTypeValueEnumMap = {
  0: GPLogType.gpMembership,
  1: GPLogType.gpGroup,
  2: GPLogType.gpWorkSpace,
  3: GPLogType.gpAuth,
  4: GPLogType.gpUpload,
  5: GPLogType.wpCommunity,
  6: GPLogType.wpLike,
  7: GPLogType.wpReactions,
  8: GPLogType.wpSeen,
  9: GPLogType.wpMessages,
  10: GPLogType.wpMembers,
  11: GPLogType.wpGroups,
  12: GPLogType.wpFeed,
  13: GPLogType.wpComments,
  14: GPLogType.wpAttachments,
  15: GPLogType.wpConversations,
  16: GPLogType.other,
  17: GPLogType.upload,
};

Id _gPLogEntityGetId(GPLogEntity object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _gPLogEntityGetLinks(GPLogEntity object) {
  return [];
}

void _gPLogEntityAttach(
    IsarCollection<dynamic> col, Id id, GPLogEntity object) {}

extension GPLogEntityQueryWhereSort
    on QueryBuilder<GPLogEntity, GPLogEntity, QWhere> {
  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhere> anyCUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'cUrl'),
      );
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhere> anyLogType() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'logType'),
      );
    });
  }
}

extension GPLogEntityQueryWhere
    on QueryBuilder<GPLogEntity, GPLogEntity, QWhereClause> {
  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> idNotEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> idGreaterThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> idLessThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> cUrlEqualTo(
      String cUrl) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cUrl',
        value: [cUrl],
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> cUrlNotEqualTo(
      String cUrl) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cUrl',
              lower: [],
              upper: [cUrl],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cUrl',
              lower: [cUrl],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cUrl',
              lower: [cUrl],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cUrl',
              lower: [],
              upper: [cUrl],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> cUrlGreaterThan(
    String cUrl, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cUrl',
        lower: [cUrl],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> cUrlLessThan(
    String cUrl, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cUrl',
        lower: [],
        upper: [cUrl],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> cUrlBetween(
    String lowerCUrl,
    String upperCUrl, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cUrl',
        lower: [lowerCUrl],
        includeLower: includeLower,
        upper: [upperCUrl],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> cUrlStartsWith(
      String CUrlPrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cUrl',
        lower: [CUrlPrefix],
        upper: ['$CUrlPrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> cUrlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cUrl',
        value: [''],
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> cUrlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'cUrl',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'cUrl',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'cUrl',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'cUrl',
              upper: [''],
            ));
      }
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> logTypeEqualTo(
      GPLogType logType) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'logType',
        value: [logType],
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> logTypeNotEqualTo(
      GPLogType logType) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'logType',
              lower: [],
              upper: [logType],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'logType',
              lower: [logType],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'logType',
              lower: [logType],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'logType',
              lower: [],
              upper: [logType],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> logTypeGreaterThan(
    GPLogType logType, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'logType',
        lower: [logType],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> logTypeLessThan(
    GPLogType logType, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'logType',
        lower: [],
        upper: [logType],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterWhereClause> logTypeBetween(
    GPLogType lowerLogType,
    GPLogType upperLogType, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'logType',
        lower: [lowerLogType],
        includeLower: includeLower,
        upper: [upperLogType],
        includeUpper: includeUpper,
      ));
    });
  }
}

extension GPLogEntityQueryFilter
    on QueryBuilder<GPLogEntity, GPLogEntity, QFilterCondition> {
  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> cUrlEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> cUrlGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> cUrlLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> cUrlBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cUrl',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> cUrlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> cUrlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> cUrlContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> cUrlMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cUrl',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> cUrlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      cUrlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      createdAtEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      createdAtLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      createdAtBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> dataEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'data',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> dataGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'data',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> dataLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'data',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> dataBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'data',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> dataStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'data',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> dataEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'data',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> dataContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'data',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> dataMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'data',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> dataIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'data',
        value: '',
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      dataIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'data',
        value: '',
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> errorEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'error',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      errorGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'error',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> errorLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'error',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> errorBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'error',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> errorStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'error',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> errorEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'error',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> errorContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'error',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> errorMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'error',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> errorIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'error',
        value: '',
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      errorIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'error',
        value: '',
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> logTypeEqualTo(
      GPLogType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'logType',
        value: value,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      logTypeGreaterThan(
    GPLogType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'logType',
        value: value,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> logTypeLessThan(
    GPLogType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'logType',
        value: value,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> logTypeBetween(
    GPLogType lower,
    GPLogType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'logType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> pathEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'path',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> pathGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'path',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> pathLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'path',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> pathBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'path',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> pathStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'path',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> pathEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'path',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> pathContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'path',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> pathMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'path',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition> pathIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'path',
        value: '',
      ));
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterFilterCondition>
      pathIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'path',
        value: '',
      ));
    });
  }
}

extension GPLogEntityQueryObject
    on QueryBuilder<GPLogEntity, GPLogEntity, QFilterCondition> {}

extension GPLogEntityQueryLinks
    on QueryBuilder<GPLogEntity, GPLogEntity, QFilterCondition> {}

extension GPLogEntityQuerySortBy
    on QueryBuilder<GPLogEntity, GPLogEntity, QSortBy> {
  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByCUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cUrl', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByCUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cUrl', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByData() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'data', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByDataDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'data', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByError() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'error', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByErrorDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'error', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByLogType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'logType', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByLogTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'logType', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByPath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'path', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> sortByPathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'path', Sort.desc);
    });
  }
}

extension GPLogEntityQuerySortThenBy
    on QueryBuilder<GPLogEntity, GPLogEntity, QSortThenBy> {
  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByCUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cUrl', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByCUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cUrl', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByData() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'data', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByDataDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'data', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByError() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'error', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByErrorDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'error', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByLogType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'logType', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByLogTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'logType', Sort.desc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByPath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'path', Sort.asc);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QAfterSortBy> thenByPathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'path', Sort.desc);
    });
  }
}

extension GPLogEntityQueryWhereDistinct
    on QueryBuilder<GPLogEntity, GPLogEntity, QDistinct> {
  QueryBuilder<GPLogEntity, GPLogEntity, QDistinct> distinctByCUrl(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cUrl', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QDistinct> distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QDistinct> distinctByData(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'data', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QDistinct> distinctByError(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'error', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QDistinct> distinctByLogType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'logType');
    });
  }

  QueryBuilder<GPLogEntity, GPLogEntity, QDistinct> distinctByPath(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'path', caseSensitive: caseSensitive);
    });
  }
}

extension GPLogEntityQueryProperty
    on QueryBuilder<GPLogEntity, GPLogEntity, QQueryProperty> {
  QueryBuilder<GPLogEntity, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<GPLogEntity, String, QQueryOperations> cUrlProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cUrl');
    });
  }

  QueryBuilder<GPLogEntity, DateTime, QQueryOperations> createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<GPLogEntity, String, QQueryOperations> dataProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'data');
    });
  }

  QueryBuilder<GPLogEntity, String, QQueryOperations> errorProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'error');
    });
  }

  QueryBuilder<GPLogEntity, GPLogType, QQueryOperations> logTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'logType');
    });
  }

  QueryBuilder<GPLogEntity, String, QQueryOperations> pathProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'path');
    });
  }
}
