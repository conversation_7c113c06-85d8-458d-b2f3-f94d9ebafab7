// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_crawl_status.entity.dart';

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const BaseCrawlDownloadStatusSchema = Schema(
  name: r'BaseCrawlDownloadStatus',
  id: 434589811210638330,
  properties: {
    r'code': PropertySchema(
      id: 0,
      name: r'code',
      type: IsarType.string,
    ),
    r'isDownloaded': PropertySchema(
      id: 1,
      name: r'isDownloaded',
      type: IsarType.bool,
    ),
    r'isDownloading': PropertySchema(
      id: 2,
      name: r'isDownloading',
      type: IsarType.bool,
    ),
    r'isFailed': PropertySchema(
      id: 3,
      name: r'isFailed',
      type: IsarType.bool,
    ),
    r'message': PropertySchema(
      id: 4,
      name: r'message',
      type: IsarType.string,
    ),
    r'status': PropertySchema(
      id: 5,
      name: r'status',
      type: IsarType.byte,
      enumMap: _BaseCrawlDownloadStatusstatusEnumValueMap,
    )
  },
  estimateSize: _baseCrawlDownloadStatusEstimateSize,
  serialize: _baseCrawlDownloadStatusSerialize,
  deserialize: _baseCrawlDownloadStatusDeserialize,
  deserializeProp: _baseCrawlDownloadStatusDeserializeProp,
);

int _baseCrawlDownloadStatusEstimateSize(
  BaseCrawlDownloadStatus object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.code;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.message;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _baseCrawlDownloadStatusSerialize(
  BaseCrawlDownloadStatus object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.code);
  writer.writeBool(offsets[1], object.isDownloaded);
  writer.writeBool(offsets[2], object.isDownloading);
  writer.writeBool(offsets[3], object.isFailed);
  writer.writeString(offsets[4], object.message);
  writer.writeByte(offsets[5], object.status.index);
}

BaseCrawlDownloadStatus _baseCrawlDownloadStatusDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = BaseCrawlDownloadStatus(
    code: reader.readStringOrNull(offsets[0]),
    message: reader.readStringOrNull(offsets[4]),
    status: _BaseCrawlDownloadStatusstatusValueEnumMap[
            reader.readByteOrNull(offsets[5])] ??
        BaseCrawlDownloadStatusEnum.none,
  );
  return object;
}

P _baseCrawlDownloadStatusDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readBool(offset)) as P;
    case 2:
      return (reader.readBool(offset)) as P;
    case 3:
      return (reader.readBool(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (_BaseCrawlDownloadStatusstatusValueEnumMap[
              reader.readByteOrNull(offset)] ??
          BaseCrawlDownloadStatusEnum.none) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _BaseCrawlDownloadStatusstatusEnumValueMap = {
  'none': 0,
  'downloading': 1,
  'downloaded': 2,
  'downloadFailed': 3,
  'saveFailed': 4,
};
const _BaseCrawlDownloadStatusstatusValueEnumMap = {
  0: BaseCrawlDownloadStatusEnum.none,
  1: BaseCrawlDownloadStatusEnum.downloading,
  2: BaseCrawlDownloadStatusEnum.downloaded,
  3: BaseCrawlDownloadStatusEnum.downloadFailed,
  4: BaseCrawlDownloadStatusEnum.saveFailed,
};

extension BaseCrawlDownloadStatusQueryFilter on QueryBuilder<
    BaseCrawlDownloadStatus, BaseCrawlDownloadStatus, QFilterCondition> {
  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'code',
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'code',
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'code',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
          QAfterFilterCondition>
      codeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
          QAfterFilterCondition>
      codeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'code',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'code',
        value: '',
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> codeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'code',
        value: '',
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> isDownloadedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isDownloaded',
        value: value,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> isDownloadingEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isDownloading',
        value: value,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> isFailedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isFailed',
        value: value,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'message',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
          QAfterFilterCondition>
      messageContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
          QAfterFilterCondition>
      messageMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'message',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> messageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> statusEqualTo(BaseCrawlDownloadStatusEnum value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> statusGreaterThan(
    BaseCrawlDownloadStatusEnum value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> statusLessThan(
    BaseCrawlDownloadStatusEnum value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<BaseCrawlDownloadStatus, BaseCrawlDownloadStatus,
      QAfterFilterCondition> statusBetween(
    BaseCrawlDownloadStatusEnum lower,
    BaseCrawlDownloadStatusEnum upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension BaseCrawlDownloadStatusQueryObject on QueryBuilder<
    BaseCrawlDownloadStatus, BaseCrawlDownloadStatus, QFilterCondition> {}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const BaseCrawlSyncStatusSchema = Schema(
  name: r'BaseCrawlSyncStatus',
  id: 8526488524000186267,
  properties: {
    r'code': PropertySchema(
      id: 0,
      name: r'code',
      type: IsarType.string,
    ),
    r'message': PropertySchema(
      id: 1,
      name: r'message',
      type: IsarType.string,
    ),
    r'status': PropertySchema(
      id: 2,
      name: r'status',
      type: IsarType.byte,
      enumMap: _BaseCrawlSyncStatusstatusEnumValueMap,
    )
  },
  estimateSize: _baseCrawlSyncStatusEstimateSize,
  serialize: _baseCrawlSyncStatusSerialize,
  deserialize: _baseCrawlSyncStatusDeserialize,
  deserializeProp: _baseCrawlSyncStatusDeserializeProp,
);

int _baseCrawlSyncStatusEstimateSize(
  BaseCrawlSyncStatus object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.code;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.message;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _baseCrawlSyncStatusSerialize(
  BaseCrawlSyncStatus object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.code);
  writer.writeString(offsets[1], object.message);
  writer.writeByte(offsets[2], object.status.index);
}

BaseCrawlSyncStatus _baseCrawlSyncStatusDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = BaseCrawlSyncStatus(
    code: reader.readStringOrNull(offsets[0]),
    message: reader.readStringOrNull(offsets[1]),
    status: _BaseCrawlSyncStatusstatusValueEnumMap[
            reader.readByteOrNull(offsets[2])] ??
        BaseCrawlSyncStatusEnum.syncing,
  );
  return object;
}

P _baseCrawlSyncStatusDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (_BaseCrawlSyncStatusstatusValueEnumMap[
              reader.readByteOrNull(offset)] ??
          BaseCrawlSyncStatusEnum.syncing) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _BaseCrawlSyncStatusstatusEnumValueMap = {
  'syncing': 0,
  'synced': 1,
  'syncFailed': 2,
  'clientMapperFailed': 3,
  'serverMapperFailed': 4,
};
const _BaseCrawlSyncStatusstatusValueEnumMap = {
  0: BaseCrawlSyncStatusEnum.syncing,
  1: BaseCrawlSyncStatusEnum.synced,
  2: BaseCrawlSyncStatusEnum.syncFailed,
  3: BaseCrawlSyncStatusEnum.clientMapperFailed,
  4: BaseCrawlSyncStatusEnum.serverMapperFailed,
};

extension BaseCrawlSyncStatusQueryFilter on QueryBuilder<BaseCrawlSyncStatus,
    BaseCrawlSyncStatus, QFilterCondition> {
  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'code',
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'code',
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'code',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'code',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'code',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'code',
        value: '',
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      codeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'code',
        value: '',
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'message',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'message',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      messageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      statusEqualTo(BaseCrawlSyncStatusEnum value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      statusGreaterThan(
    BaseCrawlSyncStatusEnum value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      statusLessThan(
    BaseCrawlSyncStatusEnum value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<BaseCrawlSyncStatus, BaseCrawlSyncStatus, QAfterFilterCondition>
      statusBetween(
    BaseCrawlSyncStatusEnum lower,
    BaseCrawlSyncStatusEnum upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension BaseCrawlSyncStatusQueryObject on QueryBuilder<BaseCrawlSyncStatus,
    BaseCrawlSyncStatus, QFilterCondition> {}
