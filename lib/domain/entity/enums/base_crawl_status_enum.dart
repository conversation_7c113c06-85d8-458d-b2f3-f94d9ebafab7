/*
 * Created Date: Saturday, 1st June 2024, 10:36:00
 * Author: Toan<PERSON><PERSON>
 * -----
 * Last Modified: Saturday, 1st June 2024 10:41:53
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

/*
  Flow of status:
                                          ---- savedFailed
                         ---- download -> 
                                                              ---- clientMapperFailed (if equal valid error_code)
                                                          ---- syncFailed
                                                      ---- clientMapperFailed
  none -> downloading ->                  syncing ->               synced
                         ---- download failed

*/
enum BaseCrawlDownloadStatusEnum {
  /// nothing
  none,

  /// downloading from Facebook Workplace
  downloading,

  /// saved to storage
  downloaded,

  /// failed when downloading
  downloadFailed,

  /// failed when saving to storage
  saveFailed,
}

enum BaseCrawlSyncStatusEnum {
  /// syncing to GapoWork
  syncing,

  /// synced to GapoWork
  synced,

  /// synced to GapoWork
  syncFailed,

  /// fail when mapping data in client
  clientMapperFailed,

  /// fail when mapping data in server
  serverM<PERSON>perFailed,
}
