import 'package:json_annotation/json_annotation.dart';

enum GPGroupPrivacy {
  @JsonValue('CLOSED')
  closed,
  @JsonValue('PUBLIC')
  public,
}

enum GPGroupDiscoverability {
  @JsonValue('HIDDEN')
  hidden,
  @JsonValue('VISIBLE')
  visible,
}

enum GPPostPrivacy {
  @JsonValue(1)
  public,
  @JsonValue(2)
  friend,
  @JsonValue(3)
  private,
  @JsonValue(4)
  group,
  @JsonValue(5)
  work,
}

enum GPPostAttachmentType {
  image,
  video,
  file,
  link,
  gapoURI,
  livestream,
  background,
  gif,
}

enum GPMentionType {
  @JsonValue(0)
  mention,
  @JsonValue(1)
  tag,
  @JsonValue(2)
  acknowledgement,
}

enum GPBodyType {
  text,
  @JsonValue('quick_reply')
  quickReply,
  menu,
  @JsonValue('multi_image')
  multiImage,
  image,
  sticker,
  message,
  reply,
  @JsonValue('animated_sticker')
  animatedSticker,
}

enum GPThreadType {
  direct,
  group,
}

enum GPMessageType {
  text,
  image,
  @JsonValue('multi_image')
  multiImage,
  file,
  video
}

enum GPCommentType {
  text,
  image,
}

enum GPCommentStatus {
  @JsonValue(1)
  pending,
  @JsonValue(2)
  approved,
  @JsonValue(3)
  rejected,
}

enum GPCommentMediaType {
  image,
  video,
  file,
}

enum GPCommentAsAuthorType {
  user,
  page,
}

enum GPReactionType {
  @JsonValue(1)
  like,
  @JsonValue(2)
  clap,
  @JsonValue(3)
  laugh,
  @JsonValue(4)
  celebrate,
  @JsonValue(5)
  sad,
  @JsonValue(6)
  love,
}

enum GPGroupRole {
  @JsonValue(1)
  owner,
  @JsonValue(2)
  admin,
  @JsonValue(3)
  moderator,
  @JsonValue(4)
  member,
}

enum GPApiUploadType { image, video, audio, files }
