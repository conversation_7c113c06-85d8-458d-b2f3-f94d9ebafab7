import 'package:gp_fbwp_crawler/domain/entity/enums/enums.dart';
import 'package:gp_fbwp_crawler/domain/entity/gapo/gp_post.dart';
import 'package:json_annotation/json_annotation.dart';

part 'gp_comment.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPComment {
  GPComment({
    this.commentAs,
    this.text,
    this.type,
    this.wpPostId,
    this.mentions,
    this.medias,
    this.status,
    this.targetType,
    this.dataSource,
    this.createdAt,
    this.reactions,
    this.replies,
  });

  final GPCommentAs? commentAs;
  final String? text;
  final GPCommentType? type;
  final String? wpPostId;
  final List<GPCommentMention>? mentions;
  final List<GPCommentMedia>? medias;
  final GPCommentStatus? status;
  final String? targetType;
  final int? dataSource;
  final String? createdAt;
  final List<GPReaction>? reactions;
  final List<GPComment>? replies;

  factory GPComment.fromJson(Map<String, dynamic> json) =>
      _$GPCommentFromJson(json);
  Map<String, dynamic> toJson() => _$GPCommentToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPCommentMention {
  GPCommentMention({
    this.offset,
    this.length,
    this.mentionId,
  });
  final int? offset;
  final int? length;
  final int? mentionId;

  factory GPCommentMention.fromJson(Map<String, dynamic> json) =>
      _$GPCommentMentionFromJson(json);
  Map<String, dynamic> toJson() => _$GPCommentMentionToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPCommentMedia {
  GPCommentMedia({
    this.type,
    this.src,
    this.id,
    this.width,
    this.height,
    this.fileType,
    this.name,
    this.size,
  });
  final GPCommentMediaType? type;
  final String? src;
  final String? id;
  final int? width;
  final int? height;
  final String? fileType;
  final String? name;
  final int? size;

  factory GPCommentMedia.fromJson(Map<String, dynamic> json) =>
      _$GPCommentMediaFromJson(json);
  Map<String, dynamic> toJson() => _$GPCommentMediaToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPCommentAs {
  GPCommentAs({this.authorId, this.authorType});
  final GPCommentAsAuthorType? authorType;
  final int? authorId;

  factory GPCommentAs.fromJson(Map<String, dynamic> json) =>
      _$GPCommentAsFromJson(json);
  Map<String, dynamic> toJson() => _$GPCommentAsToJson(this);
}
