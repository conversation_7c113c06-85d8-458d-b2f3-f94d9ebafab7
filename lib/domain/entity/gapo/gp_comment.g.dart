// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_comment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPComment _$GPCommentFromJson(Map<String, dynamic> json) => GPComment(
      commentAs: json['comment_as'] == null
          ? null
          : GPCommentAs.fromJson(json['comment_as'] as Map<String, dynamic>),
      text: json['text'] as String?,
      type: $enumDecodeNullable(_$GPCommentTypeEnumMap, json['type']),
      wpPostId: json['wp_post_id'] as String?,
      mentions: (json['mentions'] as List<dynamic>?)
          ?.map((e) => GPCommentMention.fromJson(e as Map<String, dynamic>))
          .toList(),
      medias: (json['medias'] as List<dynamic>?)
          ?.map((e) => GPCommentMedia.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: $enumDecodeNullable(_$GPCommentStatusEnumMap, json['status']),
      targetType: json['target_type'] as String?,
      dataSource: (json['data_source'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      reactions: (json['reactions'] as List<dynamic>?)
          ?.map((e) => GPReaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      replies: (json['replies'] as List<dynamic>?)
          ?.map((e) => GPComment.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$GPCommentToJson(GPComment instance) => <String, dynamic>{
      'comment_as': instance.commentAs?.toJson(),
      'text': instance.text,
      'type': _$GPCommentTypeEnumMap[instance.type],
      'wp_post_id': instance.wpPostId,
      'mentions': instance.mentions?.map((e) => e.toJson()).toList(),
      'medias': instance.medias?.map((e) => e.toJson()).toList(),
      'status': _$GPCommentStatusEnumMap[instance.status],
      'target_type': instance.targetType,
      'data_source': instance.dataSource,
      'created_at': instance.createdAt,
      'reactions': instance.reactions?.map((e) => e.toJson()).toList(),
      'replies': instance.replies?.map((e) => e.toJson()).toList(),
    };

const _$GPCommentTypeEnumMap = {
  GPCommentType.text: 'text',
  GPCommentType.image: 'image',
};

const _$GPCommentStatusEnumMap = {
  GPCommentStatus.pending: 1,
  GPCommentStatus.approved: 2,
  GPCommentStatus.rejected: 3,
};

GPCommentMention _$GPCommentMentionFromJson(Map<String, dynamic> json) =>
    GPCommentMention(
      offset: (json['offset'] as num?)?.toInt(),
      length: (json['length'] as num?)?.toInt(),
      mentionId: (json['mention_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GPCommentMentionToJson(GPCommentMention instance) =>
    <String, dynamic>{
      'offset': instance.offset,
      'length': instance.length,
      'mention_id': instance.mentionId,
    };

GPCommentMedia _$GPCommentMediaFromJson(Map<String, dynamic> json) =>
    GPCommentMedia(
      type: $enumDecodeNullable(_$GPCommentMediaTypeEnumMap, json['type']),
      src: json['src'] as String?,
      id: json['id'] as String?,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      fileType: json['file_type'] as String?,
      name: json['name'] as String?,
      size: (json['size'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GPCommentMediaToJson(GPCommentMedia instance) =>
    <String, dynamic>{
      'type': _$GPCommentMediaTypeEnumMap[instance.type],
      'src': instance.src,
      'id': instance.id,
      'width': instance.width,
      'height': instance.height,
      'file_type': instance.fileType,
      'name': instance.name,
      'size': instance.size,
    };

const _$GPCommentMediaTypeEnumMap = {
  GPCommentMediaType.image: 'image',
  GPCommentMediaType.video: 'video',
  GPCommentMediaType.file: 'file',
};

GPCommentAs _$GPCommentAsFromJson(Map<String, dynamic> json) => GPCommentAs(
      authorId: (json['author_id'] as num?)?.toInt(),
      authorType: $enumDecodeNullable(
          _$GPCommentAsAuthorTypeEnumMap, json['author_type']),
    );

Map<String, dynamic> _$GPCommentAsToJson(GPCommentAs instance) =>
    <String, dynamic>{
      'author_type': _$GPCommentAsAuthorTypeEnumMap[instance.authorType],
      'author_id': instance.authorId,
    };

const _$GPCommentAsAuthorTypeEnumMap = {
  GPCommentAsAuthorType.user: 'user',
  GPCommentAsAuthorType.page: 'page',
};
