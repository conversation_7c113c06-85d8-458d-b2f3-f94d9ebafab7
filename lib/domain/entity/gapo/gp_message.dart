import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:gp_fbwp_crawler/domain/entity/gapo/upload/gp_upload_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'gp_message.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPMessage {
  GPMessage({
    this.threadId,
    this.type,
    this.from,
    this.text,
    this.media,
    this.createdAt,
    this.isMarkdownText,
  });

  final String? threadId;
  final GPMessageType? type;
  final int? from;
  final String? text;
  final List<GPUploadResponse>? media;
  final String? createdAt;
  final int? isMarkdownText;

  factory GPMessage.fromJson(Map<String, dynamic> json) =>
      _$GPMessageFromJson(json);
  Map<String, dynamic> toJson() => _$GPMessageToJson(this);

  bool get isInvalid {
    return from == null ||
        ((media?.isEmpty ?? true) && text?.isEmpty == true) ||
        ((media?.isEmpty ?? true) && type != GPMessageType.text) ||
        threadId == null ||
        (text?.contains("joined the group.") ?? false) ||
        (text?.contains("left the group.") ?? false);
  }
}
