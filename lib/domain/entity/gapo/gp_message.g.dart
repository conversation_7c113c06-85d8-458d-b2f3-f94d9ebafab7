// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPMessage _$GPMessageFromJson(Map<String, dynamic> json) => GPMessage(
      threadId: json['thread_id'] as String?,
      type: $enumDecodeNullable(_$GPMessageTypeEnumMap, json['type']),
      from: (json['from'] as num?)?.toInt(),
      text: json['text'] as String?,
      media: (json['media'] as List<dynamic>?)
          ?.map((e) => GPUploadResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: json['created_at'] as String?,
      isMarkdownText: (json['is_markdown_text'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GPMessageToJson(GPMessage instance) => <String, dynamic>{
      'thread_id': instance.threadId,
      'type': _$GPMessageTypeEnumMap[instance.type],
      'from': instance.from,
      'text': instance.text,
      'media': instance.media?.map((e) => e.toJson()).toList(),
      'created_at': instance.createdAt,
      'is_markdown_text': instance.isMarkdownText,
    };

const _$GPMessageTypeEnumMap = {
  GPMessageType.text: 'text',
  GPMessageType.image: 'image',
  GPMessageType.multiImage: 'multi_image',
  GPMessageType.file: 'file',
  GPMessageType.video: 'video',
};
