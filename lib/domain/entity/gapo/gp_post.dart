import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:json_annotation/json_annotation.dart';

part 'gp_post.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPPost {
  GPPost({
    this.content,
    this.contentRtf,
    this.media,
    this.mention,
    this.privacy,
    this.target,
    this.userId,
    this.createdAt,
    this.comments,
    this.reactions,
    this.seen,
  });
  // final String? wpGroupId;
  final String? userId;
  final String? content;
  final int? contentRtf;
  final List<GPPostMedia>? media;
  final List<GPPostMention>? mention;
  final GPPostPrivacy? privacy;
  final String? target;
  // final GPPostPollVote? pollVote;
  final List<GPComment>? comments;
  final List<GPUser>? seen;
  final List<GPReaction>? reactions;
  final String? createdAt;

  factory GPPost.fromJson(Map<String, dynamic> json) => _$GPPostFromJson(json);
  Map<String, dynamic> toJson() => _$GPPostToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class GPReaction {
  GPReaction({
    this.userId,
    this.type,
  });
  final int? userId;
  final GPReactionType? type;

  factory GPReaction.fromJson(Map<String, dynamic> json) =>
      _$GPReactionFromJson(json);
  Map<String, dynamic> toJson() => _$GPReactionToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class GPPostMedia {
  GPPostMedia({
    this.type,
    this.src,
    this.id,
    this.width,
    this.height,
    this.fileType,
    this.name,
    this.size,
  });
  final String? id;
  final String? src;
  final GPPostAttachmentType? type;
  final int? width;
  final int? height;
  final String? fileType;
  final String? name;
  final int? size;

  factory GPPostMedia.fromJson(Map<String, dynamic> json) =>
      _$GPPostMediaFromJson(json);
  Map<String, dynamic> toJson() => _$GPPostMediaToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class GPPostMention {
  GPPostMention({
    this.id,
    this.length,
    this.offset,
    this.type,
  });
  final int? id;
  final int? length;
  final int? offset;
  final GPMentionType? type;

  factory GPPostMention.fromJson(Map<String, dynamic> json) =>
      _$GPPostMentionFromJson(json);
  Map<String, dynamic> toJson() => _$GPPostMentionToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class GPPostPollVote {
  GPPostPollVote({
    this.id,
    this.allowAddChoice,
    this.allowMultipleChoice,
    this.createdAt,
    this.endAt,
    this.incognito,
    this.myselfVotes,
    this.title,
    this.totalUsers,
    this.type,
    this.updatedAt,
    this.userId,
    this.votes,
  });
  final String? id;
  final bool? allowAddChoice;
  final bool? allowMultipleChoice;
  final String? createdAt;
  final String? endAt;
  final bool? incognito;
  final List<String>? myselfVotes;
  final String? title;
  final int? totalUsers;
  final String? type;
  final String? updatedAt;
  final String? userId;
  final GPVote? votes;
  factory GPPostPollVote.fromJson(Map<String, dynamic> json) =>
      _$GPPostPollVoteFromJson(json);
  Map<String, dynamic> toJson() => _$GPPostPollVoteToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class GPVote {
  GPVote({
    this.id,
    this.count,
    this.createdAt,
    this.image,
    this.lastUserUpdate,
    this.pollId,
    this.title,
    this.updatedAt,
    this.userId,
    this.userVotes,
  });
  final String? id;
  final int? count;
  final String? createdAt;
  final String? image;
  final String? lastUserUpdate;
  final String? pollId;
  final String? title;
  final String? updatedAt;
  final String? userId;
  final List<String>? userVotes;
  factory GPVote.fromJson(Map<String, dynamic> json) => _$GPVoteFromJson(json);
  Map<String, dynamic> toJson() => _$GPVoteToJson(this);
}
