// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_post.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPPost _$GPPostFromJson(Map<String, dynamic> json) => GPPost(
      content: json['content'] as String?,
      contentRtf: (json['content_rtf'] as num?)?.toInt(),
      media: (json['media'] as List<dynamic>?)
          ?.map((e) => GPPostMedia.fromJson(e as Map<String, dynamic>))
          .toList(),
      mention: (json['mention'] as List<dynamic>?)
          ?.map((e) => GPPostMention.fromJson(e as Map<String, dynamic>))
          .toList(),
      privacy: $enumDecodeNullable(_$GPPostPrivacyEnumMap, json['privacy']),
      target: json['target'] as String?,
      userId: json['user_id'] as String?,
      createdAt: json['created_at'] as String?,
      comments: (json['comments'] as List<dynamic>?)
          ?.map((e) => GPComment.fromJson(e as Map<String, dynamic>))
          .toList(),
      reactions: (json['reactions'] as List<dynamic>?)
          ?.map((e) => GPReaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      seen: (json['seen'] as List<dynamic>?)
          ?.map((e) => GPUser.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$GPPostToJson(GPPost instance) => <String, dynamic>{
      'user_id': instance.userId,
      'content': instance.content,
      'content_rtf': instance.contentRtf,
      'media': instance.media?.map((e) => e.toJson()).toList(),
      'mention': instance.mention?.map((e) => e.toJson()).toList(),
      'privacy': _$GPPostPrivacyEnumMap[instance.privacy],
      'target': instance.target,
      'comments': instance.comments?.map((e) => e.toJson()).toList(),
      'seen': instance.seen?.map((e) => e.toJson()).toList(),
      'reactions': instance.reactions?.map((e) => e.toJson()).toList(),
      'created_at': instance.createdAt,
    };

const _$GPPostPrivacyEnumMap = {
  GPPostPrivacy.public: 1,
  GPPostPrivacy.friend: 2,
  GPPostPrivacy.private: 3,
  GPPostPrivacy.group: 4,
  GPPostPrivacy.work: 5,
};

GPReaction _$GPReactionFromJson(Map<String, dynamic> json) => GPReaction(
      userId: (json['user_id'] as num?)?.toInt(),
      type: $enumDecodeNullable(_$GPReactionTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$GPReactionToJson(GPReaction instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'type': _$GPReactionTypeEnumMap[instance.type],
    };

const _$GPReactionTypeEnumMap = {
  GPReactionType.like: 1,
  GPReactionType.clap: 2,
  GPReactionType.laugh: 3,
  GPReactionType.celebrate: 4,
  GPReactionType.sad: 5,
  GPReactionType.love: 6,
};

GPPostMedia _$GPPostMediaFromJson(Map<String, dynamic> json) => GPPostMedia(
      type: $enumDecodeNullable(_$GPPostAttachmentTypeEnumMap, json['type']),
      src: json['src'] as String?,
      id: json['id'] as String?,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      fileType: json['file_type'] as String?,
      name: json['name'] as String?,
      size: (json['size'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GPPostMediaToJson(GPPostMedia instance) =>
    <String, dynamic>{
      'id': instance.id,
      'src': instance.src,
      'type': _$GPPostAttachmentTypeEnumMap[instance.type],
      'width': instance.width,
      'height': instance.height,
      'file_type': instance.fileType,
      'name': instance.name,
      'size': instance.size,
    };

const _$GPPostAttachmentTypeEnumMap = {
  GPPostAttachmentType.image: 'image',
  GPPostAttachmentType.video: 'video',
  GPPostAttachmentType.file: 'file',
  GPPostAttachmentType.link: 'link',
  GPPostAttachmentType.gapoURI: 'gapoURI',
  GPPostAttachmentType.livestream: 'livestream',
  GPPostAttachmentType.background: 'background',
  GPPostAttachmentType.gif: 'gif',
};

GPPostMention _$GPPostMentionFromJson(Map<String, dynamic> json) =>
    GPPostMention(
      id: (json['id'] as num?)?.toInt(),
      length: (json['length'] as num?)?.toInt(),
      offset: (json['offset'] as num?)?.toInt(),
      type: $enumDecodeNullable(_$GPMentionTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$GPPostMentionToJson(GPPostMention instance) =>
    <String, dynamic>{
      'id': instance.id,
      'length': instance.length,
      'offset': instance.offset,
      'type': _$GPMentionTypeEnumMap[instance.type],
    };

const _$GPMentionTypeEnumMap = {
  GPMentionType.mention: 0,
  GPMentionType.tag: 1,
  GPMentionType.acknowledgement: 2,
};

GPPostPollVote _$GPPostPollVoteFromJson(Map<String, dynamic> json) =>
    GPPostPollVote(
      id: json['id'] as String?,
      allowAddChoice: json['allow_add_choice'] as bool?,
      allowMultipleChoice: json['allow_multiple_choice'] as bool?,
      createdAt: json['created_at'] as String?,
      endAt: json['end_at'] as String?,
      incognito: json['incognito'] as bool?,
      myselfVotes: (json['myself_votes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      title: json['title'] as String?,
      totalUsers: (json['total_users'] as num?)?.toInt(),
      type: json['type'] as String?,
      updatedAt: json['updated_at'] as String?,
      userId: json['user_id'] as String?,
      votes: json['votes'] == null
          ? null
          : GPVote.fromJson(json['votes'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GPPostPollVoteToJson(GPPostPollVote instance) =>
    <String, dynamic>{
      'id': instance.id,
      'allow_add_choice': instance.allowAddChoice,
      'allow_multiple_choice': instance.allowMultipleChoice,
      'created_at': instance.createdAt,
      'end_at': instance.endAt,
      'incognito': instance.incognito,
      'myself_votes': instance.myselfVotes,
      'title': instance.title,
      'total_users': instance.totalUsers,
      'type': instance.type,
      'updated_at': instance.updatedAt,
      'user_id': instance.userId,
      'votes': instance.votes,
    };

GPVote _$GPVoteFromJson(Map<String, dynamic> json) => GPVote(
      id: json['id'] as String?,
      count: (json['count'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      image: json['image'] as String?,
      lastUserUpdate: json['last_user_update'] as String?,
      pollId: json['poll_id'] as String?,
      title: json['title'] as String?,
      updatedAt: json['updated_at'] as String?,
      userId: json['user_id'] as String?,
      userVotes: (json['user_votes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$GPVoteToJson(GPVote instance) => <String, dynamic>{
      'id': instance.id,
      'count': instance.count,
      'created_at': instance.createdAt,
      'image': instance.image,
      'last_user_update': instance.lastUserUpdate,
      'poll_id': instance.pollId,
      'title': instance.title,
      'updated_at': instance.updatedAt,
      'user_id': instance.userId,
      'user_votes': instance.userVotes,
    };
