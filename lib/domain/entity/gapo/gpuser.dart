import 'package:json_annotation/json_annotation.dart';

part 'gpuser.g.dart';

@JsonSerializable()
class GPUser {
  String? name, avatar, type;
  int? id;

  String? displayName;
  String? avatarThumbPattern;
  int? statusVerify;

  GPUser({
    this.name,
    this.avatar,
    this.type,
    this.displayName,
    this.id,
    this.avatarThumbPattern,
    this.statusVerify,
  });

  factory GPUser.fromJson(Map<String, dynamic> data) => _$GPUserFromJson(data);

  Map<String, dynamic> toJson() => _$GPUserToJson(this);
}
