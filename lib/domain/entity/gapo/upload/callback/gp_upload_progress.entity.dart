/*
 * Created Date: Wednesday, 12th June 2024, 08:43:19
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 12th June 2024 10:42:20
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:freezed_annotation/freezed_annotation.dart';

part 'gp_upload_progress.entity.freezed.dart';

/// Lưu trữ các phần progress của File khi đang upload / download
@freezed
class GPUploadProgressEntity with _$GPUploadProgressEntity {
  factory GPUploadProgressEntity({
    @Default(Duration.zero) Duration estimate,
    @Default(0.0) double progress,
  }) = _GPUploadProgressEntity;
}

extension GPUploadProgressEntityOperator on GPUploadProgressEntity {
  /// sum 2 [GPUploadProgressEntity]
  /// progress can be exceed to 100
  GPUploadProgressEntity add(GPUploadProgressEntity other) {
    return copyWith(
      progress: progress + other.progress,
      estimate: estimate + other.estimate,
    );
  }
}
