// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_upload_progress.entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPUploadProgressEntity {
  Duration get estimate => throw _privateConstructorUsedError;
  double get progress => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $GPUploadProgressEntityCopyWith<GPUploadProgressEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPUploadProgressEntityCopyWith<$Res> {
  factory $GPUploadProgressEntityCopyWith(GPUploadProgressEntity value,
          $Res Function(GPUploadProgressEntity) then) =
      _$GPUploadProgressEntityCopyWithImpl<$Res, GPUploadProgressEntity>;
  @useResult
  $Res call({Duration estimate, double progress});
}

/// @nodoc
class _$GPUploadProgressEntityCopyWithImpl<$Res,
        $Val extends GPUploadProgressEntity>
    implements $GPUploadProgressEntityCopyWith<$Res> {
  _$GPUploadProgressEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? estimate = null,
    Object? progress = null,
  }) {
    return _then(_value.copyWith(
      estimate: null == estimate
          ? _value.estimate
          : estimate // ignore: cast_nullable_to_non_nullable
              as Duration,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPUploadProgressEntityImplCopyWith<$Res>
    implements $GPUploadProgressEntityCopyWith<$Res> {
  factory _$$GPUploadProgressEntityImplCopyWith(
          _$GPUploadProgressEntityImpl value,
          $Res Function(_$GPUploadProgressEntityImpl) then) =
      __$$GPUploadProgressEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Duration estimate, double progress});
}

/// @nodoc
class __$$GPUploadProgressEntityImplCopyWithImpl<$Res>
    extends _$GPUploadProgressEntityCopyWithImpl<$Res,
        _$GPUploadProgressEntityImpl>
    implements _$$GPUploadProgressEntityImplCopyWith<$Res> {
  __$$GPUploadProgressEntityImplCopyWithImpl(
      _$GPUploadProgressEntityImpl _value,
      $Res Function(_$GPUploadProgressEntityImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? estimate = null,
    Object? progress = null,
  }) {
    return _then(_$GPUploadProgressEntityImpl(
      estimate: null == estimate
          ? _value.estimate
          : estimate // ignore: cast_nullable_to_non_nullable
              as Duration,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$GPUploadProgressEntityImpl implements _GPUploadProgressEntity {
  _$GPUploadProgressEntityImpl(
      {this.estimate = Duration.zero, this.progress = 0.0});

  @override
  @JsonKey()
  final Duration estimate;
  @override
  @JsonKey()
  final double progress;

  @override
  String toString() {
    return 'GPUploadProgressEntity(estimate: $estimate, progress: $progress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPUploadProgressEntityImpl &&
            (identical(other.estimate, estimate) ||
                other.estimate == estimate) &&
            (identical(other.progress, progress) ||
                other.progress == progress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, estimate, progress);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPUploadProgressEntityImplCopyWith<_$GPUploadProgressEntityImpl>
      get copyWith => __$$GPUploadProgressEntityImplCopyWithImpl<
          _$GPUploadProgressEntityImpl>(this, _$identity);
}

abstract class _GPUploadProgressEntity implements GPUploadProgressEntity {
  factory _GPUploadProgressEntity(
      {final Duration estimate,
      final double progress}) = _$GPUploadProgressEntityImpl;

  @override
  Duration get estimate;
  @override
  double get progress;
  @override
  @JsonKey(ignore: true)
  _$$GPUploadProgressEntityImplCopyWith<_$GPUploadProgressEntityImpl>
      get copyWith => throw _privateConstructorUsedError;
}
