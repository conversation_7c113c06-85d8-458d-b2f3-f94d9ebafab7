/*
 * Created Date: Wednesday, 12th June 2024, 08:38:22
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 12th June 2024 11:33:11
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_fbwp_crawler/domain/entity/enums/upload/gp_upload_status_enum.dart';
import 'package:gp_fbwp_crawler/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart';

part 'gp_dashboard_upload.entity.freezed.dart';

/// progress tổng
@freezed
class GPUploadDashboardEntity with _$GPUploadDashboardEntity {
  factory GPUploadDashboardEntity({
    /// progress tổng
    GPUploadProgressEntity? progressEntity,

    /// status tổng
    GPUploadStatus? status,

    /// tổng số files đã upload
    int? totalUploadedFiles,

    /// tổng số files upload theo tất cả các status
    int? totalUploadFiles,
  }) = _GPUploadDashboardEntity;
}
