import 'package:freezed_annotation/freezed_annotation.dart';

part 'gp_upload_response.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPUploadResponse {
  GPUploadResponse({
    this.id,
    this.name,
    this.userId,
    this.size,
    this.fileType,
    this.type,
    this.url,
    this.thumbUrl,
    this.src,
    this.fileLink,
    this.quality,
    this.source,
    this.category,
    this.width,
    this.height,
  });
  String? id;

  String? name;

  String? userId;

  int? size;

  String? fileType;

  String? type;

  GPUploadFileURLResponse? url;

  GPUploadFileURLResponse? thumbUrl;

  String? src;

  String? fileLink;

  String? quality;

  String? source;

   String? category;

  int? width;

  int? height;

  factory GPUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$GPUploadResponseFromJson(json);
  Map<String, dynamic> toJson() => _$GPUploadResponseToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPUploadFileURLResponse {
  GPUploadFileURLResponse({
    this.store,
    this.src,
  });
  String? store;
  String? src;

  factory GPUploadFileURLResponse.fromJson(Map<String, dynamic> json) =>
      _$GPUploadFileURLResponseFromJson(json);
  Map<String, dynamic> toJson() => _$GPUploadFileURLResponseToJson(this);
}
