// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_upload_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPUploadResponse _$GPUploadResponseFromJson(Map<String, dynamic> json) =>
    GPUploadResponse(
      id: json['id'] as String?,
      name: json['name'] as String?,
      userId: json['user_id'] as String?,
      size: (json['size'] as num?)?.toInt(),
      fileType: json['file_type'] as String?,
      type: json['type'] as String?,
      url: json['url'] == null
          ? null
          : GPUploadFileURLResponse.fromJson(
              json['url'] as Map<String, dynamic>),
      thumbUrl: json['thumb_url'] == null
          ? null
          : GPUploadFileURLResponse.fromJson(
              json['thumb_url'] as Map<String, dynamic>),
      src: json['src'] as String?,
      fileLink: json['file_link'] as String?,
      quality: json['quality'] as String?,
      source: json['source'] as String?,
      category: json['category'] as String?,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GPUploadResponseToJson(GPUploadResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'user_id': instance.userId,
      'size': instance.size,
      'file_type': instance.fileType,
      'type': instance.type,
      'url': instance.url?.toJson(),
      'thumb_url': instance.thumbUrl?.toJson(),
      'src': instance.src,
      'file_link': instance.fileLink,
      'quality': instance.quality,
      'source': instance.source,
      'category': instance.category,
      'width': instance.width,
      'height': instance.height,
    };

GPUploadFileURLResponse _$GPUploadFileURLResponseFromJson(
        Map<String, dynamic> json) =>
    GPUploadFileURLResponse(
      store: json['store'] as String?,
      src: json['src'] as String?,
    );

Map<String, dynamic> _$GPUploadFileURLResponseToJson(
        GPUploadFileURLResponse instance) =>
    <String, dynamic>{
      'store': instance.store,
      'src': instance.src,
    };
