import 'dart:io';

import 'package:gp_core/core.dart';
import 'package:gp_fbwp_crawler/domain/entity/enums/enums.dart';
import 'package:gp_fbwp_crawler/domain/entity/gapo/upload/callback/callback.dart';

final class GPUploadFileInput {
  GPUploadFileInput({
    required this.file,
    required this.uploadType,
  });

  final File file;
  final GPApiUploadType uploadType;

  /// listening if needed
  // final BehaviorSubject<GPUploadProgressEntity> rxProgress =
  //     BehaviorSubject.seeded(GPUploadProgressEntity());
}

/// input params for uploading
final class GPUploadInput {
  const GPUploadInput({
    required this.fileInputs,
    this.cancelToken,
    this.dashBoardCallback,
    this.itemCallback,
  });
  final List<GPUploadFileInput> fileInputs;
  final CancelToken? cancelToken;
  final GPUploadDashBoardCallback? dashBoardCallback;
  final GPUploadCallback? itemCallback;
}
