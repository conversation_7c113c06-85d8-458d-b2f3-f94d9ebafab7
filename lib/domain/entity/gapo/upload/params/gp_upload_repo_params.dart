import 'dart:io';

import 'package:gp_core/core.dart';
import 'package:gp_fbwp_crawler/domain/entity/enums/enums.dart';

class GPUploadRepoInput {
  const GPUploadRepoInput({
    required this.file,
    this.uploadType = GPApiUploadType.files,
    this.cancelToken,
    this.sendProgress,
    this.receiveProgress,
  });

  final File file;
  final GPApiUploadType uploadType;
  final CancelToken? cancelToken;
  final ProgressCallback? sendProgress;
  final ProgressCallback? receiveProgress;
}
