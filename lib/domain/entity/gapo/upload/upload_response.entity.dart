import 'package:isar/isar.dart';

part 'upload_response.entity.g.dart';

@embedded
class UploadResponseEntity {
  UploadResponseEntity({
    this.id,
    this.name,
    this.userId,
    this.size,
    this.fileType,
    this.type,
    this.url,
    this.thumbUrl,
    this.src,
    this.fileLink,
    this.quality,
    this.source,
    this.category,
    this.width,
    this.height,
  });
  String? id;

  String? name;

  String? userId;

  int? size;

  String? fileType;

  String? type;

  UploadFileURLResponseEntity? url;

  UploadFileURLResponseEntity? thumbUrl;

  String? src;

  String? fileLink;

  String? quality;

  String? source;

   String? category;

  int? width;

  int? height;
}

@embedded
class UploadFileURLResponseEntity {
  UploadFileURLResponseEntity({
    this.store,
    this.src,
  });
  String? store;
  String? src;
}
