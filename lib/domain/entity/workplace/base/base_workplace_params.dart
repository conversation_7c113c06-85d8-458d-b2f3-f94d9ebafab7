class WorkPlaceBaseParams {
  WorkPlaceBaseParams({
    required this.id,
    this.fields,
    this.limit = '5',
    this.nextQueries,
    this.since,
    this.until,
  });

  final String? fields;
  final String? limit;
  Map<String, String>? nextQueries;
  final int? since;
  final int? until;

  String id;

  Map<String, String> get requestParams {
    if (nextQueries != null) {
    final newQueries = Map<String, String>.from(nextQueries!);
      if (!newQueries.containsKey('limit')) {
        newQueries['limit'] = limit!;
      }
      return newQueries;
    }

    final query = <String, String>{};

    if (fields != null) query['fields'] = fields!;
    if (limit != null) query['limit'] = limit!;
    if (since != null) query['since'] = since.toString();
    if (until != null) query['until'] = until.toString();

    return query;
  }
}
