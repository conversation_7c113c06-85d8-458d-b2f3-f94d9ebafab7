/*
 * Created Date: Friday, 21st June 2024, 09:59:32
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 12th September 2024 22:04:25
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/data/model/workplace/enums/enums.dart';
import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

part 'workplace_feed.entity.g.dart';

@Collection()
class WorkPlaceFeedEntity extends BaseCrawlEntity {
  WorkPlaceFeedEntity({
    required super.id,
    super.crawlType = GPBaseCrawlType.feed,
    this.message,
    this.updatedTime,
    this.formatting,
    this.createdTime,
    List<WorkPlaceAttachmentEntity>? attachments,
    List<WorkPlaceCommentEntity>? comments,
    List<WorkPlaceCommunityMemberEntity>? to,
    WorkPlaceCommunityMemberEntity? from,
    this.reactions,
    List<WorkPlaceCommunityMemberEntity>? seen,
    this.isUserFeed = false,
    this.insertedAt,
  }) {
    if (comments != null && comments.isNotEmpty) {
      this.comments.addAll(comments);
    }

    if (attachments != null && attachments.isNotEmpty) {
      this.attachments.addAll(attachments);
    }

    if (to != null && to.isNotEmpty) {
      this.to.addAll(to);
    }

    if (from != null) {
      this.from.value = from;
    }

    if (seen != null && seen.isNotEmpty) {
      this.seen.addAll(seen);
    }
  }

  final String? message;

  final comments = IsarLinks<WorkPlaceCommentEntity>();
  final attachments = IsarLinks<WorkPlaceAttachmentEntity>();

  /// Profiles mentioned or targeted in this post.
  final to = IsarLinks<WorkPlaceCommunityMemberEntity>();
  final from = IsarLink<WorkPlaceCommunityMemberEntity>();

  final DateTime? createdTime;
  final DateTime? updatedTime;

  @Enumerated(EnumType.name)
  final WorkPlaceFormatting? formatting;

  final group = IsarLink<WorkPlaceGroupEntity>();

  List<WorkPlaceReactionEntity>? reactions;
  final seen = IsarLinks<WorkPlaceCommunityMemberEntity>();

  int? gpUserId;
  bool isUserFeed;
  String? gpPostId;

  DateTime? insertedAt;

  late final Id? dbId = id.hashCode;

  @ignore
  bool get isPollVote {
    if (attachments.isNotEmpty) {
      final questionType = attachments
          .where((element) => element.type == AttachmentType.question);
      return questionType.isNotEmpty;
    }
    return false;
  }

  WorkPlaceFeedEntity copyWith({
    String? newMessage,
    DateTime? newUpdatedTime,
    WorkPlaceFormatting? newFormatting,
    DateTime? newCreatedTime,
    List<WorkPlaceCommentEntity>? newComments,
    List<WorkPlaceAttachmentEntity>? newAttachments,
    List<WorkPlaceCommunityMemberEntity>? newTo,
    WorkPlaceCommunityMemberEntity? newFrom,
    List<WorkPlaceReactionEntity>? newReactions,
    List<WorkPlaceCommunityMemberEntity>? newSeen,
    bool? newIsUserFeed,
  }) {
    return WorkPlaceFeedEntity(
      id: id,
      crawlType: crawlType,
      message: newMessage ?? message,
      updatedTime: newUpdatedTime ?? updatedTime,
      formatting: newFormatting ?? formatting,
      createdTime: newCreatedTime ?? createdTime,
      comments: newComments,
      attachments: newAttachments,
      to: newTo,
      from: newFrom ?? from.value,
      reactions: newReactions ?? reactions,
      seen: newSeen,
      isUserFeed: newIsUserFeed ?? isUserFeed,
    );
  }
}
