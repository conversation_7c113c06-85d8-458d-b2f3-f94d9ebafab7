// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_group.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWorkPlaceGroupEntityCollection on Isar {
  IsarCollection<WorkPlaceGroupEntity> get workPlaceGroupEntitys =>
      this.collection();
}

const WorkPlaceGroupEntitySchema = CollectionSchema(
  name: r'WorkPlaceGroupEntity',
  id: -2066939628274418416,
  properties: {
    r'archived': PropertySchema(
      id: 0,
      name: r'archived',
      type: IsarType.bool,
    ),
    r'cover': PropertySchema(
      id: 1,
      name: r'cover',
      type: IsarType.object,
      target: r'GroupCoverEntity',
    ),
    r'crawlType': PropertySchema(
      id: 2,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _WorkPlaceGroupEntitycrawlTypeEnumValueMap,
    ),
    r'createdTime': PropertySchema(
      id: 3,
      name: r'createdTime',
      type: IsarType.dateTime,
    ),
    r'description': PropertySchema(
      id: 4,
      name: r'description',
      type: IsarType.string,
    ),
    r'gpCoverLink': PropertySchema(
      id: 5,
      name: r'gpCoverLink',
      type: IsarType.string,
    ),
    r'gpGroupId': PropertySchema(
      id: 6,
      name: r'gpGroupId',
      type: IsarType.string,
    ),
    r'icon': PropertySchema(
      id: 7,
      name: r'icon',
      type: IsarType.string,
    ),
    r'id': PropertySchema(
      id: 8,
      name: r'id',
      type: IsarType.string,
    ),
    r'insertedAt': PropertySchema(
      id: 9,
      name: r'insertedAt',
      type: IsarType.dateTime,
    ),
    r'name': PropertySchema(
      id: 10,
      name: r'name',
      type: IsarType.string,
    ),
    r'postRequiresAdminApproval': PropertySchema(
      id: 11,
      name: r'postRequiresAdminApproval',
      type: IsarType.bool,
    ),
    r'privacy': PropertySchema(
      id: 12,
      name: r'privacy',
      type: IsarType.string,
      enumMap: _WorkPlaceGroupEntityprivacyEnumValueMap,
    ),
    r'updatedTime': PropertySchema(
      id: 13,
      name: r'updatedTime',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _workPlaceGroupEntityEstimateSize,
  serialize: _workPlaceGroupEntitySerialize,
  deserialize: _workPlaceGroupEntityDeserialize,
  deserializeProp: _workPlaceGroupEntityDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {
    r'owner': LinkSchema(
      id: -1209821427811258083,
      name: r'owner',
      target: r'WorkPlaceCommunityMemberEntity',
      single: true,
    ),
    r'admins': LinkSchema(
      id: 4734976739454254720,
      name: r'admins',
      target: r'WorkPlaceCommunityMemberEntity',
      single: false,
    ),
    r'members': LinkSchema(
      id: 2613012293586874927,
      name: r'members',
      target: r'WorkPlaceCommunityMemberEntity',
      single: false,
    )
  },
  embeddedSchemas: {r'GroupCoverEntity': GroupCoverEntitySchema},
  getId: _workPlaceGroupEntityGetId,
  getLinks: _workPlaceGroupEntityGetLinks,
  attach: _workPlaceGroupEntityAttach,
  version: '3.1.0+1',
);

int _workPlaceGroupEntityEstimateSize(
  WorkPlaceGroupEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.cover;
    if (value != null) {
      bytesCount += 3 +
          GroupCoverEntitySchema.estimateSize(
              value, allOffsets[GroupCoverEntity]!, allOffsets);
    }
  }
  {
    final value = object.description;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.gpCoverLink;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.gpGroupId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.icon;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.id.length * 3;
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.privacy;
    if (value != null) {
      bytesCount += 3 + value.name.length * 3;
    }
  }
  return bytesCount;
}

void _workPlaceGroupEntitySerialize(
  WorkPlaceGroupEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeBool(offsets[0], object.archived);
  writer.writeObject<GroupCoverEntity>(
    offsets[1],
    allOffsets,
    GroupCoverEntitySchema.serialize,
    object.cover,
  );
  writer.writeByte(offsets[2], object.crawlType.index);
  writer.writeDateTime(offsets[3], object.createdTime);
  writer.writeString(offsets[4], object.description);
  writer.writeString(offsets[5], object.gpCoverLink);
  writer.writeString(offsets[6], object.gpGroupId);
  writer.writeString(offsets[7], object.icon);
  writer.writeString(offsets[8], object.id);
  writer.writeDateTime(offsets[9], object.insertedAt);
  writer.writeString(offsets[10], object.name);
  writer.writeBool(offsets[11], object.postRequiresAdminApproval);
  writer.writeString(offsets[12], object.privacy?.name);
  writer.writeDateTime(offsets[13], object.updatedTime);
}

WorkPlaceGroupEntity _workPlaceGroupEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceGroupEntity(
    archived: reader.readBoolOrNull(offsets[0]),
    cover: reader.readObjectOrNull<GroupCoverEntity>(
      offsets[1],
      GroupCoverEntitySchema.deserialize,
      allOffsets,
    ),
    crawlType: _WorkPlaceGroupEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[2])] ??
        GPBaseCrawlType.group,
    createdTime: reader.readDateTimeOrNull(offsets[3]),
    description: reader.readStringOrNull(offsets[4]),
    icon: reader.readStringOrNull(offsets[7]),
    id: reader.readString(offsets[8]),
    insertedAt: reader.readDateTimeOrNull(offsets[9]),
    name: reader.readStringOrNull(offsets[10]),
    postRequiresAdminApproval: reader.readBoolOrNull(offsets[11]),
    privacy: _WorkPlaceGroupEntityprivacyValueEnumMap[
        reader.readStringOrNull(offsets[12])],
    updatedTime: reader.readDateTimeOrNull(offsets[13]),
  );
  object.gpCoverLink = reader.readStringOrNull(offsets[5]);
  object.gpGroupId = reader.readStringOrNull(offsets[6]);
  return object;
}

P _workPlaceGroupEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readBoolOrNull(offset)) as P;
    case 1:
      return (reader.readObjectOrNull<GroupCoverEntity>(
        offset,
        GroupCoverEntitySchema.deserialize,
        allOffsets,
      )) as P;
    case 2:
      return (_WorkPlaceGroupEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.group) as P;
    case 3:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readString(offset)) as P;
    case 9:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readBoolOrNull(offset)) as P;
    case 12:
      return (_WorkPlaceGroupEntityprivacyValueEnumMap[
          reader.readStringOrNull(offset)]) as P;
    case 13:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceGroupEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _WorkPlaceGroupEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};
const _WorkPlaceGroupEntityprivacyEnumValueMap = {
  r'open': r'open',
  r'closed': r'closed',
  r'secret': r'secret',
};
const _WorkPlaceGroupEntityprivacyValueEnumMap = {
  r'open': WorkPlaceGroupPrivacy.open,
  r'closed': WorkPlaceGroupPrivacy.closed,
  r'secret': WorkPlaceGroupPrivacy.secret,
};

Id _workPlaceGroupEntityGetId(WorkPlaceGroupEntity object) {
  return object.dbId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _workPlaceGroupEntityGetLinks(
    WorkPlaceGroupEntity object) {
  return [object.owner, object.admins, object.members];
}

void _workPlaceGroupEntityAttach(
    IsarCollection<dynamic> col, Id id, WorkPlaceGroupEntity object) {
  object.owner.attach(
      col, col.isar.collection<WorkPlaceCommunityMemberEntity>(), r'owner', id);
  object.admins.attach(col,
      col.isar.collection<WorkPlaceCommunityMemberEntity>(), r'admins', id);
  object.members.attach(col,
      col.isar.collection<WorkPlaceCommunityMemberEntity>(), r'members', id);
}

extension WorkPlaceGroupEntityQueryWhereSort
    on QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QWhere> {
  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterWhere>
      anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WorkPlaceGroupEntityQueryWhere
    on QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QWhereClause> {
  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterWhereClause>
      dbIdEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterWhereClause>
      dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterWhereClause>
      dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterWhereClause>
      dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterWhereClause>
      dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceGroupEntityQueryFilter on QueryBuilder<WorkPlaceGroupEntity,
    WorkPlaceGroupEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> archivedIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'archived',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> archivedIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'archived',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> archivedEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'archived',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> coverIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cover',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> coverIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cover',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> createdTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> createdTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> createdTimeEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> createdTimeGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> createdTimeLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> createdTimeBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> dbIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> dbIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> dbIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> dbIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> dbIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> dbIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'description',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'description',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'description',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      descriptionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      descriptionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'description',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> descriptionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpCoverLink',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpCoverLink',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpCoverLink',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      gpCoverLinkContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      gpCoverLinkMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'gpCoverLink',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpCoverLink',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpCoverLinkIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'gpCoverLink',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpGroupId',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpGroupId',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpGroupId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpGroupId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpGroupId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpGroupId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'gpGroupId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'gpGroupId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      gpGroupIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'gpGroupId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      gpGroupIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'gpGroupId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpGroupId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> gpGroupIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'gpGroupId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'icon',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'icon',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'icon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'icon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'icon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'icon',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'icon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'icon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      iconContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'icon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      iconMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'icon',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'icon',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> iconIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'icon',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> insertedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'insertedAt',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> insertedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'insertedAt',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> insertedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> insertedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> insertedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> insertedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'insertedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> postRequiresAdminApprovalIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'postRequiresAdminApproval',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> postRequiresAdminApprovalIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'postRequiresAdminApproval',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> postRequiresAdminApprovalEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'postRequiresAdminApproval',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'privacy',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'privacy',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyEqualTo(
    WorkPlaceGroupPrivacy? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'privacy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyGreaterThan(
    WorkPlaceGroupPrivacy? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'privacy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyLessThan(
    WorkPlaceGroupPrivacy? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'privacy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyBetween(
    WorkPlaceGroupPrivacy? lower,
    WorkPlaceGroupPrivacy? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'privacy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'privacy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'privacy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      privacyContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'privacy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      privacyMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'privacy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'privacy',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> privacyIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'privacy',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> updatedTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> updatedTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> updatedTimeEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> updatedTimeGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> updatedTimeLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> updatedTimeBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceGroupEntityQueryObject on QueryBuilder<WorkPlaceGroupEntity,
    WorkPlaceGroupEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> cover(FilterQuery<GroupCoverEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'cover');
    });
  }
}

extension WorkPlaceGroupEntityQueryLinks on QueryBuilder<WorkPlaceGroupEntity,
    WorkPlaceGroupEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      owner(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'owner');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> ownerIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'owner', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      admins(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'admins');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> adminsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'admins', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> adminsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'admins', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> adminsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'admins', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> adminsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'admins', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> adminsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'admins', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> adminsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'admins', lower, includeLower, upper, includeUpper);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
          QAfterFilterCondition>
      members(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'members');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> membersLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'members', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> membersIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'members', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> membersIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'members', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> membersLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'members', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> membersLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'members', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity,
      QAfterFilterCondition> membersLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'members', lower, includeLower, upper, includeUpper);
    });
  }
}

extension WorkPlaceGroupEntityQuerySortBy
    on QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QSortBy> {
  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByArchived() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'archived', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByArchivedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'archived', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByCreatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByGpCoverLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpCoverLink', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByGpCoverLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpCoverLink', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByGpGroupId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpGroupId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByGpGroupIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpGroupId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByIcon() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'icon', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByIconDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'icon', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByInsertedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByPostRequiresAdminApproval() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'postRequiresAdminApproval', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByPostRequiresAdminApprovalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'postRequiresAdminApproval', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByPrivacy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'privacy', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByPrivacyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'privacy', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByUpdatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      sortByUpdatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedTime', Sort.desc);
    });
  }
}

extension WorkPlaceGroupEntityQuerySortThenBy
    on QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QSortThenBy> {
  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByArchived() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'archived', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByArchivedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'archived', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByCreatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByGpCoverLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpCoverLink', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByGpCoverLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpCoverLink', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByGpGroupId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpGroupId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByGpGroupIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpGroupId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByIcon() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'icon', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByIconDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'icon', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByInsertedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByPostRequiresAdminApproval() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'postRequiresAdminApproval', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByPostRequiresAdminApprovalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'postRequiresAdminApproval', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByPrivacy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'privacy', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByPrivacyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'privacy', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByUpdatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QAfterSortBy>
      thenByUpdatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedTime', Sort.desc);
    });
  }
}

extension WorkPlaceGroupEntityQueryWhereDistinct
    on QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct> {
  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByArchived() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'archived');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdTime');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByDescription({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'description', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByGpCoverLink({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'gpCoverLink', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByGpGroupId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'gpGroupId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByIcon({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'icon', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'insertedAt');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByPostRequiresAdminApproval() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'postRequiresAdminApproval');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByPrivacy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'privacy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupEntity, QDistinct>
      distinctByUpdatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedTime');
    });
  }
}

extension WorkPlaceGroupEntityQueryProperty on QueryBuilder<
    WorkPlaceGroupEntity, WorkPlaceGroupEntity, QQueryProperty> {
  QueryBuilder<WorkPlaceGroupEntity, int, QQueryOperations> dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, bool?, QQueryOperations>
      archivedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'archived');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, GroupCoverEntity?, QQueryOperations>
      coverProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cover');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, GPBaseCrawlType, QQueryOperations>
      crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, DateTime?, QQueryOperations>
      createdTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdTime');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, String?, QQueryOperations>
      descriptionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'description');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, String?, QQueryOperations>
      gpCoverLinkProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'gpCoverLink');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, String?, QQueryOperations>
      gpGroupIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'gpGroupId');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, String?, QQueryOperations> iconProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'icon');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, String, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, DateTime?, QQueryOperations>
      insertedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'insertedAt');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, String?, QQueryOperations> nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, bool?, QQueryOperations>
      postRequiresAdminApprovalProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'postRequiresAdminApproval');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, WorkPlaceGroupPrivacy?, QQueryOperations>
      privacyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'privacy');
    });
  }

  QueryBuilder<WorkPlaceGroupEntity, DateTime?, QQueryOperations>
      updatedTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedTime');
    });
  }
}

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const GroupCoverEntitySchema = Schema(
  name: r'GroupCoverEntity',
  id: -7571517387291118009,
  properties: {
    r'coverId': PropertySchema(
      id: 0,
      name: r'coverId',
      type: IsarType.string,
    ),
    r'gpCoverLink': PropertySchema(
      id: 1,
      name: r'gpCoverLink',
      type: IsarType.string,
    ),
    r'id': PropertySchema(
      id: 2,
      name: r'id',
      type: IsarType.string,
    ),
    r'localFilePath': PropertySchema(
      id: 3,
      name: r'localFilePath',
      type: IsarType.string,
    ),
    r'source': PropertySchema(
      id: 4,
      name: r'source',
      type: IsarType.string,
    )
  },
  estimateSize: _groupCoverEntityEstimateSize,
  serialize: _groupCoverEntitySerialize,
  deserialize: _groupCoverEntityDeserialize,
  deserializeProp: _groupCoverEntityDeserializeProp,
);

int _groupCoverEntityEstimateSize(
  GroupCoverEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.coverId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.gpCoverLink;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.id;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.localFilePath;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.source;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _groupCoverEntitySerialize(
  GroupCoverEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.coverId);
  writer.writeString(offsets[1], object.gpCoverLink);
  writer.writeString(offsets[2], object.id);
  writer.writeString(offsets[3], object.localFilePath);
  writer.writeString(offsets[4], object.source);
}

GroupCoverEntity _groupCoverEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = GroupCoverEntity(
    coverId: reader.readStringOrNull(offsets[0]),
    id: reader.readStringOrNull(offsets[2]),
    source: reader.readStringOrNull(offsets[4]),
  );
  object.gpCoverLink = reader.readStringOrNull(offsets[1]);
  object.localFilePath = reader.readStringOrNull(offsets[3]);
  return object;
}

P _groupCoverEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension GroupCoverEntityQueryFilter
    on QueryBuilder<GroupCoverEntity, GroupCoverEntity, QFilterCondition> {
  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'coverId',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'coverId',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'coverId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'coverId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'coverId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'coverId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'coverId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'coverId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'coverId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'coverId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'coverId',
        value: '',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      coverIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'coverId',
        value: '',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpCoverLink',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpCoverLink',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpCoverLink',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'gpCoverLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'gpCoverLink',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpCoverLink',
        value: '',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      gpCoverLinkIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'gpCoverLink',
        value: '',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'localFilePath',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'localFilePath',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localFilePath',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'localFilePath',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localFilePath',
        value: '',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      localFilePathIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'localFilePath',
        value: '',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'source',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'source',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'source',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'source',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'source',
        value: '',
      ));
    });
  }

  QueryBuilder<GroupCoverEntity, GroupCoverEntity, QAfterFilterCondition>
      sourceIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'source',
        value: '',
      ));
    });
  }
}

extension GroupCoverEntityQueryObject
    on QueryBuilder<GroupCoverEntity, GroupCoverEntity, QFilterCondition> {}
