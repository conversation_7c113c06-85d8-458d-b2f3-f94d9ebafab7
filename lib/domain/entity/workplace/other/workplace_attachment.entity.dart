/*
 * Created Date: Tuesday, 11th June 2024, 08:46:28
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Saturday, 14th September 2024 23:50:57
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/data/model/workplace/enums/workplace_enums.dart';
import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

part 'workplace_attachment.entity.g.dart';

// @Collection()
// class WorkPlaceAttachmentWrapperEntity extends BaseCrawlEntity {
//   WorkPlaceAttachmentWrapperEntity({
//     required super.id,
//     super.crawlType = GPBaseCrawlType.attachment,
//     List<WorkPlaceAttachmentEntity>? attachments,
//   }) {
//     if (attachments != null && attachments.isNotEmpty) {
//       this.attachments.addAll(attachments);
//       this.attachments.save();
//     }
//   }

//   final attachments = IsarLinks<WorkPlaceAttachmentEntity>();

//   late final Id? dbId = id.hashCode;
// }

@Collection()
class WorkPlaceAttachmentEntity extends BaseCrawlEntity {
  WorkPlaceAttachmentEntity({
    super.id = '',
    super.crawlType = GPBaseCrawlType.attachment,
    this.target,
    this.title,
    this.type,
    this.url,
    // this.subAttachments,
    this.media,
  }) {
    super.id = '$dbId';
  }

  final AttachmentMediaEntity? media;

  // SubAttachmentsEntity? subAttachments;

  final AttachmentTargetEntity? target;
  final String? title;

  @Enumerated(EnumType.name)
  final AttachmentType? type;

  final String? url;

  String? gpLink;
  String? gpId;

  String? localFilePath;

  UploadResponseEntity? uploadResponse;

  String urlDownload() {
    switch (type) {
      case AttachmentType.fileUpload:
        return url ?? '';
      case AttachmentType.photo:
      case AttachmentType.album:
      case AttachmentType.animateImageShare:
      case AttachmentType.sticker:
      case AttachmentType.coverPhoto:
        return media?.image?.src ?? '';
      case AttachmentType.videoOnline:
      case AttachmentType.animateImageVideo:
        return media?.source ?? '';

      default:
        return media?.source ?? '';
    }

    // if (type == AttachmentType.fileUpload) {
    //   return url ?? '';
    // } else if (type == AttachmentType.photo ||
    //     type == AttachmentType.album ||
    //     type == AttachmentType.videoOnline) {
    //   return media?.image?.src ?? '';
    // } else {
    //   return media?.source ?? '';
    // }
  }

  @ignore
  bool get invalidAttachmentType =>
      type == AttachmentType.option ||
      type == AttachmentType.knowledgeNote ||
      type == AttachmentType.nativeTemplates ||
      type == AttachmentType.share ||
      type == AttachmentType.album ||
      type == AttachmentType.event ||
      type == AttachmentType.unknow;

  GPPostMedia? mapToPostMedia() {
    if (invalidAttachmentType) return null;
    final gpType = _mapAttachmentType;
    if (gpType == GPPostAttachmentType.file) {
      return GPPostMedia(
        src: gpLink,
        type: gpType,
        id: gpId,
        fileType: uploadResponse?.fileType,
        name: uploadResponse?.name,
        size: uploadResponse?.size,
      );
    }
    return GPPostMedia(
      src: gpLink,
      type: gpType,
      id: gpId,
      width: media?.image?.width,
      height: media?.image?.height,
      fileType: uploadResponse?.fileType,
      name: uploadResponse?.name,
      size: uploadResponse?.size,
    );
  }

  GPPostAttachmentType get _mapAttachmentType {
    switch (type) {
      case AttachmentType.photo:
      case AttachmentType.sticker:
      case AttachmentType.animateImageShare:
      case AttachmentType.album:
      case AttachmentType.coverPhoto:
        return GPPostAttachmentType.image;
      case AttachmentType.video:
      case AttachmentType.animateImageVideo:
        return GPPostAttachmentType.video;
      default:
        return GPPostAttachmentType.file;
    }
  }

  late final Id? dbId = target?.id != null
      ? target?.id.hashCode
      : (url?.hashCode ?? 0) + (media?.source?.hashCode ?? 1);
}

@embedded
class AttachmentTargetEntity {
  AttachmentTargetEntity({this.id, this.url});

  final String? id;

  /// url tới attachments
  final String? url;
}

// @embedded
// class SubAttachmentsEntity {
//   SubAttachmentsEntity({
//     this.type,
//     this.attachments,
//   });

//   final List<WorkPlaceAttachmentEntity>? attachments;

//   @Enumerated(EnumType.name)
//   final AttachmentType? type;
// }

@embedded
class AttachmentMediaEntity {
  AttachmentMediaEntity({this.image, this.source});

  final AttachmentMediaImageEntity? image;

  /// Source for download
  final String? source;
}

@embedded
class AttachmentMediaImageEntity {
  AttachmentMediaImageEntity({
    this.height,
    this.width,
    this.src,
  });

  final int? height;
  final int? width;
  final String? src;
}

extension WPAttachmentCollectionExt on List<WorkPlaceAttachmentEntity> {
  // List<WorkPlaceAttachmentEntity> flattern() {
  //   if (isEmpty) return [];

  //   List<WorkPlaceAttachmentEntity> flatternAttachments = [];

  //   for (var element in this) {
  //     flatternAttachments.add(element);

  //     if (element.hasSubAttachment) {
  //       flatternAttachments.addAll(element.subAttachments!.attachments!);
  //     }
  //   }

  //   return flatternAttachments;
  // }

  List<WorkPlaceAttachmentEntity> removeUnusedAttachments() {
    // skip poll vote
    removeWhere((element) => element.type == AttachmentType.question);

    return this;
  }
}
