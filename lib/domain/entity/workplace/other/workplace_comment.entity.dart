/*
 * Created Date: Tuesday, 11th June 2024, 08:46:28
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Sunday, 8th September 2024 20:03:51
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

part 'workplace_comment.entity.g.dart';

@Collection()
class WorkPlaceCommentEntity extends BaseCrawlEntity {
  WorkPlaceCommentEntity({
    required super.id,
    super.crawlType = GPBaseCrawlType.comment,
    this.message,
    this.createdTime,
    WorkPlaceCommunityMemberEntity? from,
    WorkPlaceAttachmentEntity? attachment,
    this.likeCount,
    this.commentCount,
    this.reactions,
    this.messageTags,
    this.likes,
    List<WorkPlaceCommentEntity>? reply,
  }) {
    if (from != null) {
      this.from.value = from;
    }

    if (attachment != null) {
      this.attachment.value = attachment;
    }

    if (reply != null) {
      replies.addAll(reply);
    }
  }

  final String? message;
  final DateTime? createdTime;

  final from = IsarLink<WorkPlaceCommunityMemberEntity>();

  final attachment = IsarLink<WorkPlaceAttachmentEntity>();

  final int? likeCount;
  final int? commentCount;
  List<WorkPlaceReactionEntity>? reactions;
  final List<WorkPlaceMessageTagsEntity>? messageTags;
  final List<WorkPlaceReactionEntity>? likes;
  final replies = IsarLinks<WorkPlaceCommentEntity>();

  late final Id? dbId = id.hashCode;

  WorkPlaceCommentEntity copyWith({
    String? message,
    DateTime? createdTime,
    WorkPlaceCommunityMemberEntity? from,
    WorkPlaceAttachmentEntity? attachment,
    int? likeCount,
    int? commentCount,
    List<WorkPlaceReactionEntity>? reactions,
    List<WorkPlaceMessageTagsEntity>? messageTags,
    List<WorkPlaceReactionEntity>? likes,
    List<WorkPlaceCommentEntity>? reply,
  }) {
    return WorkPlaceCommentEntity(
      id: id,
      crawlType: crawlType,
      message: message ?? this.message,
      createdTime: createdTime ?? this.createdTime,
      from: from,
      attachment: attachment,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      reactions: reactions ?? this.reactions,
      messageTags: messageTags ?? this.messageTags,
      likes: likes ?? this.likes,
      reply: reply,
    );
  }
}

@embedded
class WorkPlaceReactionEntity {
  WorkPlaceReactionEntity({
    this.userId,
    this.username,
    this.type = WorkPlaceReactionType.none,
    this.gpUserId,
  });

  final String? userId;

  final String? username;

  int? gpUserId;

  @enumerated
  final WorkPlaceReactionType type;
}

@embedded
class WorkPlaceMessageTagsEntity {
  WorkPlaceMessageTagsEntity({
    this.id,
    this.name,
    this.length = 0,
    this.offset = 0,
    this.type = WorkPlaceMessageTagsType.nothing,
  });

  final String? id;
  final String? name;
  final int length;
  final int offset;

  @enumerated
  final WorkPlaceMessageTagsType type;
}
