// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_comment.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWorkPlaceCommentEntityCollection on Isar {
  IsarCollection<WorkPlaceCommentEntity> get workPlaceCommentEntitys =>
      this.collection();
}

const WorkPlaceCommentEntitySchema = CollectionSchema(
  name: r'WorkPlaceCommentEntity',
  id: 1558438411708766440,
  properties: {
    r'commentCount': PropertySchema(
      id: 0,
      name: r'commentCount',
      type: IsarType.long,
    ),
    r'crawlType': PropertySchema(
      id: 1,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _WorkPlaceCommentEntitycrawlTypeEnumValueMap,
    ),
    r'createdTime': PropertySchema(
      id: 2,
      name: r'createdTime',
      type: IsarType.dateTime,
    ),
    r'id': PropertySchema(
      id: 3,
      name: r'id',
      type: IsarType.string,
    ),
    r'likeCount': PropertySchema(
      id: 4,
      name: r'likeCount',
      type: IsarType.long,
    ),
    r'likes': PropertySchema(
      id: 5,
      name: r'likes',
      type: IsarType.objectList,
      target: r'WorkPlaceReactionEntity',
    ),
    r'message': PropertySchema(
      id: 6,
      name: r'message',
      type: IsarType.string,
    ),
    r'messageTags': PropertySchema(
      id: 7,
      name: r'messageTags',
      type: IsarType.objectList,
      target: r'WorkPlaceMessageTagsEntity',
    ),
    r'reactions': PropertySchema(
      id: 8,
      name: r'reactions',
      type: IsarType.objectList,
      target: r'WorkPlaceReactionEntity',
    )
  },
  estimateSize: _workPlaceCommentEntityEstimateSize,
  serialize: _workPlaceCommentEntitySerialize,
  deserialize: _workPlaceCommentEntityDeserialize,
  deserializeProp: _workPlaceCommentEntityDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {
    r'from': LinkSchema(
      id: 4058383129701479770,
      name: r'from',
      target: r'WorkPlaceCommunityMemberEntity',
      single: true,
    ),
    r'attachment': LinkSchema(
      id: -7945730271352281485,
      name: r'attachment',
      target: r'WorkPlaceAttachmentEntity',
      single: true,
    ),
    r'replies': LinkSchema(
      id: -6454635164192961511,
      name: r'replies',
      target: r'WorkPlaceCommentEntity',
      single: false,
    )
  },
  embeddedSchemas: {
    r'WorkPlaceReactionEntity': WorkPlaceReactionEntitySchema,
    r'WorkPlaceMessageTagsEntity': WorkPlaceMessageTagsEntitySchema
  },
  getId: _workPlaceCommentEntityGetId,
  getLinks: _workPlaceCommentEntityGetLinks,
  attach: _workPlaceCommentEntityAttach,
  version: '3.1.0+1',
);

int _workPlaceCommentEntityEstimateSize(
  WorkPlaceCommentEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.id.length * 3;
  {
    final list = object.likes;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        final offsets = allOffsets[WorkPlaceReactionEntity]!;
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += WorkPlaceReactionEntitySchema.estimateSize(
              value, offsets, allOffsets);
        }
      }
    }
  }
  {
    final value = object.message;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final list = object.messageTags;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        final offsets = allOffsets[WorkPlaceMessageTagsEntity]!;
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += WorkPlaceMessageTagsEntitySchema.estimateSize(
              value, offsets, allOffsets);
        }
      }
    }
  }
  {
    final list = object.reactions;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        final offsets = allOffsets[WorkPlaceReactionEntity]!;
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += WorkPlaceReactionEntitySchema.estimateSize(
              value, offsets, allOffsets);
        }
      }
    }
  }
  return bytesCount;
}

void _workPlaceCommentEntitySerialize(
  WorkPlaceCommentEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.commentCount);
  writer.writeByte(offsets[1], object.crawlType.index);
  writer.writeDateTime(offsets[2], object.createdTime);
  writer.writeString(offsets[3], object.id);
  writer.writeLong(offsets[4], object.likeCount);
  writer.writeObjectList<WorkPlaceReactionEntity>(
    offsets[5],
    allOffsets,
    WorkPlaceReactionEntitySchema.serialize,
    object.likes,
  );
  writer.writeString(offsets[6], object.message);
  writer.writeObjectList<WorkPlaceMessageTagsEntity>(
    offsets[7],
    allOffsets,
    WorkPlaceMessageTagsEntitySchema.serialize,
    object.messageTags,
  );
  writer.writeObjectList<WorkPlaceReactionEntity>(
    offsets[8],
    allOffsets,
    WorkPlaceReactionEntitySchema.serialize,
    object.reactions,
  );
}

WorkPlaceCommentEntity _workPlaceCommentEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceCommentEntity(
    commentCount: reader.readLongOrNull(offsets[0]),
    crawlType: _WorkPlaceCommentEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[1])] ??
        GPBaseCrawlType.comment,
    createdTime: reader.readDateTimeOrNull(offsets[2]),
    id: reader.readString(offsets[3]),
    likeCount: reader.readLongOrNull(offsets[4]),
    likes: reader.readObjectList<WorkPlaceReactionEntity>(
      offsets[5],
      WorkPlaceReactionEntitySchema.deserialize,
      allOffsets,
      WorkPlaceReactionEntity(),
    ),
    message: reader.readStringOrNull(offsets[6]),
    messageTags: reader.readObjectList<WorkPlaceMessageTagsEntity>(
      offsets[7],
      WorkPlaceMessageTagsEntitySchema.deserialize,
      allOffsets,
      WorkPlaceMessageTagsEntity(),
    ),
    reactions: reader.readObjectList<WorkPlaceReactionEntity>(
      offsets[8],
      WorkPlaceReactionEntitySchema.deserialize,
      allOffsets,
      WorkPlaceReactionEntity(),
    ),
  );
  return object;
}

P _workPlaceCommentEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (_WorkPlaceCommentEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.comment) as P;
    case 2:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 3:
      return (reader.readString(offset)) as P;
    case 4:
      return (reader.readLongOrNull(offset)) as P;
    case 5:
      return (reader.readObjectList<WorkPlaceReactionEntity>(
        offset,
        WorkPlaceReactionEntitySchema.deserialize,
        allOffsets,
        WorkPlaceReactionEntity(),
      )) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readObjectList<WorkPlaceMessageTagsEntity>(
        offset,
        WorkPlaceMessageTagsEntitySchema.deserialize,
        allOffsets,
        WorkPlaceMessageTagsEntity(),
      )) as P;
    case 8:
      return (reader.readObjectList<WorkPlaceReactionEntity>(
        offset,
        WorkPlaceReactionEntitySchema.deserialize,
        allOffsets,
        WorkPlaceReactionEntity(),
      )) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceCommentEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _WorkPlaceCommentEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};

Id _workPlaceCommentEntityGetId(WorkPlaceCommentEntity object) {
  return object.dbId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _workPlaceCommentEntityGetLinks(
    WorkPlaceCommentEntity object) {
  return [object.from, object.attachment, object.replies];
}

void _workPlaceCommentEntityAttach(
    IsarCollection<dynamic> col, Id id, WorkPlaceCommentEntity object) {
  object.from.attach(
      col, col.isar.collection<WorkPlaceCommunityMemberEntity>(), r'from', id);
  object.attachment.attach(
      col, col.isar.collection<WorkPlaceAttachmentEntity>(), r'attachment', id);
  object.replies.attach(
      col, col.isar.collection<WorkPlaceCommentEntity>(), r'replies', id);
}

extension WorkPlaceCommentEntityQueryWhereSort
    on QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QWhere> {
  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterWhere>
      anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WorkPlaceCommentEntityQueryWhere on QueryBuilder<
    WorkPlaceCommentEntity, WorkPlaceCommentEntity, QWhereClause> {
  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterWhereClause> dbIdEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterWhereClause> dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterWhereClause> dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterWhereClause> dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterWhereClause> dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceCommentEntityQueryFilter on QueryBuilder<
    WorkPlaceCommentEntity, WorkPlaceCommentEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> commentCountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'commentCount',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> commentCountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'commentCount',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> commentCountEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'commentCount',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> commentCountGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'commentCount',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> commentCountLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'commentCount',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> commentCountBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'commentCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> createdTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> createdTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> createdTimeEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> createdTimeGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> createdTimeLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> createdTimeBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> dbIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> dbIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> dbIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> dbIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> dbIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> dbIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
          QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
          QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likeCountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'likeCount',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likeCountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'likeCount',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likeCountEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'likeCount',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likeCountGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'likeCount',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likeCountLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'likeCount',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likeCountBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'likeCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'likes',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'likes',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'likes',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'likes',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'likes',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'likes',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'likes',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> likesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'likes',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'message',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
          QAfterFilterCondition>
      messageContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
          QAfterFilterCondition>
      messageMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'message',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageTagsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'messageTags',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageTagsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'messageTags',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageTagsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'messageTags',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageTagsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'messageTags',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageTagsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'messageTags',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageTagsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'messageTags',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageTagsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'messageTags',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> messageTagsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'messageTags',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> reactionsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reactions',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> reactionsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reactions',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> reactionsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> reactionsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> reactionsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> reactionsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> reactionsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> reactionsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }
}

extension WorkPlaceCommentEntityQueryObject on QueryBuilder<
    WorkPlaceCommentEntity, WorkPlaceCommentEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
          QAfterFilterCondition>
      likesElement(FilterQuery<WorkPlaceReactionEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'likes');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
          QAfterFilterCondition>
      messageTagsElement(FilterQuery<WorkPlaceMessageTagsEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'messageTags');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
          QAfterFilterCondition>
      reactionsElement(FilterQuery<WorkPlaceReactionEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'reactions');
    });
  }
}

extension WorkPlaceCommentEntityQueryLinks on QueryBuilder<
    WorkPlaceCommentEntity, WorkPlaceCommentEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
          QAfterFilterCondition>
      from(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'from');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> fromIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'from', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
          QAfterFilterCondition>
      attachment(FilterQuery<WorkPlaceAttachmentEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'attachment');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> attachmentIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachment', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> replies(FilterQuery<WorkPlaceCommentEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'replies');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> repliesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'replies', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> repliesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'replies', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> repliesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'replies', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> repliesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'replies', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> repliesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'replies', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity,
      QAfterFilterCondition> repliesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'replies', lower, includeLower, upper, includeUpper);
    });
  }
}

extension WorkPlaceCommentEntityQuerySortBy
    on QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QSortBy> {
  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByCommentCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commentCount', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByCommentCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commentCount', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByCreatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByLikeCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'likeCount', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByLikeCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'likeCount', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByMessage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      sortByMessageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.desc);
    });
  }
}

extension WorkPlaceCommentEntityQuerySortThenBy on QueryBuilder<
    WorkPlaceCommentEntity, WorkPlaceCommentEntity, QSortThenBy> {
  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByCommentCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commentCount', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByCommentCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commentCount', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByCreatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByLikeCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'likeCount', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByLikeCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'likeCount', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByMessage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QAfterSortBy>
      thenByMessageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.desc);
    });
  }
}

extension WorkPlaceCommentEntityQueryWhereDistinct
    on QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QDistinct> {
  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QDistinct>
      distinctByCommentCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'commentCount');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QDistinct>
      distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QDistinct>
      distinctByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdTime');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QDistinct>
      distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QDistinct>
      distinctByLikeCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'likeCount');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, WorkPlaceCommentEntity, QDistinct>
      distinctByMessage({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'message', caseSensitive: caseSensitive);
    });
  }
}

extension WorkPlaceCommentEntityQueryProperty on QueryBuilder<
    WorkPlaceCommentEntity, WorkPlaceCommentEntity, QQueryProperty> {
  QueryBuilder<WorkPlaceCommentEntity, int, QQueryOperations> dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, int?, QQueryOperations>
      commentCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'commentCount');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, GPBaseCrawlType, QQueryOperations>
      crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, DateTime?, QQueryOperations>
      createdTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdTime');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, String, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, int?, QQueryOperations>
      likeCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'likeCount');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, List<WorkPlaceReactionEntity>?,
      QQueryOperations> likesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'likes');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, String?, QQueryOperations>
      messageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'message');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, List<WorkPlaceMessageTagsEntity>?,
      QQueryOperations> messageTagsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messageTags');
    });
  }

  QueryBuilder<WorkPlaceCommentEntity, List<WorkPlaceReactionEntity>?,
      QQueryOperations> reactionsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reactions');
    });
  }
}

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const WorkPlaceReactionEntitySchema = Schema(
  name: r'WorkPlaceReactionEntity',
  id: -6916900907607015791,
  properties: {
    r'gpUserId': PropertySchema(
      id: 0,
      name: r'gpUserId',
      type: IsarType.long,
    ),
    r'type': PropertySchema(
      id: 1,
      name: r'type',
      type: IsarType.byte,
      enumMap: _WorkPlaceReactionEntitytypeEnumValueMap,
    ),
    r'userId': PropertySchema(
      id: 2,
      name: r'userId',
      type: IsarType.string,
    ),
    r'username': PropertySchema(
      id: 3,
      name: r'username',
      type: IsarType.string,
    )
  },
  estimateSize: _workPlaceReactionEntityEstimateSize,
  serialize: _workPlaceReactionEntitySerialize,
  deserialize: _workPlaceReactionEntityDeserialize,
  deserializeProp: _workPlaceReactionEntityDeserializeProp,
);

int _workPlaceReactionEntityEstimateSize(
  WorkPlaceReactionEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.userId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.username;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _workPlaceReactionEntitySerialize(
  WorkPlaceReactionEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.gpUserId);
  writer.writeByte(offsets[1], object.type.index);
  writer.writeString(offsets[2], object.userId);
  writer.writeString(offsets[3], object.username);
}

WorkPlaceReactionEntity _workPlaceReactionEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceReactionEntity(
    gpUserId: reader.readLongOrNull(offsets[0]),
    type: _WorkPlaceReactionEntitytypeValueEnumMap[
            reader.readByteOrNull(offsets[1])] ??
        WorkPlaceReactionType.none,
    userId: reader.readStringOrNull(offsets[2]),
    username: reader.readStringOrNull(offsets[3]),
  );
  return object;
}

P _workPlaceReactionEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (_WorkPlaceReactionEntitytypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          WorkPlaceReactionType.none) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceReactionEntitytypeEnumValueMap = {
  'like': 0,
  'love': 1,
  'angry': 2,
  'wow': 3,
  'sad': 4,
  'haha': 5,
  'care': 6,
  'none': 7,
};
const _WorkPlaceReactionEntitytypeValueEnumMap = {
  0: WorkPlaceReactionType.like,
  1: WorkPlaceReactionType.love,
  2: WorkPlaceReactionType.angry,
  3: WorkPlaceReactionType.wow,
  4: WorkPlaceReactionType.sad,
  5: WorkPlaceReactionType.haha,
  6: WorkPlaceReactionType.care,
  7: WorkPlaceReactionType.none,
};

extension WorkPlaceReactionEntityQueryFilter on QueryBuilder<
    WorkPlaceReactionEntity, WorkPlaceReactionEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> gpUserIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> gpUserIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> gpUserIdEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> gpUserIdGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> gpUserIdLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> gpUserIdBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpUserId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> typeEqualTo(WorkPlaceReactionType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> typeGreaterThan(
    WorkPlaceReactionType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> typeLessThan(
    WorkPlaceReactionType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> typeBetween(
    WorkPlaceReactionType lower,
    WorkPlaceReactionType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'type',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
          QAfterFilterCondition>
      userIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
          QAfterFilterCondition>
      userIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'userId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> userIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'userId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'username',
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'username',
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'username',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'username',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'username',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'username',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'username',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'username',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
          QAfterFilterCondition>
      usernameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'username',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
          QAfterFilterCondition>
      usernameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'username',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'username',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceReactionEntity, WorkPlaceReactionEntity,
      QAfterFilterCondition> usernameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'username',
        value: '',
      ));
    });
  }
}

extension WorkPlaceReactionEntityQueryObject on QueryBuilder<
    WorkPlaceReactionEntity, WorkPlaceReactionEntity, QFilterCondition> {}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const WorkPlaceMessageTagsEntitySchema = Schema(
  name: r'WorkPlaceMessageTagsEntity',
  id: -6707371991040778814,
  properties: {
    r'id': PropertySchema(
      id: 0,
      name: r'id',
      type: IsarType.string,
    ),
    r'length': PropertySchema(
      id: 1,
      name: r'length',
      type: IsarType.long,
    ),
    r'name': PropertySchema(
      id: 2,
      name: r'name',
      type: IsarType.string,
    ),
    r'offset': PropertySchema(
      id: 3,
      name: r'offset',
      type: IsarType.long,
    ),
    r'type': PropertySchema(
      id: 4,
      name: r'type',
      type: IsarType.byte,
      enumMap: _WorkPlaceMessageTagsEntitytypeEnumValueMap,
    )
  },
  estimateSize: _workPlaceMessageTagsEntityEstimateSize,
  serialize: _workPlaceMessageTagsEntitySerialize,
  deserialize: _workPlaceMessageTagsEntityDeserialize,
  deserializeProp: _workPlaceMessageTagsEntityDeserializeProp,
);

int _workPlaceMessageTagsEntityEstimateSize(
  WorkPlaceMessageTagsEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.id;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _workPlaceMessageTagsEntitySerialize(
  WorkPlaceMessageTagsEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.id);
  writer.writeLong(offsets[1], object.length);
  writer.writeString(offsets[2], object.name);
  writer.writeLong(offsets[3], object.offset);
  writer.writeByte(offsets[4], object.type.index);
}

WorkPlaceMessageTagsEntity _workPlaceMessageTagsEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceMessageTagsEntity(
    id: reader.readStringOrNull(offsets[0]),
    length: reader.readLongOrNull(offsets[1]) ?? 0,
    name: reader.readStringOrNull(offsets[2]),
    offset: reader.readLongOrNull(offsets[3]) ?? 0,
    type: _WorkPlaceMessageTagsEntitytypeValueEnumMap[
            reader.readByteOrNull(offsets[4])] ??
        WorkPlaceMessageTagsType.nothing,
  );
  return object;
}

P _workPlaceMessageTagsEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 4:
      return (_WorkPlaceMessageTagsEntitytypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          WorkPlaceMessageTagsType.nothing) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceMessageTagsEntitytypeEnumValueMap = {
  'user': 0,
  'page': 1,
  'group': 2,
  'nothing': 3,
};
const _WorkPlaceMessageTagsEntitytypeValueEnumMap = {
  0: WorkPlaceMessageTagsType.user,
  1: WorkPlaceMessageTagsType.page,
  2: WorkPlaceMessageTagsType.group,
  3: WorkPlaceMessageTagsType.nothing,
};

extension WorkPlaceMessageTagsEntityQueryFilter on QueryBuilder<
    WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
          QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
          QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> lengthEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'length',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> lengthGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'length',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> lengthLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'length',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> lengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'length',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
          QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
          QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> offsetEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'offset',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> offsetGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'offset',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> offsetLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'offset',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> offsetBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'offset',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> typeEqualTo(WorkPlaceMessageTagsType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> typeGreaterThan(
    WorkPlaceMessageTagsType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> typeLessThan(
    WorkPlaceMessageTagsType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity,
      QAfterFilterCondition> typeBetween(
    WorkPlaceMessageTagsType lower,
    WorkPlaceMessageTagsType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'type',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceMessageTagsEntityQueryObject on QueryBuilder<
    WorkPlaceMessageTagsEntity, WorkPlaceMessageTagsEntity, QFilterCondition> {}
