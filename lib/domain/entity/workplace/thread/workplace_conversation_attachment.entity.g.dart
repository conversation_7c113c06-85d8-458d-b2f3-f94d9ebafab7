// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_conversation_attachment.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWorkPlaceConversationAttachmentsEntityCollection on Isar {
  IsarCollection<WorkPlaceConversationAttachmentsEntity>
      get workPlaceConversationAttachmentsEntitys => this.collection();
}

const WorkPlaceConversationAttachmentsEntitySchema = CollectionSchema(
  name: r'WorkPlaceConversationAttachmentsEntity',
  id: -7400544486071396317,
  properties: {
    r'crawlType': PropertySchema(
      id: 0,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _WorkPlaceConversationAttachmentsEntitycrawlTypeEnumValueMap,
    ),
    r'fileUrl': PropertySchema(
      id: 1,
      name: r'fileUrl',
      type: IsarType.string,
    ),
    r'gpLink': PropertySchema(
      id: 2,
      name: r'gpLink',
      type: IsarType.string,
    ),
    r'id': PropertySchema(
      id: 3,
      name: r'id',
      type: IsarType.string,
    ),
    r'imageData': PropertySchema(
      id: 4,
      name: r'imageData',
      type: IsarType.object,
      target: r'AttachmentImageDataEntity',
    ),
    r'localFilePath': PropertySchema(
      id: 5,
      name: r'localFilePath',
      type: IsarType.string,
    ),
    r'mimeType': PropertySchema(
      id: 6,
      name: r'mimeType',
      type: IsarType.string,
    ),
    r'name': PropertySchema(
      id: 7,
      name: r'name',
      type: IsarType.string,
    ),
    r'size': PropertySchema(
      id: 8,
      name: r'size',
      type: IsarType.long,
    ),
    r'src': PropertySchema(
      id: 9,
      name: r'src',
      type: IsarType.string,
    ),
    r'uploadResponse': PropertySchema(
      id: 10,
      name: r'uploadResponse',
      type: IsarType.object,
      target: r'UploadResponseEntity',
    ),
    r'videoData': PropertySchema(
      id: 11,
      name: r'videoData',
      type: IsarType.object,
      target: r'AttachmentVideoDataEntity',
    )
  },
  estimateSize: _workPlaceConversationAttachmentsEntityEstimateSize,
  serialize: _workPlaceConversationAttachmentsEntitySerialize,
  deserialize: _workPlaceConversationAttachmentsEntityDeserialize,
  deserializeProp: _workPlaceConversationAttachmentsEntityDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {},
  embeddedSchemas: {
    r'AttachmentImageDataEntity': AttachmentImageDataEntitySchema,
    r'AttachmentVideoDataEntity': AttachmentVideoDataEntitySchema,
    r'UploadResponseEntity': UploadResponseEntitySchema,
    r'UploadFileURLResponseEntity': UploadFileURLResponseEntitySchema
  },
  getId: _workPlaceConversationAttachmentsEntityGetId,
  getLinks: _workPlaceConversationAttachmentsEntityGetLinks,
  attach: _workPlaceConversationAttachmentsEntityAttach,
  version: '3.1.0+1',
);

int _workPlaceConversationAttachmentsEntityEstimateSize(
  WorkPlaceConversationAttachmentsEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.fileUrl;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.gpLink;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.id.length * 3;
  {
    final value = object.imageData;
    if (value != null) {
      bytesCount += 3 +
          AttachmentImageDataEntitySchema.estimateSize(
              value, allOffsets[AttachmentImageDataEntity]!, allOffsets);
    }
  }
  {
    final value = object.localFilePath;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.mimeType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.src;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.uploadResponse;
    if (value != null) {
      bytesCount += 3 +
          UploadResponseEntitySchema.estimateSize(
              value, allOffsets[UploadResponseEntity]!, allOffsets);
    }
  }
  {
    final value = object.videoData;
    if (value != null) {
      bytesCount += 3 +
          AttachmentVideoDataEntitySchema.estimateSize(
              value, allOffsets[AttachmentVideoDataEntity]!, allOffsets);
    }
  }
  return bytesCount;
}

void _workPlaceConversationAttachmentsEntitySerialize(
  WorkPlaceConversationAttachmentsEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeByte(offsets[0], object.crawlType.index);
  writer.writeString(offsets[1], object.fileUrl);
  writer.writeString(offsets[2], object.gpLink);
  writer.writeString(offsets[3], object.id);
  writer.writeObject<AttachmentImageDataEntity>(
    offsets[4],
    allOffsets,
    AttachmentImageDataEntitySchema.serialize,
    object.imageData,
  );
  writer.writeString(offsets[5], object.localFilePath);
  writer.writeString(offsets[6], object.mimeType);
  writer.writeString(offsets[7], object.name);
  writer.writeLong(offsets[8], object.size);
  writer.writeString(offsets[9], object.src);
  writer.writeObject<UploadResponseEntity>(
    offsets[10],
    allOffsets,
    UploadResponseEntitySchema.serialize,
    object.uploadResponse,
  );
  writer.writeObject<AttachmentVideoDataEntity>(
    offsets[11],
    allOffsets,
    AttachmentVideoDataEntitySchema.serialize,
    object.videoData,
  );
}

WorkPlaceConversationAttachmentsEntity
    _workPlaceConversationAttachmentsEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceConversationAttachmentsEntity(
    crawlType: _WorkPlaceConversationAttachmentsEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[0])] ??
        GPBaseCrawlType.attachment,
    fileUrl: reader.readStringOrNull(offsets[1]),
    id: reader.readString(offsets[3]),
    imageData: reader.readObjectOrNull<AttachmentImageDataEntity>(
      offsets[4],
      AttachmentImageDataEntitySchema.deserialize,
      allOffsets,
    ),
    mimeType: reader.readStringOrNull(offsets[6]),
    name: reader.readStringOrNull(offsets[7]),
    size: reader.readLongOrNull(offsets[8]),
    videoData: reader.readObjectOrNull<AttachmentVideoDataEntity>(
      offsets[11],
      AttachmentVideoDataEntitySchema.deserialize,
      allOffsets,
    ),
  );
  object.gpLink = reader.readStringOrNull(offsets[2]);
  object.localFilePath = reader.readStringOrNull(offsets[5]);
  object.uploadResponse = reader.readObjectOrNull<UploadResponseEntity>(
    offsets[10],
    UploadResponseEntitySchema.deserialize,
    allOffsets,
  );
  return object;
}

P _workPlaceConversationAttachmentsEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (_WorkPlaceConversationAttachmentsEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.attachment) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readString(offset)) as P;
    case 4:
      return (reader.readObjectOrNull<AttachmentImageDataEntity>(
        offset,
        AttachmentImageDataEntitySchema.deserialize,
        allOffsets,
      )) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readLongOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readObjectOrNull<UploadResponseEntity>(
        offset,
        UploadResponseEntitySchema.deserialize,
        allOffsets,
      )) as P;
    case 11:
      return (reader.readObjectOrNull<AttachmentVideoDataEntity>(
        offset,
        AttachmentVideoDataEntitySchema.deserialize,
        allOffsets,
      )) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceConversationAttachmentsEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _WorkPlaceConversationAttachmentsEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};

Id _workPlaceConversationAttachmentsEntityGetId(
    WorkPlaceConversationAttachmentsEntity object) {
  return object.dbId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _workPlaceConversationAttachmentsEntityGetLinks(
    WorkPlaceConversationAttachmentsEntity object) {
  return [];
}

void _workPlaceConversationAttachmentsEntityAttach(IsarCollection<dynamic> col,
    Id id, WorkPlaceConversationAttachmentsEntity object) {}

extension WorkPlaceConversationAttachmentsEntityQueryWhereSort on QueryBuilder<
    WorkPlaceConversationAttachmentsEntity,
    WorkPlaceConversationAttachmentsEntity,
    QWhere> {
  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterWhere> anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WorkPlaceConversationAttachmentsEntityQueryWhere on QueryBuilder<
    WorkPlaceConversationAttachmentsEntity,
    WorkPlaceConversationAttachmentsEntity,
    QWhereClause> {
  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterWhereClause> dbIdEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterWhereClause> dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterWhereClause> dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterWhereClause> dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterWhereClause> dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceConversationAttachmentsEntityQueryFilter on QueryBuilder<
    WorkPlaceConversationAttachmentsEntity,
    WorkPlaceConversationAttachmentsEntity,
    QFilterCondition> {
  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> dbIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> dbIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> dbIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> dbIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> dbIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> dbIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'fileUrl',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'fileUrl',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fileUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fileUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fileUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fileUrl',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'fileUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'fileUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      fileUrlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'fileUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      fileUrlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'fileUrl',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fileUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> fileUrlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'fileUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpLink',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpLink',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpLink',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      gpLinkContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      gpLinkMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'gpLink',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpLink',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> gpLinkIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'gpLink',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition> idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition> idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition> idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> imageDataIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'imageData',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> imageDataIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'imageData',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'localFilePath',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'localFilePath',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localFilePath',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      localFilePathContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      localFilePathMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'localFilePath',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localFilePath',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> localFilePathIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'localFilePath',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'mimeType',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'mimeType',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'mimeType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'mimeType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'mimeType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'mimeType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'mimeType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'mimeType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      mimeTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'mimeType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      mimeTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'mimeType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'mimeType',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> mimeTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'mimeType',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> sizeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'size',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> sizeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'size',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> sizeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'size',
        value: value,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> sizeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'size',
        value: value,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> sizeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'size',
        value: value,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> sizeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'size',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> srcIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'src',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> srcIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'src',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition> srcEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> srcGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> srcLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition> srcBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'src',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> srcStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> srcEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      srcContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      srcMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'src',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> srcIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'src',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> srcIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'src',
        value: '',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> uploadResponseIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'uploadResponse',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> uploadResponseIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'uploadResponse',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> videoDataIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'videoData',
      ));
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterFilterCondition> videoDataIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'videoData',
      ));
    });
  }
}

extension WorkPlaceConversationAttachmentsEntityQueryObject on QueryBuilder<
    WorkPlaceConversationAttachmentsEntity,
    WorkPlaceConversationAttachmentsEntity,
    QFilterCondition> {
  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      imageData(FilterQuery<AttachmentImageDataEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'imageData');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      uploadResponse(FilterQuery<UploadResponseEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'uploadResponse');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
          WorkPlaceConversationAttachmentsEntity, QAfterFilterCondition>
      videoData(FilterQuery<AttachmentVideoDataEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'videoData');
    });
  }
}

extension WorkPlaceConversationAttachmentsEntityQueryLinks on QueryBuilder<
    WorkPlaceConversationAttachmentsEntity,
    WorkPlaceConversationAttachmentsEntity,
    QFilterCondition> {}

extension WorkPlaceConversationAttachmentsEntityQuerySortBy on QueryBuilder<
    WorkPlaceConversationAttachmentsEntity,
    WorkPlaceConversationAttachmentsEntity,
    QSortBy> {
  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortByFileUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fileUrl', Sort.asc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> sortByFileUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fileUrl', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortByGpLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortByGpLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> sortByLocalFilePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.asc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> sortByLocalFilePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortByMimeType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'mimeType', Sort.asc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> sortByMimeTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'mimeType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortBySize() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'size', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortBySizeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'size', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortBySrc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'src', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> sortBySrcDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'src', Sort.desc);
    });
  }
}

extension WorkPlaceConversationAttachmentsEntityQuerySortThenBy on QueryBuilder<
    WorkPlaceConversationAttachmentsEntity,
    WorkPlaceConversationAttachmentsEntity,
    QSortThenBy> {
  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByFileUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fileUrl', Sort.asc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> thenByFileUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fileUrl', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByGpLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByGpLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> thenByLocalFilePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.asc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> thenByLocalFilePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByMimeType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'mimeType', Sort.asc);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QAfterSortBy> thenByMimeTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'mimeType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenBySize() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'size', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenBySizeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'size', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenBySrc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'src', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QAfterSortBy> thenBySrcDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'src', Sort.desc);
    });
  }
}

extension WorkPlaceConversationAttachmentsEntityQueryWhereDistinct
    on QueryBuilder<WorkPlaceConversationAttachmentsEntity,
        WorkPlaceConversationAttachmentsEntity, QDistinct> {
  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QDistinct> distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QDistinct> distinctByFileUrl({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fileUrl', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QDistinct> distinctByGpLink({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'gpLink', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QDistinct> distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QDistinct> distinctByLocalFilePath({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localFilePath',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QDistinct> distinctByMimeType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'mimeType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QDistinct> distinctByName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity, QDistinct> distinctBySize() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'size');
    });
  }

  QueryBuilder<
      WorkPlaceConversationAttachmentsEntity,
      WorkPlaceConversationAttachmentsEntity,
      QDistinct> distinctBySrc({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'src', caseSensitive: caseSensitive);
    });
  }
}

extension WorkPlaceConversationAttachmentsEntityQueryProperty on QueryBuilder<
    WorkPlaceConversationAttachmentsEntity,
    WorkPlaceConversationAttachmentsEntity,
    QQueryProperty> {
  QueryBuilder<WorkPlaceConversationAttachmentsEntity, int, QQueryOperations>
      dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, GPBaseCrawlType,
      QQueryOperations> crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, String?,
      QQueryOperations> fileUrlProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fileUrl');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, String?,
      QQueryOperations> gpLinkProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'gpLink');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, String, QQueryOperations>
      idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      AttachmentImageDataEntity?, QQueryOperations> imageDataProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'imageData');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, String?,
      QQueryOperations> localFilePathProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localFilePath');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, String?,
      QQueryOperations> mimeTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'mimeType');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, String?,
      QQueryOperations> nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, int?, QQueryOperations>
      sizeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'size');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, String?,
      QQueryOperations> srcProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'src');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity, UploadResponseEntity?,
      QQueryOperations> uploadResponseProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'uploadResponse');
    });
  }

  QueryBuilder<WorkPlaceConversationAttachmentsEntity,
      AttachmentVideoDataEntity?, QQueryOperations> videoDataProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'videoData');
    });
  }
}

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const AttachmentImageDataEntitySchema = Schema(
  name: r'AttachmentImageDataEntity',
  id: -2072872597085803821,
  properties: {
    r'height': PropertySchema(
      id: 0,
      name: r'height',
      type: IsarType.long,
    ),
    r'imageType': PropertySchema(
      id: 1,
      name: r'imageType',
      type: IsarType.long,
    ),
    r'url': PropertySchema(
      id: 2,
      name: r'url',
      type: IsarType.string,
    ),
    r'width': PropertySchema(
      id: 3,
      name: r'width',
      type: IsarType.long,
    )
  },
  estimateSize: _attachmentImageDataEntityEstimateSize,
  serialize: _attachmentImageDataEntitySerialize,
  deserialize: _attachmentImageDataEntityDeserialize,
  deserializeProp: _attachmentImageDataEntityDeserializeProp,
);

int _attachmentImageDataEntityEstimateSize(
  AttachmentImageDataEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.url;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _attachmentImageDataEntitySerialize(
  AttachmentImageDataEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.height);
  writer.writeLong(offsets[1], object.imageType);
  writer.writeString(offsets[2], object.url);
  writer.writeLong(offsets[3], object.width);
}

AttachmentImageDataEntity _attachmentImageDataEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AttachmentImageDataEntity(
    height: reader.readLongOrNull(offsets[0]),
    imageType: reader.readLongOrNull(offsets[1]),
    url: reader.readStringOrNull(offsets[2]),
    width: reader.readLongOrNull(offsets[3]),
  );
  return object;
}

P _attachmentImageDataEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension AttachmentImageDataEntityQueryFilter on QueryBuilder<
    AttachmentImageDataEntity, AttachmentImageDataEntity, QFilterCondition> {
  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> heightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> heightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> heightEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'height',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> heightGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'height',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> heightLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'height',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> heightBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'height',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> imageTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'imageType',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> imageTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'imageType',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> imageTypeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'imageType',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> imageTypeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'imageType',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> imageTypeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'imageType',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> imageTypeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'imageType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'url',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
          QAfterFilterCondition>
      urlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
          QAfterFilterCondition>
      urlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'url',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> urlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'url',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> widthIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> widthIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> widthEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'width',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> widthGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'width',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> widthLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'width',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentImageDataEntity, AttachmentImageDataEntity,
      QAfterFilterCondition> widthBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'width',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AttachmentImageDataEntityQueryObject on QueryBuilder<
    AttachmentImageDataEntity, AttachmentImageDataEntity, QFilterCondition> {}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const AttachmentVideoDataEntitySchema = Schema(
  name: r'AttachmentVideoDataEntity',
  id: 548988813931820262,
  properties: {
    r'height': PropertySchema(
      id: 0,
      name: r'height',
      type: IsarType.long,
    ),
    r'length': PropertySchema(
      id: 1,
      name: r'length',
      type: IsarType.long,
    ),
    r'url': PropertySchema(
      id: 2,
      name: r'url',
      type: IsarType.string,
    ),
    r'videoType': PropertySchema(
      id: 3,
      name: r'videoType',
      type: IsarType.long,
    ),
    r'width': PropertySchema(
      id: 4,
      name: r'width',
      type: IsarType.long,
    )
  },
  estimateSize: _attachmentVideoDataEntityEstimateSize,
  serialize: _attachmentVideoDataEntitySerialize,
  deserialize: _attachmentVideoDataEntityDeserialize,
  deserializeProp: _attachmentVideoDataEntityDeserializeProp,
);

int _attachmentVideoDataEntityEstimateSize(
  AttachmentVideoDataEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.url;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _attachmentVideoDataEntitySerialize(
  AttachmentVideoDataEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.height);
  writer.writeLong(offsets[1], object.length);
  writer.writeString(offsets[2], object.url);
  writer.writeLong(offsets[3], object.videoType);
  writer.writeLong(offsets[4], object.width);
}

AttachmentVideoDataEntity _attachmentVideoDataEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AttachmentVideoDataEntity(
    height: reader.readLongOrNull(offsets[0]),
    length: reader.readLongOrNull(offsets[1]),
    url: reader.readStringOrNull(offsets[2]),
    videoType: reader.readLongOrNull(offsets[3]),
    width: reader.readLongOrNull(offsets[4]),
  );
  return object;
}

P _attachmentVideoDataEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readLongOrNull(offset)) as P;
    case 4:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension AttachmentVideoDataEntityQueryFilter on QueryBuilder<
    AttachmentVideoDataEntity, AttachmentVideoDataEntity, QFilterCondition> {
  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> heightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> heightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> heightEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'height',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> heightGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'height',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> heightLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'height',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> heightBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'height',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> lengthIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'length',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> lengthIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'length',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> lengthEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'length',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> lengthGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'length',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> lengthLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'length',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> lengthBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'length',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'url',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
          QAfterFilterCondition>
      urlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
          QAfterFilterCondition>
      urlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'url',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> urlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'url',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> videoTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'videoType',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> videoTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'videoType',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> videoTypeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'videoType',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> videoTypeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'videoType',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> videoTypeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'videoType',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> videoTypeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'videoType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> widthIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> widthIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> widthEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'width',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> widthGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'width',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> widthLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'width',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentVideoDataEntity, AttachmentVideoDataEntity,
      QAfterFilterCondition> widthBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'width',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AttachmentVideoDataEntityQueryObject on QueryBuilder<
    AttachmentVideoDataEntity, AttachmentVideoDataEntity, QFilterCondition> {}
