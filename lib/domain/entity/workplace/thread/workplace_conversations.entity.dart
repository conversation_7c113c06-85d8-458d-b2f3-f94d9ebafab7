/*
 * Created Date: Friday, 21st June 2024, 09:59:32
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Sunday, 15th September 2024 14:44:53
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/helpers.dart';
import 'package:isar/isar.dart';

part 'workplace_conversations.entity.g.dart';

@Collection()
class WorkPlaceConversationEntity extends BaseCrawlEntity {
  WorkPlaceConversationEntity({
    required super.id,
    super.crawlType = GPBaseCrawlType.thread,
    this.name,
    List<WorkPlaceMessagesEntity>? messages,
    List<WorkPlaceCommunityMemberEntity>? participants,
    this.link,
    this.insertedAt,
  }) {
    if (messages != null && messages.isNotEmpty) {
      this.messages.addAll(messages);
    }

    if (participants != null && participants.isNotEmpty) {
      this.participants.addAll(participants);
    }
  }

  final messages = IsarLinks<WorkPlaceMessagesEntity>();
  final participants = IsarLinks<WorkPlaceCommunityMemberEntity>();

  final String? name;
  final String? link;

  String? wpUserId;
  String? threadId;
  String? gpThreadId;
  int? gpUserId;
  DateTime? insertedAt;

  late final Id? dbId = id.hashCode;

  WorkPlaceConversationEntity copyWith({
    String? name,
    List<WorkPlaceMessagesEntity>? messages,
    List<WorkPlaceCommunityMemberEntity>? participants,
    String? link,
  }) {
    return WorkPlaceConversationEntity(
      id: id,
      crawlType: crawlType,
      name: name ?? this.name,
      messages: messages,
      participants: participants,
      link: link ?? this.link,
    );
  }

  @ignore
  WorkPlaceThreadType get type {
    final linkThreadId =
        't_${StringHelper.getConversationIdFromLink(link ?? '')}';
    if (linkThreadId != id) {
      return WorkPlaceThreadType.direct;
    }
    return WorkPlaceThreadType.group;
  }

  @override
  bool operator ==(Object other) {
    if (other is WorkPlaceConversationEntity) {
      return id == other.id;
    }
    return false;
  }

  @override
  int get hashCode => id.hashCode;
}

@Collection()
class WorkPlaceMessagesEntity extends BaseCrawlEntity {
  WorkPlaceMessagesEntity({
    required super.id,
    super.crawlType = GPBaseCrawlType.message,
    this.message,
    WorkPlaceCommunityMemberEntity? from,
    List<WorkPlaceCommunityMemberEntity>? to,
    List<WorkPlaceConversationAttachmentsEntity>? attachments,
    this.createdTime,
    WorkPlaceStickerEntity? sticker,
    this.wpThreadId,
  }) {
    if (from != null) {
      this.from.value = from;
    }

    if (to != null && to.isNotEmpty) {
      this.to.addAll(to);
    }

    if (attachments != null && attachments.isNotEmpty) {
      this.attachments.addAll(attachments);
    }

    if (sticker != null) {
      this.sticker.value = sticker;
    }
  }

  final String? message;

  final from = IsarLink<WorkPlaceCommunityMemberEntity>();
  final to = IsarLinks<WorkPlaceCommunityMemberEntity>();
  final attachments = IsarLinks<WorkPlaceConversationAttachmentsEntity>();

  final DateTime? createdTime;
  final sticker = IsarLink<WorkPlaceStickerEntity>();

  late final Id? dbId = id.hashCode;

  @Backlink(to: 'messages')
  final conversation = IsarLink<WorkPlaceConversationEntity>();

  String? wpThreadId;

  @ignore
  GPMessageType get messageType {
    if (sticker.value != null) {
      return GPMessageType.image;
    }
    if (attachments.isEmpty) return GPMessageType.text;

    if (attachments.length > 1) {
      // chỉ có image mới gộp lại 1 tin nhiều attachment
      return GPMessageType.multiImage;
    } else {
      final file = attachments.first;
      final mimeType = attachments.first.mimeType ?? '';
      final isImage = FileExtensions.kPhotoFileExtensions
          .contains(file.localFilePath?.split(".").last);
      final isVideo = FileExtensions.kVideoFileExtensions
          .contains(file.localFilePath?.split(".").last);
      return mimeType.contains('image') || isImage
          ? GPMessageType.image
          : mimeType.contains('video') || isVideo
              ? GPMessageType.video
              : GPMessageType.file;
    }
  }

  WorkPlaceMessagesEntity copyWith({
    String? message,
    WorkPlaceCommunityMemberEntity? from,
    List<WorkPlaceCommunityMemberEntity>? to,
    List<WorkPlaceConversationAttachmentsEntity>? attachments,
    DateTime? createdTime,
    WorkPlaceStickerEntity? sticker,
  }) {
    final id = '${this.id}${StringHelper.generateRandomString(5)}';
    return WorkPlaceMessagesEntity(
      id: id,
      message: message ?? this.message,
      from: from ?? this.from.value,
      to: to ?? this.to.toList(),
      attachments: attachments ?? this.attachments.toList(),
      createdTime: createdTime ?? this.createdTime,
      sticker: sticker ?? this.sticker.value,
      wpThreadId: wpThreadId,
    );
  }

  WorkPlaceMessagesEntity copyMergeDb({
    String? message,
    WorkPlaceCommunityMemberEntity? from,
    List<WorkPlaceCommunityMemberEntity>? to,
    List<WorkPlaceConversationAttachmentsEntity>? attachments,
    DateTime? createdTime,
    WorkPlaceStickerEntity? sticker,
  }) {
    return WorkPlaceMessagesEntity(
      id: id,
      message: message ?? this.message,
      from: from,
      to: to,
      attachments: attachments,
      createdTime: createdTime ?? this.createdTime,
      sticker: sticker,
      wpThreadId: wpThreadId,
    );
  }
}
