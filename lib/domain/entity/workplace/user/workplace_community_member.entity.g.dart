// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_community_member.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWorkPlaceCommunityMemberEntityCollection on Isar {
  IsarCollection<WorkPlaceCommunityMemberEntity>
      get workPlaceCommunityMemberEntitys => this.collection();
}

const WorkPlaceCommunityMemberEntitySchema = CollectionSchema(
  name: r'WorkPlaceCommunityMemberEntity',
  id: 9039766161614792583,
  properties: {
    r'active': PropertySchema(
      id: 0,
      name: r'active',
      type: IsarType.bool,
    ),
    r'administrator': PropertySchema(
      id: 1,
      name: r'administrator',
      type: IsarType.bool,
    ),
    r'cover': PropertySchema(
      id: 2,
      name: r'cover',
      type: IsarType.string,
    ),
    r'crawlType': PropertySchema(
      id: 3,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _WorkPlaceCommunityMemberEntitycrawlTypeEnumValueMap,
    ),
    r'department': PropertySchema(
      id: 4,
      name: r'department',
      type: IsarType.string,
    ),
    r'division': PropertySchema(
      id: 5,
      name: r'division',
      type: IsarType.string,
    ),
    r'email': PropertySchema(
      id: 6,
      name: r'email',
      type: IsarType.string,
    ),
    r'gpUserId': PropertySchema(
      id: 7,
      name: r'gpUserId',
      type: IsarType.long,
    ),
    r'id': PropertySchema(
      id: 8,
      name: r'id',
      type: IsarType.string,
    ),
    r'insertedAt': PropertySchema(
      id: 9,
      name: r'insertedAt',
      type: IsarType.dateTime,
    ),
    r'name': PropertySchema(
      id: 10,
      name: r'name',
      type: IsarType.string,
    ),
    r'organization': PropertySchema(
      id: 11,
      name: r'organization',
      type: IsarType.string,
    ),
    r'picture': PropertySchema(
      id: 12,
      name: r'picture',
      type: IsarType.string,
    ),
    r'primaryPhone': PropertySchema(
      id: 13,
      name: r'primaryPhone',
      type: IsarType.string,
    )
  },
  estimateSize: _workPlaceCommunityMemberEntityEstimateSize,
  serialize: _workPlaceCommunityMemberEntitySerialize,
  deserialize: _workPlaceCommunityMemberEntityDeserialize,
  deserializeProp: _workPlaceCommunityMemberEntityDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _workPlaceCommunityMemberEntityGetId,
  getLinks: _workPlaceCommunityMemberEntityGetLinks,
  attach: _workPlaceCommunityMemberEntityAttach,
  version: '3.1.0+1',
);

int _workPlaceCommunityMemberEntityEstimateSize(
  WorkPlaceCommunityMemberEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.cover;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.department;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.division;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.email;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.id.length * 3;
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.organization;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.picture;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.primaryPhone;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _workPlaceCommunityMemberEntitySerialize(
  WorkPlaceCommunityMemberEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeBool(offsets[0], object.active);
  writer.writeBool(offsets[1], object.administrator);
  writer.writeString(offsets[2], object.cover);
  writer.writeByte(offsets[3], object.crawlType.index);
  writer.writeString(offsets[4], object.department);
  writer.writeString(offsets[5], object.division);
  writer.writeString(offsets[6], object.email);
  writer.writeLong(offsets[7], object.gpUserId);
  writer.writeString(offsets[8], object.id);
  writer.writeDateTime(offsets[9], object.insertedAt);
  writer.writeString(offsets[10], object.name);
  writer.writeString(offsets[11], object.organization);
  writer.writeString(offsets[12], object.picture);
  writer.writeString(offsets[13], object.primaryPhone);
}

WorkPlaceCommunityMemberEntity _workPlaceCommunityMemberEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceCommunityMemberEntity(
    active: reader.readBoolOrNull(offsets[0]),
    administrator: reader.readBoolOrNull(offsets[1]),
    cover: reader.readStringOrNull(offsets[2]),
    crawlType: _WorkPlaceCommunityMemberEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[3])] ??
        GPBaseCrawlType.user,
    department: reader.readStringOrNull(offsets[4]),
    division: reader.readStringOrNull(offsets[5]),
    email: reader.readStringOrNull(offsets[6]),
    gpUserId: reader.readLongOrNull(offsets[7]),
    id: reader.readString(offsets[8]),
    insertedAt: reader.readDateTimeOrNull(offsets[9]),
    name: reader.readStringOrNull(offsets[10]),
    organization: reader.readStringOrNull(offsets[11]),
    picture: reader.readStringOrNull(offsets[12]),
    primaryPhone: reader.readStringOrNull(offsets[13]),
  );
  return object;
}

P _workPlaceCommunityMemberEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readBoolOrNull(offset)) as P;
    case 1:
      return (reader.readBoolOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (_WorkPlaceCommunityMemberEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.user) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readLongOrNull(offset)) as P;
    case 8:
      return (reader.readString(offset)) as P;
    case 9:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    case 13:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceCommunityMemberEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _WorkPlaceCommunityMemberEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};

Id _workPlaceCommunityMemberEntityGetId(WorkPlaceCommunityMemberEntity object) {
  return object.dbId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _workPlaceCommunityMemberEntityGetLinks(
    WorkPlaceCommunityMemberEntity object) {
  return [];
}

void _workPlaceCommunityMemberEntityAttach(IsarCollection<dynamic> col, Id id,
    WorkPlaceCommunityMemberEntity object) {}

extension WorkPlaceCommunityMemberEntityQueryWhereSort on QueryBuilder<
    WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity, QWhere> {
  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterWhere> anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WorkPlaceCommunityMemberEntityQueryWhere on QueryBuilder<
    WorkPlaceCommunityMemberEntity,
    WorkPlaceCommunityMemberEntity,
    QWhereClause> {
  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterWhereClause> dbIdEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterWhereClause> dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterWhereClause> dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterWhereClause> dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterWhereClause> dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceCommunityMemberEntityQueryFilter on QueryBuilder<
    WorkPlaceCommunityMemberEntity,
    WorkPlaceCommunityMemberEntity,
    QFilterCondition> {
  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> activeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'active',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> activeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'active',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> activeEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'active',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> administratorIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'administrator',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> administratorIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'administrator',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> administratorEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'administrator',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cover',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cover',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cover',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cover',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cover',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cover',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cover',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cover',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      coverContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cover',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      coverMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cover',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cover',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> coverIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cover',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> dbIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> dbIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> dbIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> dbIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> dbIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> dbIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'department',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'department',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'department',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'department',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'department',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'department',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'department',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'department',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      departmentContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'department',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      departmentMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'department',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'department',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> departmentIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'department',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'division',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'division',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'division',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'division',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'division',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'division',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'division',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'division',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      divisionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'division',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      divisionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'division',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'division',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> divisionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'division',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'email',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'email',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'email',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      emailContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      emailMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'email',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'email',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> emailIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'email',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> gpUserIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> gpUserIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> gpUserIdEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> gpUserIdGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> gpUserIdLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> gpUserIdBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpUserId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> insertedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'insertedAt',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> insertedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'insertedAt',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> insertedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> insertedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> insertedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> insertedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'insertedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'organization',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'organization',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'organization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'organization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'organization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'organization',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'organization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'organization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      organizationContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'organization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      organizationMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'organization',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'organization',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> organizationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'organization',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'picture',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'picture',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'picture',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'picture',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'picture',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'picture',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'picture',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'picture',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      pictureContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'picture',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      pictureMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'picture',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'picture',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> pictureIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'picture',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'primaryPhone',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'primaryPhone',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'primaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'primaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'primaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'primaryPhone',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'primaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'primaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      primaryPhoneContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'primaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
          QAfterFilterCondition>
      primaryPhoneMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'primaryPhone',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'primaryPhone',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterFilterCondition> primaryPhoneIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'primaryPhone',
        value: '',
      ));
    });
  }
}

extension WorkPlaceCommunityMemberEntityQueryObject on QueryBuilder<
    WorkPlaceCommunityMemberEntity,
    WorkPlaceCommunityMemberEntity,
    QFilterCondition> {}

extension WorkPlaceCommunityMemberEntityQueryLinks on QueryBuilder<
    WorkPlaceCommunityMemberEntity,
    WorkPlaceCommunityMemberEntity,
    QFilterCondition> {}

extension WorkPlaceCommunityMemberEntityQuerySortBy on QueryBuilder<
    WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity, QSortBy> {
  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'active', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByActiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'active', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByAdministrator() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'administrator', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByAdministratorDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'administrator', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByCover() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cover', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByCoverDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cover', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByDepartment() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'department', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByDepartmentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'department', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByDivision() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'division', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByDivisionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'division', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByEmail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'email', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByEmailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'email', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByGpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByGpUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByInsertedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByOrganization() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'organization', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByOrganizationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'organization', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByPicture() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'picture', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByPictureDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'picture', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByPrimaryPhone() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'primaryPhone', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> sortByPrimaryPhoneDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'primaryPhone', Sort.desc);
    });
  }
}

extension WorkPlaceCommunityMemberEntityQuerySortThenBy on QueryBuilder<
    WorkPlaceCommunityMemberEntity,
    WorkPlaceCommunityMemberEntity,
    QSortThenBy> {
  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'active', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByActiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'active', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByAdministrator() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'administrator', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByAdministratorDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'administrator', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByCover() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cover', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByCoverDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cover', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByDepartment() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'department', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByDepartmentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'department', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByDivision() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'division', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByDivisionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'division', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByEmail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'email', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByEmailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'email', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByGpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByGpUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByInsertedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByOrganization() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'organization', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByOrganizationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'organization', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByPicture() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'picture', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByPictureDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'picture', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByPrimaryPhone() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'primaryPhone', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QAfterSortBy> thenByPrimaryPhoneDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'primaryPhone', Sort.desc);
    });
  }
}

extension WorkPlaceCommunityMemberEntityQueryWhereDistinct on QueryBuilder<
    WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity, QDistinct> {
  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'active');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByAdministrator() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'administrator');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByCover({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cover', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByDepartment({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'department', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByDivision({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'division', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByEmail({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'email', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByGpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'gpUserId');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'insertedAt');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByOrganization({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'organization', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByPicture({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'picture', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, WorkPlaceCommunityMemberEntity,
      QDistinct> distinctByPrimaryPhone({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'primaryPhone', caseSensitive: caseSensitive);
    });
  }
}

extension WorkPlaceCommunityMemberEntityQueryProperty on QueryBuilder<
    WorkPlaceCommunityMemberEntity,
    WorkPlaceCommunityMemberEntity,
    QQueryProperty> {
  QueryBuilder<WorkPlaceCommunityMemberEntity, int, QQueryOperations>
      dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, bool?, QQueryOperations>
      activeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'active');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, bool?, QQueryOperations>
      administratorProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'administrator');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, String?, QQueryOperations>
      coverProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cover');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, GPBaseCrawlType,
      QQueryOperations> crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, String?, QQueryOperations>
      departmentProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'department');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, String?, QQueryOperations>
      divisionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'division');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, String?, QQueryOperations>
      emailProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'email');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, int?, QQueryOperations>
      gpUserIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'gpUserId');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, String, QQueryOperations>
      idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, DateTime?, QQueryOperations>
      insertedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'insertedAt');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, String?, QQueryOperations>
      nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, String?, QQueryOperations>
      organizationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'organization');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, String?, QQueryOperations>
      pictureProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'picture');
    });
  }

  QueryBuilder<WorkPlaceCommunityMemberEntity, String?, QQueryOperations>
      primaryPhoneProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'primaryPhone');
    });
  }
}
