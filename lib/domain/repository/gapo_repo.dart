/*
 * Created Date: 2/01/2024 10:33:50
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 12th June 2024 09:14:39
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/base/base.dart';
import 'package:gp_fbwp_crawler/data/model/gpw/auth/request/auth_check_phone_request.dart';
import 'package:gp_fbwp_crawler/data/model/gpw/auth/response/auth_check_phone_response.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';

/// GapoWork repository
abstract class GapoRepository {
  Future<ApiResponseV2<AuthCheckMailResponse>> checkEmail({
    required AuthCheckEmailRequest checkEmailRequest,
  });

  Future<ApiResponseV2<AuthCheckPhoneResponse>> checkP<PERSON>({
    required AuthCheckPhoneRequest checkPhoneRequest,
  });

  Future<ApiResponseV2<AuthResponse>> login(AuthParams params);

  Future<UploadFileResponseModelWrapper> uploadFiles({
    required GPUploadRepoInput input,
  });

  Future<ApiResponseV2<AuthResponse>> signUp({
    required GPSignupParams input,
  });

  Future<ApiResponseV2WithoutData> setPassword({
    required GPSetPasswordParams params,
    required String token,
  });

  Future<ApiResponseV2<InviteWorkspaceResponse>> inviteEmailToWorkspace({
    required GPInviteWsParams params,
  });

  Future<ApiResponseV2<InviteWorkspaceResponse>> invitePhoneToWorkspace({
    required GPInviteWsParams params,
  });

  Future<ApiResponseV2WithoutData> updateUserProfile({
    required GPUserProfileParams params,
    required String userId,
    required String token,
  });

  Future<ApiResponseV2<GroupResponse>> createGroup({
    required GPGroupParams params,
  });

  Future<ApiResponseV2WithoutData> inviteToGroup({
    required GPGroupInviteParams params,
  });

  Future<ApiResponseV2WithoutData> adjustMemberRole({
    required GPGroupAdjustMemberRoleParams params,
    required String groupId,
  });

  Future<ApiResponseV2WithoutData> leaveGroup({
    required GPGroupLeaveParams params,
  });
}
