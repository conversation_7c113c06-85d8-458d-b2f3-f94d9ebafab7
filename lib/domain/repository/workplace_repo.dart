/*
 * Created Date: 2/01/2024 10:33:50
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 28th August 2024 16:17:24
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';

/// WorkPlace repository
abstract class WorkPlaceRepository {
  /// https://orange-shuttle-332055.postman.co/workspace/My-Workspace~df769fbd-6ac5-49c2-80df-937ec9aecc31/request/1127063-71dc753a-6fd2-4f91-99ef-a7cef3edfbc8
  Future<WorkPlaceListReponse<WorkPlaceGroupResponse>> groups(
    WorkPlaceBaseParams params,
  );
  
  /// curl --location 'https://graph.workplace.com/61560786735318?fields=id%2Cfirst_name%2Clast_name%2Cemail%2Ctitle%2Cdepartment%2Cemployee_number%2Cfrontline%2Cconversations' \
  ///--header 'Authorization: Bearer DQWRJRzY1S3dMNWpIdWpmNk9BZAFR6RWw4cHM5UjhpaHdqenlZAZAEdFWnVDUFFidDRtRk5hdDRCZATkxQTNZAM1B6blN4NEkzV01zZAkpLZAGxic2VCYmZAtSzVrT3VOallxNkhGZAHU5ZAnE0Q05jaW9ycTNqX2wwUFlXOXRIVjdMRUc0S0VVRFphdHdkbG5od3RpVFhzM3pKWmU2bWVPRXJjNFNRLVdoRkhHajlzaVpKMVRoak5USE1MRlRwM09MN0luclQ3TjZAERGVQNEY2ZAUdreXBTX0d3'
  Future<WorkPlaceGroupResponse> groupById(
    WorkPlaceBaseParams params,
  );

  Future<AppConfigEntity> getCurrentAppConfig();

  Future<String> getAppVersion();

  Future<WorkPlaceListReponse<WorkPlaceUser>> groupMembers(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<WorkPlaceUser>> communityMembers(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceUser> communityMemberById(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<WorkPlaceFeedsResponse>> groupFeeds(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<WorkPlaceCommentsResponse>> comments(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<WorkPlacePostAttachmentsResponse>> attachments(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<WorkPlaceUserConversationsResponse>>
      conversations(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<Messages>> messages(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<WorkPlaceFeedsResponse>> userFeeds(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<WorkPlaceReaction>> feedReactions(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<WorkPlaceUser>> feedSeen(
    WorkPlaceBaseParams params,
  );

  Future<WorkPlaceListReponse<WorkPlaceReaction>> commentReactions(
    WorkPlaceBaseParams params,
  );
}
