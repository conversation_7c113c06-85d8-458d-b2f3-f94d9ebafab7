import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class GPAdjustMemberRoleGroupUseCase extends GPBaseFutureUseCase<
    GPAdjustMemberRoleGroupInput, ApiResponseV2WithoutData> {
  const GPAdjustMemberRoleGroupUseCase(
    @Named('kGapoRepository') this._gpRepository,
  );

  final GapoRepository _gpRepository;

  @override
  Future<ApiResponseV2WithoutData> buildUseCase(
      GPAdjustMemberRoleGroupInput input) async {
    return _gpRepository.adjustMemberRole(
        params: input.params, groupId: input.groupId);
  }
}

class GPAdjustMemberRoleGroupInput extends GPBaseInput {
  const GPAdjustMemberRoleGroupInput({
    required this.params,
    required this.groupId,
  });

  final GPGroupAdjustMemberRoleParams params;
  final String groupId;
}
