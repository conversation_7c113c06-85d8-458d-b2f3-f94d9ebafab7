/*
 * Created Date: Tuesday, 11th June 2024, 11:13:29
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 10th September 2024 10:04:21
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class AuthCheckMailUseCase extends GPBaseFutureUseCase<AuthCheckEmailRequest,
    ApiResponseV2<AuthCheckMailResponse>> {
  const AuthCheckMailUseCase(
    @Named('kGapoRepository') this._authRepository,
  );

  final GapoRepository _authRepository;

  @override
  Future<ApiResponseV2<AuthCheckMailResponse>> buildUseCase(
      AuthCheckEmailRequest input) async {
    return await _authRepository.checkEmail(checkEmailRequest: input);
  }
}
