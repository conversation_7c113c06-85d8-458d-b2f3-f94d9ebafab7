import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class GPCreateGroupUseCase extends GPBaseFutureUseCase<GPCreateGroupInput,
    ApiResponseV2<GroupResponse>> {
  const GPCreateGroupUseCase(
    @Named('kGapoRepository') this._gpRepository,
  );

  final GapoRepository _gpRepository;

  @override
  Future<ApiResponseV2<GroupResponse>> buildUseCase(
      GPCreateGroupInput input) async {
    return _gpRepository.createGroup(params: input.params);
  }
}

class GPCreateGroupInput extends GPBaseInput {
  const GPCreateGroupInput({
    required this.params,
  });

  final GPGroupParams params;
}
