/*
 * Created Date: Friday, 14th June 2024, 10:10:02
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 24th June 2024 15:06:49
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';
import 'package:gp_core/core.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class GapoSignupUseCase
    extends GPBaseFutureUseCase<GPSignupInput, ApiResponseV2<AuthResponse>> {
  const GapoSignupUseCase(
    @Named('kGapoRepository') this._gapoRepository,
  );

  final GapoRepository _gapoRepository;

  @override
  Future<ApiResponseV2<AuthResponse>> buildUseCase(GPSignupInput input) async {
    return await retry(
      () async {
        return await _gapoRepository.signUp(input: input.params);
      },
      maxAttempts: 3,
      onRetry: input.onRetry,
      retryIf: (e) {
        if (e is DioException) {
          return e.response?.statusCode != 429;
        }

        return true;
      },
    );
  }
}

class GPSignupInput extends GPBaseInput {
  const GPSignupInput({
    required this.params,
    this.onRetry,
  });

  final GPSignupParams params;
  final Function(Exception e)? onRetry;
}
