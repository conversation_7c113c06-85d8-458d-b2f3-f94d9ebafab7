import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/list.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class GPInviteGroupUseCase
    extends GPBaseFutureUseCase<GPInviteGroupInput, ApiResponseV2WithoutData> {
  const GPInviteGroupUseCase(
    @Named('kGapoRepository') this._gpRepository,
  );

  final GapoRepository _gpRepository;

  @override
  Future<ApiResponseV2WithoutData> buildUseCase(
      GPInviteGroupInput input) async {
    final splitedListUsers = input.params.users.splitList(100);
    await Future.forEach(splitedListUsers, (users) async {
      await retry(() async {
        await Future.delayed(const Duration(seconds: 10));
        final params = GPGroupInviteParams(
          groupId: input.params.groupId,
          users: users,
        );
        await _gpRepository.inviteToGroup(params: params);
      }, maxAttempts: 3);
    });
    return ApiResponseV2WithoutData();
  }
}

class GPInviteGroupInput extends GPBaseInput {
  const GPInviteGroupInput({
    required this.params,
  });

  final GPGroupInviteParams params;
}
