import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class GPInviteEmailWorkspaceUseCase extends GPBaseFutureUseCase<
    GPInviteEmailWorkspaceInput, ApiResponseV2<InviteWorkspaceResponse>> {
  const GPInviteEmailWorkspaceUseCase(
    @Named('kGapoRepository') this._gpRepository,
  );

  final GapoRepository _gpRepository;

  @override
  Future<ApiResponseV2<InviteWorkspaceResponse>> buildUseCase(
      GPInviteEmailWorkspaceInput input) async {
    return retry(
      () async {
        return _gpRepository.inviteEmailToWorkspace(params: input.params);
      },
      maxAttempts: 1,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: GPInviteEmailWorkspaceUseCase error -> $e');
      },
    );
  }
}

class GPInviteEmailWorkspaceInput extends GPBaseInput {
  const GPInviteEmailWorkspaceInput({
    required this.params,
  });

  final GPInviteWsParams params;
}
