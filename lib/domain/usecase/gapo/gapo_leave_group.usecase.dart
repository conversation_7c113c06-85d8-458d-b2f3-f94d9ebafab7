import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class GPLeaveGroupUseCase
    extends GPBaseFutureUseCase<GPLeaveGroupInput, ApiResponseV2WithoutData> {
  const GPLeaveGroupUseCase(
    @Named('kGapoRepository') this._gpRepository,
  );

  final GapoRepository _gpRepository;

  @override
  Future<ApiResponseV2WithoutData> buildUseCase(
      GPLeaveGroupInput input) async {
    return _gpRepository.leaveGroup(params: input.params);
  }
}

class GPLeaveGroupInput extends GPBaseInput {
  const GPLeaveGroupInput({
    required this.params,
  });

  final GPGroupLeaveParams params;
}
