import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/gpw/auth/request/gp_user_profile_params.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class GPUserProfileUseCase
    extends GPBaseFutureUseCase<GPUserProfileInput, ApiResponseV2WithoutData> {
  const GPUserProfileUseCase(
    @Named('kGapoRepository') this._gpRepository,
  );

  final GapoRepository _gpRepository;

  @override
  Future<ApiResponseV2WithoutData> buildUseCase(
      GPUserProfileInput input) async {
    return retry(
      () async {
        return _gpRepository.updateUserProfile(
            params: input.params, userId: input.userId, token: input.token);
      },
      maxAttempts: 1,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: GPUserProfileUseCase error -> $e');
      },
    );
  }
}

class GPUserProfileInput extends GPBaseInput {
  const GPUserProfileInput({
    required this.params,
    required this.userId,
    required this.token,
  });

  final GPUserProfileParams params;
  final String userId;
  final String token;
}
