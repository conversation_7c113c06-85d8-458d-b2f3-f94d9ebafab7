import 'dart:convert';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';

mixin WorkPlaceFetchAllDataMixin {
  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');
  Future<WorkPlaceListReponse<T>> fetchAllData<T>({
    required WorkPlaceBaseParams params,
    int? limit,
    required Future<WorkPlaceListReponse<T>> Function(
      WorkPlaceBaseParams params,
    ) loadFunction,
    Future Function(List<T> data, Map<String, String>?)? saveData,
  }) async {
    var response = await loadFunction(params);
    final isDone =
        await saveData?.call(response.data, response.paging?.nextQueries);
    if (isDone == true) {
      return response;
    }

    while (response.paging?.next?.isNotEmpty == true) {
      if (limit != null && response.data.length >= limit) {
        break;
      }
      params.nextQueries = response.paging?.nextQueries;

      final nextResponse = await loadFunction(params);
      final isDone =
          await saveData?.call(nextResponse.data, response.paging?.nextQueries);
      response = response.copyWith(
        paging: nextResponse.paging,
        data: [...response.data, ...nextResponse.data],
      );
      if (isDone == true) break;
    }

    return response;
  }
}
