/*
 * Created Date: Friday, 21st June 2024, 21:31:21
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 2nd September 2024 23:47:29
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceCommentReactionsUseCase extends GPBaseFutureUseCase<
        WorkPlaceCommentReactionsInput,
        WorkPlaceListReponse<WorkPlaceReaction>>
    with WorkPlaceFetchAllDataMixin {
  WorkPlaceCommentReactionsUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceReaction>> buildUseCase(
    WorkPlaceCommentReactionsInput input,
  ) async {
    return retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.commentId,
          fields: input.fields,
          limit: '6',
        );

        return await fetchAllData<WorkPlaceReaction>(
          params: params,
          loadFunction: _worplaceRepository.commentReactions,
        );
      },
      maxAttempts: 1,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceCommentReactionsUseCase error -> $e');
      },
    );
  }
}

class WorkPlaceCommentReactionsInput extends GPBaseInput {
  const WorkPlaceCommentReactionsInput({
    required this.commentId,
    this.fields = '',
  });

  final String commentId;
  final String? fields;
}
