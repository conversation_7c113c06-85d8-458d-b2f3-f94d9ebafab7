/*
 * Created Date: Friday, 21st June 2024, 21:31:21
 * Author: To<PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 9th September 2024 09:45:23
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceCommunityMembersUseCase extends GPBaseFutureUseCase<
    WorkPlaceCommunityMembersInput,
    WorkPlaceListReponse<WorkPlaceUser>> with WorkPlaceFetchAllDataMixin {
  WorkPlaceCommunityMembersUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceUser>> buildUseCase(
    WorkPlaceCommunityMembersInput input,
  ) async {
    return await retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.communityId,
          fields: input.fields,
          limit: '30'
        );

        return await fetchAllData<WorkPlaceUser>(
          params: params,
          loadFunction: _worplaceRepository.communityMembers,
          saveData: input.saveData,
        );
      },
      maxAttempts: 5,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceCommunityMembersUseCase error -> $e');
      },
    );
  }
}

class WorkPlaceCommunityMembersInput extends GPBaseInput {
  const WorkPlaceCommunityMembersInput({
    this.fields = 'email,name,primary_phone,department,division,picture,cover',
    required this.communityId,
    required this.saveData,
    this.memberIds,
  });

  final String fields;
  final String communityId;
  final Future Function(List<WorkPlaceUser> data, Map<String, String>?) saveData;
  final List<String>? memberIds;
}

class WorkPlaceMemberParams {
  const WorkPlaceMemberParams({
    this.fields,
    this.next,
    required this.communityId,
  });

  final String? fields;
  final String? next;
  final String communityId;
}
