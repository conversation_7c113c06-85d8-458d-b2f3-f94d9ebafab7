/*
 * Created Date: Friday, 21st June 2024, 21:31:21
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 28th August 2024 16:17:13
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceCommunityMemberByIdUseCase extends GPBaseFutureUseCase<
    WorkPlaceCommunityMembersInput,
    WorkPlaceListReponse<WorkPlaceUser>> with WorkPlaceFetchAllDataMixin {
  WorkPlaceCommunityMemberByIdUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceUser>> buildUseCase(
    WorkPlaceCommunityMembersInput input,
  ) async {
    return await retry(
      () async {
        final output = <WorkPlaceUser>[];

        await Future.forEach(input.memberIds ?? [], (id) async {
          final params = WorkPlaceBaseParams(
            id: id,
            fields: input.fields,
          );

          final response =
              await _worplaceRepository.communityMemberById(params);

          output.add(response);
        });

        return WorkPlaceListReponse(
          data: output,
        );
      },
      maxAttempts: 5,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceCommunityMembersUseCase error -> $e');
      },
    );
  }
}
