/*
 * Created Date: Sunday, 2nd June 2024, 16:26:36
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 28th August 2024 16:17:24
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceGetAllGroupsByIdUseCase extends GPBaseFutureUseCase<
        WorkPlaceGroupsInput, WorkPlaceListReponse<WorkPlaceGroupResponse>>
    with WorkPlaceFetchAllDataMixin {
  WorkPlaceGetAllGroupsByIdUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceGroupResponse>> buildUseCase(
    WorkPlaceGroupsInput input,
  ) async {
    return await retry(
      () async {
        final output = <WorkPlaceGroupResponse>[];

        await Future.forEach(input.groupIds ?? [], (id) async {
          final params = WorkPlaceBaseParams(
            id: id,
            fields: input.fields,
          );
          final response = await _worplaceRepository.groupById(params);

          output.add(response);
        });

        return WorkPlaceListReponse(
          data: output,
        );
      },
      maxAttempts: 3,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceGetAllGroupsUseCase error -> $e');
      },
    );
  }
}
