/*
 * Created Date: Sunday, 11th August 2024, 14:27:18
 * Author: To<PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 9th September 2024 16:17:38
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlacePostReactionsUseCase extends GPBaseFutureUseCase<
        WorkPlacePostReactionsInput,
        WorkPlaceListReponse<WorkPlaceReaction>>
    with WorkPlaceFetchAllDataMixin {
  WorkPlacePostReactionsUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceReaction>> buildUseCase(
    WorkPlacePostReactionsInput input,
  ) async {
    return retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.postId,
          fields: input.fields,
          limit: '30'
        );

        return await fetchAllData<WorkPlaceReaction>(
          params: params,
          loadFunction: _worplaceRepository.feedReactions,
        );
      },
      maxAttempts: 1,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlacePostReactionsUseCase error -> $e');
      },
    );
  }
}

class WorkPlacePostReactionsInput extends GPBaseInput {
  const WorkPlacePostReactionsInput({
    required this.postId,
    this.fields = '',
  });

  final String postId;
  final String? fields;
}
