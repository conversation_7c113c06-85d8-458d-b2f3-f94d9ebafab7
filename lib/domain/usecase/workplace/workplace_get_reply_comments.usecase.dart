/*
 * Created Date: Friday, 21st June 2024, 21:31:21
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 26th June 2024 10:29:22
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceReplyCommentsUseCase extends GPBaseFutureUseCase<
        WorkPlaceReplyCommentsInput,
        WorkPlaceListReponse<WorkPlaceCommentsResponse>>
    with WorkPlaceFetchAllDataMixin {
  WorkPlaceReplyCommentsUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceCommentsResponse>> buildUseCase(
    WorkPlaceReplyCommentsInput input,
  ) async {
    return retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.cmtId,
          fields: input.fields,
        );

        return await fetchAllData<WorkPlaceCommentsResponse>(
          params: params,
          loadFunction: _worplaceRepository.comments,
          saveData: input.saveData,
        );
      },
      maxAttempts: 1,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceReplyCommentsUseCase error -> $e');
      },
    );
  }
}

class WorkPlaceReplyCommentsInput extends GPBaseInput {
  const WorkPlaceReplyCommentsInput({
    required this.cmtId,
    this.fields =
        'attachment,message,source,attachments,comment_count,created_time,from,like_count,message_tags,object,parent,user_likes,tags',
    required this.saveData,
  });

  final String cmtId;
  final String? fields;
  final Future Function(List<WorkPlaceCommentsResponse>, Map<String, String>?)
      saveData;
}
