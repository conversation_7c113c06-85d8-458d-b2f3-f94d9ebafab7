/*
 * Created Date: Friday, 21st June 2024, 21:31:21
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 26th June 2024 10:25:42
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceUserFeedsUseCase extends GPBaseFutureUseCase<
        WorkPlaceUserFeedsInput, WorkPlaceListReponse<WorkPlaceFeedsResponse>>
    with WorkPlaceFetchAllDataMixin {
  WorkPlaceUserFeedsUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceFeedsResponse>> buildUseCase(
    WorkPlaceUserFeedsInput input,
  ) async {
    return retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.userId,
          fields: input.fields,
          since: input.since,
          nextQueries: input.nextQuery,
        );

        return await fetchAllData<WorkPlaceFeedsResponse>(
          params: params,
          loadFunction: _worplaceRepository.userFeeds,
          saveData: input.saveData,
        );
      },
      maxAttempts: 1,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceUserFeedsUseCase error -> $e');
      },
    );
  }
}

class WorkPlaceUserFeedsInput extends GPBaseInput {
  const WorkPlaceUserFeedsInput({
    required this.userId,
    this.fields =
        'message,updated_time,from,attachments,to,formatting,created_time',
    required this.saveData,
    this.since,
    this.nextQuery,
  });

  final String userId;
  final String? fields;
  final int? since;
  final Future Function(List<WorkPlaceFeedsResponse>, Map<String, String>?)
      saveData;
  final Map<String, String>? nextQuery;
}
