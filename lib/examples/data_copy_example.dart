import 'package:flutter/material.dart';
import '../helpers/data_copy_helper.dart';

/// V<PERSON> dụ về cách sử dụng DataCopyHelper để copy files từ thư mục data
/// đến thư mục gpdata trong Documents
class DataCopyExample extends StatefulWidget {
  const DataCopyExample({Key? key}) : super(key: key);

  @override
  State<DataCopyExample> createState() => _DataCopyExampleState();
}

class _DataCopyExampleState extends State<DataCopyExample> {
  String _status = 'Chưa thực hiện';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Copy Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Trạng thái:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(_status),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _copyAllData,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Copy tất cả files từ data đến gpdata'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _copySpecificFile,
              child: const Text('Copy file computer_ids.txt'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _checkGpDataExists,
              child: const Text('Kiểm tra thư mục gpdata có tồn tại không'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _getGpDataPath,
              child: const Text('Lấy đường dẫn thư mục gpdata'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _deleteGpData,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Xóa thư mục gpdata'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _copyAllData() async {
    setState(() {
      _isLoading = true;
      _status = 'Đang copy tất cả files...';
    });

    try {
      await DataCopyHelper.copyDataToDocuments();
      setState(() {
        _status = 'Copy thành công tất cả files từ data đến gpdata';
      });
    } catch (e) {
      setState(() {
        _status = 'Lỗi khi copy: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _copySpecificFile() async {
    setState(() {
      _isLoading = true;
      _status = 'Đang copy file computer_ids.txt...';
    });

    try {
      await DataCopyHelper.copySpecificFile('computer_ids.txt');
      setState(() {
        _status = 'Copy thành công file computer_ids.txt';
      });
    } catch (e) {
      setState(() {
        _status = 'Lỗi khi copy file: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkGpDataExists() async {
    setState(() {
      _isLoading = true;
      _status = 'Đang kiểm tra...';
    });

    try {
      final exists = await DataCopyHelper.isGpDataExists();
      setState(() {
        _status = exists 
            ? 'Thư mục gpdata đã tồn tại' 
            : 'Thư mục gpdata chưa tồn tại';
      });
    } catch (e) {
      setState(() {
        _status = 'Lỗi khi kiểm tra: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getGpDataPath() async {
    setState(() {
      _isLoading = true;
      _status = 'Đang lấy đường dẫn...';
    });

    try {
      final path = await DataCopyHelper.getGpDataPath();
      setState(() {
        _status = 'Đường dẫn gpdata: $path';
      });
    } catch (e) {
      setState(() {
        _status = 'Lỗi khi lấy đường dẫn: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteGpData() async {
    // Hiển thị dialog xác nhận
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: const Text('Bạn có chắc chắn muốn xóa thư mục gpdata và tất cả files bên trong?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
      _status = 'Đang xóa thư mục gpdata...';
    });

    try {
      await DataCopyHelper.deleteGpData();
      setState(() {
        _status = 'Đã xóa thành công thư mục gpdata';
      });
    } catch (e) {
      setState(() {
        _status = 'Lỗi khi xóa: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
