import 'dart:io';
import 'package:path_provider/path_provider.dart';

class DataCopyHelper {
  /// Copy files từ thư mục data trong project đến thư mục gpdata trong Documents
  /// Nếu file đã tồn tại thì sẽ bỏ qua
  static Future<void> copyDataToDocuments() async {
    try {
      // Lấy thư mục Documents của máy tính
      final documentsDirectory = await getApplicationDocumentsDirectory();

      // Tạo thư mục gpdata trong Documents
      final gpDataDirectory = Directory(
          '${documentsDirectory.path}${Platform.pathSeparator}gpdata');
      if (!await gpDataDirectory.exists()) {
        await gpDataDirectory.create(recursive: true);
        print('Đã tạo thư mục gpdata: ${gpDataDirectory.path}');
      }

      // Thư mục data trong project
      final projectDataDirectory = Directory('data');

      if (!await projectDataDirectory.exists()) {
        print('Thư mục data không tồn tại trong project');
        print('Đường dẫn hiện tại: ${Directory.current.path}');
        return;
      }

      // Debug: In ra nội dung thư mục data
      print('Thư mục data tồn tại tại: ${projectDataDirectory.path}');
      final entities = await projectDataDirectory.list().toList();
      print('Nội dung thư mục data:');
      for (final entity in entities) {
        if (entity is File) {
          print('  File: ${entity.path}');
        } else if (entity is Directory) {
          print('  Directory: ${entity.path}');
        }
      }

      // Copy tất cả files từ thư mục data
      await _copyDirectoryRecursively(projectDataDirectory, gpDataDirectory);

      print(
          'Đã copy thành công tất cả files từ data đến ${gpDataDirectory.path}');
    } catch (e) {
      print('Lỗi khi copy database: $e');
    }
  }

  /// Copy một thư mục và tất cả nội dung bên trong một cách đệ quy
  static Future<void> _copyDirectoryRecursively(
      Directory source, Directory destination) async {
    // Tạo thư mục đích nếu chưa tồn tại
    if (!await destination.exists()) {
      await destination.create(recursive: true);
    }

    // Lấy danh sách tất cả files và folders trong thư mục nguồn
    await for (final entity in source.list(recursive: false)) {
      if (entity is File) {
        // Copy file - sử dụng Platform.pathSeparator để xử lý đường dẫn chính xác
        final fileName = entity.uri.pathSegments.last;
        final destinationFilePath =
            '${destination.path}${Platform.pathSeparator}$fileName';
        final destinationFile = File(destinationFilePath);

        // Kiểm tra nếu file đã tồn tại thì bỏ qua
        if (await destinationFile.exists()) {
          print('File $fileName đã tồn tại, bỏ qua');
          continue;
        }

        await entity.copy(destinationFile.path);
        print(
            'Đã copy file: $fileName từ ${entity.path} đến ${destinationFile.path}');
      } else if (entity is Directory) {
        // Copy thư mục con - sử dụng Platform.pathSeparator
        final dirName = entity.uri.pathSegments.where((s) => s.isNotEmpty).last;
        final destinationDirPath =
            '${destination.path}${Platform.pathSeparator}$dirName';
        final destinationDir = Directory(destinationDirPath);

        await _copyDirectoryRecursively(entity, destinationDir);
      }
    }
  }

  /// Lấy đường dẫn đến thư mục gpdata trong Documents
  static Future<String> getGpDataPath() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    return '${documentsDirectory.path}${Platform.pathSeparator}gpdata';
  }

  /// Kiểm tra xem thư mục gpdata có tồn tại không
  static Future<bool> isGpDataExists() async {
    final gpDataPath = await getGpDataPath();
    return await Directory(gpDataPath).exists();
  }

  /// Xóa thư mục gpdata và tất cả nội dung bên trong
  static Future<void> deleteGpData() async {
    try {
      final gpDataPath = await getGpDataPath();
      final gpDataDirectory = Directory(gpDataPath);

      if (await gpDataDirectory.exists()) {
        await gpDataDirectory.delete(recursive: true);
        print('Đã xóa thư mục gpdata: $gpDataPath');
      } else {
        print('Thư mục gpdata không tồn tại');
      }
    } catch (e) {
      print('Lỗi khi xóa thư mục gpdata: $e');
    }
  }

  /// Copy một file cụ thể từ thư mục data đến gpdata
  static Future<void> copySpecificFile(String fileName) async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final gpDataDirectory = Directory(
          '${documentsDirectory.path}${Platform.pathSeparator}gpdata');

      // Tạo thư mục gpdata nếu chưa tồn tại
      if (!await gpDataDirectory.exists()) {
        await gpDataDirectory.create(recursive: true);
      }

      // File nguồn trong thư mục data
      final sourceFile = File('data${Platform.pathSeparator}$fileName');
      if (!await sourceFile.exists()) {
        print('File $fileName không tồn tại trong thư mục data');
        return;
      }

      // File đích trong thư mục gpdata
      final destinationFile =
          File('${gpDataDirectory.path}${Platform.pathSeparator}$fileName');

      // Kiểm tra nếu file đã tồn tại thì bỏ qua
      if (await destinationFile.exists()) {
        print('File $fileName đã tồn tại trong gpdata, bỏ qua');
        return;
      }

      await sourceFile.copy(destinationFile.path);
      print('Đã copy file $fileName đến gpdata');
    } catch (e) {
      print('Lỗi khi copy file $fileName: $e');
    }
  }
}
