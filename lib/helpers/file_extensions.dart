class FileExtensions {
  static const kAudioFileExtensions = [
    "mp3",
    "wma",
    "wav",
    "flac",
    "aac",
    "ogg",
    "aiff",
    "alac",
    "amr",
    "midi",
    "wma9",
    "aac+",
    "aac++",
    "eaac+",
    "ac3"
  ];

  static const kPhotoFileExtensions = [
    "jpg",
    "jpeg",
    "png",
    "bmp",
    "webp",
    "heic",
    "heif",
    "gif",
  ];

  static const kVideoFileExtensions = [
    "mov",
    "mp4",
    "mkv",
    "flv",
    "avi",
    "wmv",
    "vob",
    "divx",
    "h.265",
    "3gp",
    "h.263",
    "h.264",
    "wmv9",
    "xvid",
    "mp1",
    "mp2"
  ];

  static const kWordFileExtensions = [
    "doc",
    "docx",
    "docm",
    "docx",
    "dotm",
    "docb",
    "dot",
    "wbk"
  ];

  static const kExcelFileExtensions = [
    "xls",
    "xlsx",
    "xlt",
    "xlm",
    "xlsm",
    "xltm",
    "xlsb",
    "xla",
    "xlam",
    "xll",
    "xlw"
  ];

  static const kPowerPointFileExtensions = [
    "pptx",
    "ppt",
    "pot",
    "pps",
    "pptm",
    "potx",
    "potm",
    "ppam",
    "ppsx",
    "ppsm",
    "sldx",
    "sldm"
  ];

  static const kPdfFileExtensions = ["pdf"];

  static const kZipFileExtensions = ["zip", "rar"];
}
