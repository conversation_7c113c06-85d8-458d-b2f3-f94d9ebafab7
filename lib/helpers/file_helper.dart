import 'dart:developer';
import 'dart:io';

import 'package:gp_core/core.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/domain/entity/enums/enums.dart';
import 'package:gp_fbwp_crawler/helpers/helpers.dart';

class FileHelper {
  static Future<Directory> getDownloadDirectory() async {
    final documentDirectory = await getApplicationDocumentsDirectory();
    String path =
        '${documentDirectory.path}/${AppConstants.resourceFolderName}';

    return Directory(path);
  }

  static Future<String> downloadDirectoryPath(String fileName) async {
    final documentDirectory = await getApplicationDocumentsDirectory();
    String path =
        '${documentDirectory.path}/${AppConstants.resourceFolderName}';

    try {
      if (!File(path).existsSync()) {
        await Directory(path).create();
      }
    } catch (e) {
      log('e -> $e');
    }

    String filePath = '$path/$fileName';

    return filePath;
  }

  static String? getFileNameFromUrl(String url) {
    final regex = RegExp(r"[^\/]+\.*\?");
    final match = regex.firstMatch(url);

    if (match != null) {
      final result = match.group(0) ?? '';
      return result.substring(0, result.length - 1);
    } else {
      return null;
    }
  }

  static Future<String> getFilePathFromUrl(String url) async {
    String fileName;
    try {
      fileName = Uri.decodeFull(getFileNameFromUrl(url) ?? '');
    } catch (e) {
      fileName = getFileNameFromUrl(url) ?? '';
    }

    if (fileName.isNotEmpty) {
      return await downloadDirectoryPath(fileName);
    } else {
      return '';
    }
  }

  static GPApiUploadType apiUploadType(String fileName) {
    final isImage =
        FileExtensions.kPhotoFileExtensions.contains(fileName.split(".").last);
    final isVideo =
        FileExtensions.kVideoFileExtensions.contains(fileName.split(".").last);
    final isAudio =
        FileExtensions.kAudioFileExtensions.contains(fileName.split(".").last);

    return isVideo
        ? GPApiUploadType.video
        : isAudio
            ? GPApiUploadType.audio
            : isImage
                ? GPApiUploadType.image
                : GPApiUploadType.files;
  }

  static Future saveCSV(String filename, String csv) async {
    String? outputFile = await FilePicker.platform.saveFile(
      dialogTitle: l10n.download_csv_title,
      fileName: filename,
    );

    try {
      File returnedFile = File('$outputFile');
      await returnedFile.writeAsString(csv);
      log("File exported successfully!");
    } catch (e) {
      log("File exported failed!");
    }
  }
}

extension FileSizeExtensions on num {
  /// method returns a human readable string representing a file size
  /// size can be passed as number or as string
  /// the optional parameter 'round' specifies the number of numbers after comma/point (default is 2)
  /// the optional boolean parameter 'useBase1024' specifies if we should count in 1024's (true) or 1000's (false). e.g. 1KB = 1024B (default is true)
  String toHumanReadableFileSize({int round = 2, bool useBase1024 = true}) {
    const List<String> affixes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

    num divider = useBase1024 ? 1024 : 1000;

    num size = this;
    num runningDivider = divider;
    num runningPreviousDivider = 0;
    int affix = 0;

    while (size >= runningDivider && affix < affixes.length - 1) {
      runningPreviousDivider = runningDivider;
      runningDivider *= divider;
      affix++;
    }

    String result =
        (runningPreviousDivider == 0 ? size : size / runningPreviousDivider)
            .toStringAsFixed(round);

    //Check if the result ends with .00000 (depending on how many decimals) and remove it if found.
    if (result.endsWith("0" * round))
      result = result.substring(0, result.length - round - 1);

    return "$result ${affixes[affix]}";
  }
}
