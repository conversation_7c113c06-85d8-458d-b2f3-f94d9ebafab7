import 'dart:developer';

import 'package:async_task/async_task_extension.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';

final class GPUploadManagment with _UploadLazyVariables {
  GPUploadManagment({
    required this.input,
    required this.gpRepository,
    // required this.rxDashBoardProgress,
  }) {
    _uploadFileResponseModels = [];
    _uploadImageResponseModels = [];

    _dashboardCallback = input.uploadInput.dashBoardCallback;
    _itemCallback = input.uploadInput.itemCallback;

    _totalFiles = input.uploadInput.fileInputs.length;
    _totalUploadedFiles = 0;
    isParallelism = false; // input.isParallelism
  }

  final GPUploadUseCaseInput input;
  final GapoRepository gpRepository;
  // final BehaviorSubject<GPUploadProgressEntity> rxDashBoardProgress;

  final List<GPUploadFileInput> _totalUploadingFiles = [];

  // final List<Stream<GPUploadProgressEntity>> _streams =
  //     <Stream<GPUploadProgressEntity>>[];
  // StreamSubscription? _dashBoardSubScription;

  Future startUploadFiles() async {
    _totalUploadingFiles.addAll(input.uploadInput.fileInputs);

    _dashboardCallback?.onFileStartUpload?.call(Duration.zero, _totalFiles);

    await _uploadFiles();

    // if (!isParallelism) {
    //   _onDashBoardUploadCompleted();
    // }

    // close();

    return UploadFileResponseModelWrapper(
      uploadFileResponseModels: _uploadFileResponseModels,
      uploadImageResponseModels: _uploadImageResponseModels,
    );
  }

  Future _uploadFiles() async {
    // Future _nextUploadFiles(int currentIndex) async {
    //   if (isParallelism) {
    //     if (currentIndex >= _totalUploadingFiles.length / input.maxConnection) {
    //       _onDashBoardUploadCompleted();
    //     }

    //     if (_remainingUploadingFiles.isEmpty) {
    //       await _uploadFilesWithConnection(currentIndex: currentIndex++);
    //     }
    //   }
    // }

    final uploadInputs = _uploadInputs(input: input);

    // _dashBoardSubScription?.cancel();
    // _dashBoardSubScription = _initSubscription(
    //   _dashboardCallback,
    //   input.dashboardProgressThrottleTime,
    // );

    await Future.forEach(
      uploadInputs,
      (uploadInput) async {
        // if (isParallelism) {
        //   currentIndex++;
        //   unawaited(_uploadAFile(uploadInput)
        //       .then((value) => _nextUploadFiles(currentIndex)));
        // } else {
        await _uploadAFile(uploadInput);
        // _nextUploadFiles(currentIndex);
        // }
      },
    );
  }

  Future _uploadAFile(GPUploadRepoInput uploadInput) async {
    _itemCallback?.onAFileStartUpload?.call(Duration.zero, uploadInput.file);

    try {
      final response = await gpRepository.uploadFiles(input: uploadInput);

      _totalUploadedFiles++;

      _itemCallback?.onAFileUploaded?.call(
        _totalUploadedFiles,
        _totalFiles,
        response,
      );

      _dashboardCallback?.onAFileUploaded?.call(
        _totalUploadedFiles,
        _totalFiles,
        response,
      );

      _uploadFileResponseModels.addAll(response.uploadFileResponseModels);
      _uploadImageResponseModels.addAll(response.uploadImageResponseModels);
    } catch (ex) {
      log('uploadFiles error: $ex');
    }
  }

  // StreamSubscription _initSubscription(
  //   GPUploadDashBoardCallback? dashboardCallback,
  //   Duration throttleTimer,
  // ) {
  //   return CombineLatestStream(
  //     _streams,
  //     (values) {
  //       return GPUploadProgressEntity(
  //         progress:
  //             values.reduce((a, b) => a.add(b)).progress / _streams.length,
  //       );
  //     },
  //   ).throttleTime(throttleTimer).listen(
  //     (progressModel) {
  //       _updateDashBoardProgress(itemProgressEntity: progressModel);

  //       dashboardCallback?.onFileProgress?.call(progressModel);
  //     },
  //   );
  // }

  List<GPUploadRepoInput> _uploadInputs({
    required GPUploadUseCaseInput input,
  }) {
    final GPUploadCallback? itemCallback = input.uploadInput.itemCallback;

    final ret = _totalUploadingFiles.map(
      (e) {
        // _streams.add(e.rxProgress);

        return GPUploadRepoInput(
          file: e.file,
          uploadType: e.uploadType,
          cancelToken: input.uploadInput.cancelToken,
          sendProgress: (count, total) {
            if (total != -1) {
              double progress = count / total;
              if (progress > 1) {
                progress = 1;
              }

              final progressEntity = GPUploadProgressEntity(progress: progress);

              itemCallback?.onAFileUploadProgress?.call(progressEntity, e.file);

              // e.rxProgress.add(progressEntity);
            }

            // if (count == total) {
            //   Future.delayed(const Duration(seconds: 10)).then((value) {
            //     if (!e.rxProgress.isClosed) {
            //       e.rxProgress.close();
            //     }
            //   });
            // }
          },
          receiveProgress: (count, total) {
            // do nothing
          },
        );
      },
    ).toList();

    return ret;
  }

  // void _onDashBoardUploadCompleted() {
  //   _dashBoardSubScription?.cancel();

  //   _updateDashBoardProgress(isDoneAllUpload: true);

  //   _dashboardCallback?.onUploadProcessCompleted?.call();
  // }

  // void _updateDashBoardProgress({
  //   GPUploadProgressEntity? itemProgressEntity,
  //   bool isDoneAllUpload = false,
  // }) {
  //   if (rxDashBoardProgress.isClosed) return;

  //   if (isDoneAllUpload) {
  //     rxDashBoardProgress.add(GPUploadProgressEntity(progress: 1));

  //     return;
  //   }

  //   if (itemProgressEntity == null) return;

  //   rxDashBoardProgress.add(itemProgressEntity);
  // }

  // Future close() async {
  //   await Future.delayed(const Duration(seconds: 10));

  //   await Future.forEach(input.uploadInput.fileInputs, (element) async {
  //     await element.rxProgress.close();
  //   });

  //   await rxDashBoardProgress.close();
  //   await _dashBoardSubScription?.cancel();
  // }
}

mixin _UploadLazyVariables {
  late final List<GPUploadFileResponseModel> _uploadFileResponseModels;
  late final List<GPUploadImageResponseModel> _uploadImageResponseModels;

  late final GPUploadDashBoardCallback? _dashboardCallback;
  late final GPUploadCallback? _itemCallback;
  late final int _totalFiles;
  int _totalUploadedFiles = 0;

  late bool isParallelism;
}
