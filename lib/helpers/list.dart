import 'package:gp_fbwp_crawler/domain/entity/entity.dart';

extension ListExtension<T> on List<T> {
  List<List<T>> splitList(int maxLength) {
    final originalList = this;
    return List.generate(
      (originalList.length / maxLength).ceil(),
      (index) => originalList.skip(index * maxLength).take(maxLength).toList(),
    );
  }

  List<List<T>> splitToNumList(int numList) {
    final input = this;
    List<List<T>> output = [];

    int start = 0;
    final int end = input.length;

    if (numList > end) {
      numList = end;
    }

    final int itemPerList = ((end - start) / numList).ceil();

    int totalPages = (end / itemPerList).ceil();
    int maxLoops = numList;

    if (totalPages < numList) {
      maxLoops = totalPages;
    }

    for (var i = 1; i <= maxLoops; i++) {
      if (i != 1) {
        start = itemPerList * (i - 1);
      }

      int takeCount = itemPerList;
      if ((end - start) < itemPerList) {
        takeCount = end - start;
      }

      final newInputs = input.skip(start).take(takeCount).toList();
      output.add(newInputs);
    }
    return output;
  }
}

class ArrayPartition {
  static List<List<WorkPlaceGroupEntity>> groupPartitionByMember(
      List<WorkPlaceGroupEntity> input, int numPartitions) {
    if (numPartitions <= 0 || input.isEmpty) {
      return [];
    }

    // Tạo bản sao của mảng đầu vào để không làm thay đổi mảng gốc
    List<WorkPlaceGroupEntity> sortedInput =
        List<WorkPlaceGroupEntity>.from(input);
    // Sắp xếp giảm dần
    sortedInput.sort((a, b) => b.members.length.compareTo(a.members.length));

    // Khởi tạo mảng kết quả
    List<List<WorkPlaceGroupEntity>> result =
        List.generate(numPartitions, (index) => []);
    List<int> sums = List.filled(numPartitions, 0);

    // Phân phối vào các mảng con
    for (WorkPlaceGroupEntity group in sortedInput) {
      // Tìm mảng có tổng nhỏ nhất
      int minSumIndex = _findMinSumIndex(sums);

      // Thêm vào mảng có tổng nhỏ nhất
      result[minSumIndex].add(group);
      sums[minSumIndex] += group.members.length;
    }

    return result;
  }

  // Hàm helper để tìm index của mảng có tổng nhỏ nhất
  static int _findMinSumIndex(List<int> sums) {
    int minIndex = 0;
    for (int i = 1; i < sums.length; i++) {
      if (sums[i] < sums[minIndex]) {
        minIndex = i;
      }
    }
    return minIndex;
  }
}
