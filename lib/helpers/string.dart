import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

class StringHelper {
  static String? getConversationIdFromLink(String link) {
    final regex = RegExp(r"/messages/t/([^/]+)/");
    final match = regex.firstMatch(link);

    if (match != null) {
      return match.group(1);
    } else {
      return null;
    }
  }

  static String generateRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    Random random = Random();
    return String.fromCharCodes(Iterable.generate(
        length, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  static List<String> splitIntoShortStrings(String longString) {
    const maxLength = 3000;
    List<String> result = [];
    String currentString = '';

    for (int i = 0; i < longString.length; i++) {
      currentString += longString[i];

      if (currentString.length == maxLength || i == longString.length - 1) {
        result.add(currentString);
        currentString = '';
      }
    }

    return result;
  }
}

extension StringExtension on String {
  String get sha256Value {
    var bytes1 = utf8.encode(this);
    var digest1 = sha256.convert(bytes1);
    return digest1.toString();
  }

  String passwordSHA256(String? salt) {
    if (salt == null || salt.isEmpty) return this;

    final plaintext = this;
    final firstHash = (plaintext + salt).sha256Value;
    return (firstHash + salt).sha256Value.toLowerCase();
  }
}
