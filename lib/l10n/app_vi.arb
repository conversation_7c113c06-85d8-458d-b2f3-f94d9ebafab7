{"@@locale": "vi", "app_name": "GapoCrawler", "download_csv": "<PERSON>ải xuống CSV", "download_csv_title": "• Chọn vị trí lưu trữ file CSV", "handle_duration": "<PERSON>ử lý trong: ", "crawl_run_time_start_header": "<PERSON>h<PERSON><PERSON> điểm b<PERSON><PERSON> đầu", "crawl_run_time_diff": "Tool đã chạy trong", "crawl_go_to_details": "<PERSON>em chi tiết", "crawl_user_init_admin_account": "• Khởi tạo tài k<PERSON>n Admin ở GapoWork", "crawl_user_get_wp_users": "• <PERSON><PERSON><PERSON> thông tin người dùng ở WorkPlace", "crawl_user_save_csv": "• <PERSON><PERSON><PERSON> trữ thông tin người dùng vào file CSV", "total_crawl_files": "• Tống số dữ liệu cần crawl từ Facebook WorkPlace: {total} bản ghi", "@total_crawl_files": {"description": "• Tống số dữ liệu cần crawl từ Facebook WorkPlace", "placeholders": {"total": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_user_save_members": "• <PERSON><PERSON><PERSON> thông tin {count} ngư<PERSON>i dùng", "@crawl_user_save_members": {"description": "• <PERSON><PERSON><PERSON> thông tin XXX người dùng", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_user_signup_users": "• <PERSON><PERSON><PERSON> bộ thông tin {count} người dùng ở GapoWork", "@crawl_user_signup_users": {"description": "• Đồng bộ thông tin XXX người dùng ở GapoWork", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_user_invite_to_ws": "• Mời {count} người dùng vào workspace ở GapoWork", "@crawl_user_invite_to_ws": {"description": "• Mời XXX người dùng vào workspace ở GapoWork", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_user_invite_user_to_ws": "• Mời {name} vào workspace ở GapoWork", "@crawl_user_invite_user_to_ws": {"description": "• Mời XXX vào workspace ở GapoWork", "placeholders": {"name": {"type": "String"}}}, "crawl_user_update_gp_user_info": "• <PERSON><PERSON><PERSON> nhật thông tin {name} ở GapoWork", "@crawl_user_update_gp_user_info": {"description": "• Cậ<PERSON> nhật thông tin XXX ở GapoWork", "placeholders": {"name": {"type": "String"}}}, "crawl_group_get_groups": "• <PERSON><PERSON><PERSON> thông tin nhóm ở WorkPlace", "crawl_group_upload_cover": "• <PERSON><PERSON><PERSON> thông tin ảnh cover cho {count} ở WorkPlace", "@crawl_group_upload_cover": {"description": "• <PERSON><PERSON><PERSON> thông tin ảnh cover cho XXX ở WorkPlace", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_group_member_wp": "• <PERSON><PERSON><PERSON> thông tin thành viên nhóm ở WorkPlace", "crawl_group_member": "• <PERSON><PERSON><PERSON> thông tin thành viên nhóm {group_name}", "@crawl_group_member": {"description": "• <PERSON><PERSON><PERSON> thông tin thành viên nhóm ở WorkPlace", "placeholders": {"group_name": {"type": "String"}}}, "crawling_community": "Lấy thông tin Community ở WorkPlace", "crawl_community": "<PERSON><PERSON><PERSON> bộ dữ liệu từ {name} ở WorkPlace", "@crawl_community": {"description": "<PERSON><PERSON><PERSON> thông tin comunity ở WorkPlace", "placeholders": {"name": {"type": "String"}}}, "crawl_group_save_group": "• <PERSON><PERSON><PERSON> trữ dữ liệu nhóm vào bộ nhớ", "crawl_group_save_csv": "• <PERSON><PERSON><PERSON> trữ thông tin nhóm vào file CSV", "crawl_feed_from_a_groups": "• <PERSON><PERSON><PERSON> thông tin bài viết từ {count} nhóm ở WorkPlace", "@crawl_feed_from_a_groups": {"description": "• <PERSON><PERSON><PERSON> thông tin bài viết từ XXX nhóm ở WorkPlace", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_group_add_to_gp": "Thêm nhóm vào GapoWork", "crawl_group_to_gp": "• Thêm nhóm {group_name} vào GapoWork", "@crawl_group_to_gp": {"description": "• Thêm nhóm vào GapoWork", "placeholders": {"group_name": {"type": "String"}}}, "crawl_group_invite_to_gp": "Thêm thành viên vào nhóm ở GapoWork", "crawl_group_invite": "• Thêm thành viên vào nhóm {group_name} vào GapoWork", "@crawl_group_invite": {"description": "• Thêm thành viên vào nhóm GapoWork", "placeholders": {"group_name": {"type": "String"}}}, "crawl_group_change_owner": "<PERSON><PERSON><PERSON><PERSON> quyền owner n<PERSON><PERSON><PERSON> {group_name}", "@crawl_group_change_owner": {"description": "<PERSON><PERSON><PERSON><PERSON> quyền owner nhóm XXX ở WorkPlace", "placeholders": {"group_name": {"type": "String"}}}, "crawl_group_leave": "Admin account rời nh<PERSON>m {group_name}", "@crawl_group_leave": {"description": "Admin account rời nhóm XXX ở WorkPlace", "placeholders": {"group_name": {"type": "String"}}}, "crawl_feed_from_a_group": "• <PERSON><PERSON><PERSON> thông tin bài viết trên nhóm \"{group_name}\" ở WorkPlace", "@crawl_feed_from_a_group": {"description": "• <PERSON><PERSON><PERSON> thông tin bài viết trên nhóm XXX ở WorkPlace", "placeholders": {"group_name": {"type": "String"}}}, "crawl_feed_from_a_members": "• <PERSON><PERSON><PERSON> thông tin bài viết từ {count} người dùng ở WorkPlace", "@crawl_feed_from_a_members": {"description": "• <PERSON><PERSON><PERSON> thông tin bài viết từ XXX người dùng ở WorkPlace", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_feed_from_member": "• <PERSON><PERSON><PERSON> thông tin bài viết từ người dùng \"{user_name}\"", "@crawl_feed_from_member": {"description": "• <PERSON><PERSON><PERSON> thông tin bài viết từ người dùng XXX", "placeholders": {"user_name": {"type": "String"}}}, "crawl_feed_save_feed": "• L<PERSON><PERSON> trữ dữ liệu bài viết vào bộ nhớ", "crawl_feed_get_attachments": "• T<PERSON><PERSON> xuống các tệ<PERSON>, h<PERSON><PERSON>, video từ {count} bài viết ở WorkPlace", "@crawl_feed_get_attachments": {"description": "• T<PERSON><PERSON> xuống các tệ<PERSON>, h<PERSON><PERSON> <PERSON>, video từ XXX bài viết ở WorkPlace", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_gp_upload_comment_attachments": "• <PERSON><PERSON><PERSON> lên c<PERSON>c tệ<PERSON>, h<PERSON><PERSON>, video từ {count} comments ở WorkPlace lên <PERSON>", "@crawl_gp_upload_comment_attachments": {"description": "• <PERSON><PERSON><PERSON> lên c<PERSON>c tệ<PERSON>, h<PERSON><PERSON>, video từ XXX comments ở WorkPlace lên <PERSON>", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_feed_get_comments": "• T<PERSON><PERSON> xuống các tệ<PERSON>, h<PERSON><PERSON> <PERSON>, video từ bình luận của {count} bài viết ở WorkPlace", "@crawl_feed_get_comments": {"description": "• T<PERSON><PERSON> xuống các tệ<PERSON>, h<PERSON><PERSON> <PERSON>, video từ bình luận của XXX bài viết ở WorkPlace", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_feed_upload_attachments": "• <PERSON><PERSON><PERSON> lên c<PERSON>c tệ<PERSON>, h<PERSON><PERSON>, video từ bài viết \"{feed_name}\" ở WorkPlace", "@crawl_feed_upload_attachments": {"description": "• T<PERSON><PERSON> xuống các tệ<PERSON>, h<PERSON><PERSON> <PERSON>, video từ bài viết XXX ở WorkPlace", "placeholders": {"feed_name": {"type": "String"}}}, "crawl_feed_upload_comments": "• <PERSON><PERSON><PERSON> lên c<PERSON>c t<PERSON><PERSON>, h<PERSON><PERSON>, video từ comment của bài viết {feed_name} ở WorkPlace", "@crawl_feed_upload_comments": {"description": "• T<PERSON><PERSON> xuống c<PERSON>c tệ<PERSON>, h<PERSON><PERSON> <PERSON>, video từ  comment của bài viết XXX ở WorkPlace", "placeholders": {"feed_name": {"type": "String"}}}, "crawl_feed_save_feed_csv": "• <PERSON><PERSON><PERSON> trữ thông tin bài viết vào file CSV", "crawl_thread_get_conversations": "• <PERSON><PERSON><PERSON> thông tin cuộc hội thoại ở WorkPlace", "crawl_thread_get_conversation_from_user": "• <PERSON><PERSON><PERSON> thông tin cuộc hội thoại của người dùng \"{username}\" ở WorkPlace", "@crawl_thread_get_conversation_from_user": {"description": "• T<PERSON><PERSON> xuống c<PERSON>c tệ<PERSON>, h<PERSON><PERSON> <PERSON>, video từ comment của bài viết XXX ở WorkPlace", "placeholders": {"username": {"type": "String"}}}, "crawl_feed_get_reactions": "• <PERSON><PERSON><PERSON> thông tin tương tác từ {count} bà<PERSON> viết ở WorkPlace", "@crawl_feed_get_reactions": {"description": "• <PERSON><PERSON><PERSON> thông tin tương tác từ XXX bài viết ở WorkPlace", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_feed_get_reactions_from": "• <PERSON><PERSON><PERSON> thông tin tương tác bài viết \"{post_id}\"", "@crawl_feed_get_reactions_from": {"description": "• <PERSON><PERSON><PERSON> thông tin tương tác từ bài viết XXX ở WorkPlace", "placeholders": {"post_id": {"type": "String"}}}, "crawl_feed_get_seen": "• <PERSON><PERSON><PERSON> thông tin lượt xem từ {count} bài viết ở WorkPlace", "@crawl_feed_get_seen": {"description": "• <PERSON><PERSON><PERSON> thông tin lượt xem từ XXX bài viết ở WorkPlace", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_feed_get_seen_from": "• <PERSON><PERSON><PERSON> thông tin lượt xem bài viết \"{post_id}\"", "@crawl_feed_get_seen_from": {"description": "• <PERSON><PERSON><PERSON> thông tin lượt xem từ bài viết XXX ở WorkPlace", "placeholders": {"post_id": {"type": "String"}}}, "crawl_feed_get_attachment": "• <PERSON><PERSON><PERSON> thông tin attachment bài viết", "crawl_feed_get_cmt": "• Lấy thông tin attachment", "crawl_feed_get_react": "• Lấy thông tin reaction bài viết", "crawl_thread_save_thread": "• <PERSON><PERSON><PERSON> trữ dữ liệu hội thoại vào bộ nhớ", "crawl_thread_get_messages": "• <PERSON><PERSON><PERSON> thông tin tin nhắn ở WorkPlace", "crawl_thread_get_message": "• <PERSON><PERSON><PERSON> thông tin tin nhắn id = \"{message_id}\" ở WorkPlace", "@crawl_thread_get_message": {"description": "• <PERSON><PERSON><PERSON> thông tin tin nhắn id XXX ở WorkPlace ở WorkPlace", "placeholders": {"message_id": {"type": "String"}}}, "crawl_thread_get_conversation_attachment": "• <PERSON><PERSON><PERSON> xuống c<PERSON>c tệ<PERSON>, <PERSON><PERSON><PERSON>, video của tin nhắn từ cuộc hội thoại", "crawl_thread_get_conversation_from_a_thread": "• T<PERSON><PERSON> xuống c<PERSON>c tệ<PERSON>, <PERSON><PERSON><PERSON>, video của tin nhắn từ cuộc hội thoại \"{thread_name}\" ở WorkPlace", "@crawl_thread_get_conversation_from_a_thread": {"description": "• T<PERSON><PERSON> xuống các tệ<PERSON>, <PERSON><PERSON><PERSON>, video của tin nhắn từ cuộc hội thoại XXX ở WorkPlace", "placeholders": {"thread_name": {"type": "String"}}}, "crawl_thread_upload_attachment": "• <PERSON><PERSON><PERSON> lê<PERSON> các tệ<PERSON> đ<PERSON>, <PERSON><PERSON><PERSON>, video của tin nhắn từ {count} cuộc hội thoại", "@crawl_thread_upload_attachment": {"description": "• <PERSON><PERSON><PERSON> lê<PERSON> các tệ<PERSON> đ<PERSON>, <PERSON><PERSON><PERSON>, video của tin nhắn từ XXX cuộc hội thoại", "placeholders": {"count": {"type": "int", "format": "decimalPattern", "optionalParameters": {"decimalDigits": 0}}}}, "crawl_thread_upload_message_attachment": "• <PERSON><PERSON><PERSON> lê<PERSON> c<PERSON>c tệ<PERSON> đ<PERSON>, <PERSON><PERSON><PERSON>, video của tin nhắn từ cuộc hội thoại \"{thread_name}\" ở WorkPlace", "@crawl_thread_upload_message_attachment": {"description": "• <PERSON><PERSON><PERSON> lê<PERSON> các tệ<PERSON> đ<PERSON>, <PERSON><PERSON><PERSON>, video của tin nhắn từ cuộc hội thoại XXX ở WorkPlace", "placeholders": {"thread_name": {"type": "String"}}}, "crawl_thread_upload_message_sticker": "• <PERSON><PERSON><PERSON> lê<PERSON> các sticker từ cuộc hội thoại \"{thread_name}\" ở WorkPlace", "@crawl_thread_upload_message_sticker": {"description": "• T<PERSON><PERSON> lên <PERSON> các sticker của tin nhắn từ cuộc hội thoại XXX ở WorkPlace", "placeholders": {"thread_name": {"type": "String"}}}, "crawl_thread_get_attachment_from_a_message": "• <PERSON><PERSON><PERSON> xu<PERSON> c<PERSON>c tệ<PERSON>, h<PERSON><PERSON>, video của tin nhắn từ tin nhắn \"{message_id}\" ở WorkPlace", "@crawl_thread_get_attachment_from_a_message": {"description": "• T<PERSON><PERSON> xuống c<PERSON>c tệ<PERSON>, h<PERSON><PERSON>, video của tin nhắn từ tin nhắn XXX ở WorkPlace", "placeholders": {"message_id": {"type": "String"}}}, "crawl_thread_get_sticker_from_a_message": "• T<PERSON><PERSON> xuống sticker từ tin nhắn \"{message_id}\" ở WorkPlace", "@crawl_thread_get_sticker_from_a_message": {"description": "• Tải xuống sticker từ tin nhắn XXX ở WorkPlace", "placeholders": {"message_id": {"type": "String"}}}, "crawl_thread_update_thread": "• <PERSON><PERSON><PERSON> nhật tin nhắn vào cuộc hội thoại", "crawl_thread_save_message": "• <PERSON><PERSON><PERSON> trữ dữ liệu tin nhắn vào bộ nhớ", "crawl_thread_save_thread_csv": "• <PERSON><PERSON><PERSON> trữ thông tin cuộc hội thoại vào file CSV", "crawl_thread_save_message_csv": "• <PERSON><PERSON><PERSON> trữ thông tin tin nhắn vào file CSV"}