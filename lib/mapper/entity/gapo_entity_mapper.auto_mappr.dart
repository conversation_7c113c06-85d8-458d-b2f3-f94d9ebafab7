// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i2;
import 'package:gp_core/models/upload/upload_file_url_response_model.dart'
    as _i18;

import '../../data/model/gpw/gp_upload_file_response.dart' as _i16;
import '../../data/model/gpw/upload_image_response.dart' as _i14;
import '../../domain/entity/enums/gp_enums.dart' as _i1;
import '../../domain/entity/gapo/gp_comment.dart' as _i13;
import '../../domain/entity/gapo/gp_group.dart' as _i6;
import '../../domain/entity/gapo/gp_message.dart' as _i11;
import '../../domain/entity/gapo/gp_post.dart' as _i8;
import '../../domain/entity/gapo/gp_thread.dart' as _i10;
import '../../domain/entity/gapo/gpuser.dart' as _i4;
import '../../domain/entity/gapo/upload/gp_upload_response.dart' as _i17;
import '../../domain/entity/gapo/upload/upload_response.entity.dart' as _i15;
import '../../domain/entity/workplace/feed/workplace_feed.entity.dart' as _i7;
import '../../domain/entity/workplace/group/workplace_group.entity.dart' as _i5;
import '../../domain/entity/workplace/other/workplace_comment.entity.dart'
    as _i12;
import '../../domain/entity/workplace/thread/workplace_conversations.entity.dart'
    as _i9;
import '../../domain/entity/workplace/user/workplace_community_member.entity.dart'
    as _i3;
import 'gapo_entity_mapper.dart' as _i19;

/// {@template package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
/// Available mappings:
/// - `WorkPlaceCommunityMemberEntity` → `GPUser`.
/// - `WorkPlaceGroupEntity` → `GPGroup`.
/// - `WorkPlaceFeedEntity` → `GPPost`.
/// - `WorkPlaceCommunityMemberEntity` → `GPPostMention`.
/// - `WorkPlaceConversationEntity` → `GPThread`.
/// - `WorkPlaceMessagesEntity` → `GPMessage`.
/// - `WorkPlaceMessageTagsEntity` → `GPCommentMention`.
/// - `WorkPlaceCommentEntity` → `GPComment`.
/// - `WorkPlaceReactionEntity` → `GPReaction`.
/// - `WorkPlaceCommunityMemberEntity` → `GPCommentAs`.
/// - `GPUploadImageResponseModel` → `UploadResponseEntity`.
/// - `GPUploadFileResponseModel` → `UploadResponseEntity`.
/// - `UploadResponseEntity` → `GPUploadResponse`.
/// {@endtemplate}
class $GapoEntityMapper implements _i2.AutoMapprInterface {
  const $GapoEntityMapper();

  Type _typeOf<T>() => T;

  List<_i2.AutoMapprInterface> get _delegates => const [];

  /// {@macro AutoMapprInterface:canConvert}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity>() ||
            sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity?>()) &&
        (targetTypeOf == _typeOf<_i4.GPUser>() ||
            targetTypeOf == _typeOf<_i4.GPUser?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i5.WorkPlaceGroupEntity>() ||
            sourceTypeOf == _typeOf<_i5.WorkPlaceGroupEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.GPGroup>() ||
            targetTypeOf == _typeOf<_i6.GPGroup?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i7.WorkPlaceFeedEntity>() ||
            sourceTypeOf == _typeOf<_i7.WorkPlaceFeedEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.GPPost>() ||
            targetTypeOf == _typeOf<_i8.GPPost?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity>() ||
            sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.GPPostMention>() ||
            targetTypeOf == _typeOf<_i8.GPPostMention?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.WorkPlaceConversationEntity>() ||
            sourceTypeOf == _typeOf<_i9.WorkPlaceConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i10.GPThread>() ||
            targetTypeOf == _typeOf<_i10.GPThread?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.WorkPlaceMessagesEntity>() ||
            sourceTypeOf == _typeOf<_i9.WorkPlaceMessagesEntity?>()) &&
        (targetTypeOf == _typeOf<_i11.GPMessage>() ||
            targetTypeOf == _typeOf<_i11.GPMessage?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i12.WorkPlaceMessageTagsEntity>() ||
            sourceTypeOf == _typeOf<_i12.WorkPlaceMessageTagsEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.GPCommentMention>() ||
            targetTypeOf == _typeOf<_i13.GPCommentMention?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i12.WorkPlaceCommentEntity>() ||
            sourceTypeOf == _typeOf<_i12.WorkPlaceCommentEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.GPComment>() ||
            targetTypeOf == _typeOf<_i13.GPComment?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i12.WorkPlaceReactionEntity>() ||
            sourceTypeOf == _typeOf<_i12.WorkPlaceReactionEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.GPReaction>() ||
            targetTypeOf == _typeOf<_i8.GPReaction?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity>() ||
            sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.GPCommentAs>() ||
            targetTypeOf == _typeOf<_i13.GPCommentAs?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i14.GPUploadImageResponseModel>() ||
            sourceTypeOf == _typeOf<_i14.GPUploadImageResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i15.UploadResponseEntity>() ||
            targetTypeOf == _typeOf<_i15.UploadResponseEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i16.GPUploadFileResponseModel>() ||
            sourceTypeOf == _typeOf<_i16.GPUploadFileResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i15.UploadResponseEntity>() ||
            targetTypeOf == _typeOf<_i15.UploadResponseEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.UploadResponseEntity>() ||
            sourceTypeOf == _typeOf<_i15.UploadResponseEntity?>()) &&
        (targetTypeOf == _typeOf<_i17.GPUploadResponse>() ||
            targetTypeOf == _typeOf<_i17.GPUploadResponse?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  /// {@macro AutoMapprInterface:convert}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// {@macro AutoMapprInterface:tryConvert}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  /// {@macro AutoMapprInterface:convertIterable}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// {@macro AutoMapprInterface:convertList}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// {@macro AutoMapprInterface:convertSet}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity>() ||
            sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity?>()) &&
        (targetTypeOf == _typeOf<_i4.GPUser>() ||
            targetTypeOf == _typeOf<_i4.GPUser?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$WorkPlaceCommunityMemberEntity_To__i4$GPUser(
          (model as _i3.WorkPlaceCommunityMemberEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i5.WorkPlaceGroupEntity>() ||
            sourceTypeOf == _typeOf<_i5.WorkPlaceGroupEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.GPGroup>() ||
            targetTypeOf == _typeOf<_i6.GPGroup?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i5$WorkPlaceGroupEntity_To__i6$GPGroup(
          (model as _i5.WorkPlaceGroupEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i7.WorkPlaceFeedEntity>() ||
            sourceTypeOf == _typeOf<_i7.WorkPlaceFeedEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.GPPost>() ||
            targetTypeOf == _typeOf<_i8.GPPost?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i7$WorkPlaceFeedEntity_To__i8$GPPost(
          (model as _i7.WorkPlaceFeedEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity>() ||
            sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.GPPostMention>() ||
            targetTypeOf == _typeOf<_i8.GPPostMention?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$WorkPlaceCommunityMemberEntity_To__i8$GPPostMention(
          (model as _i3.WorkPlaceCommunityMemberEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.WorkPlaceConversationEntity>() ||
            sourceTypeOf == _typeOf<_i9.WorkPlaceConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i10.GPThread>() ||
            targetTypeOf == _typeOf<_i10.GPThread?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$WorkPlaceConversationEntity_To__i10$GPThread(
          (model as _i9.WorkPlaceConversationEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.WorkPlaceMessagesEntity>() ||
            sourceTypeOf == _typeOf<_i9.WorkPlaceMessagesEntity?>()) &&
        (targetTypeOf == _typeOf<_i11.GPMessage>() ||
            targetTypeOf == _typeOf<_i11.GPMessage?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$WorkPlaceMessagesEntity_To__i11$GPMessage(
          (model as _i9.WorkPlaceMessagesEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i12.WorkPlaceMessageTagsEntity>() ||
            sourceTypeOf == _typeOf<_i12.WorkPlaceMessageTagsEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.GPCommentMention>() ||
            targetTypeOf == _typeOf<_i13.GPCommentMention?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i12$WorkPlaceMessageTagsEntity_To__i13$GPCommentMention(
          (model as _i12.WorkPlaceMessageTagsEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i12.WorkPlaceCommentEntity>() ||
            sourceTypeOf == _typeOf<_i12.WorkPlaceCommentEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.GPComment>() ||
            targetTypeOf == _typeOf<_i13.GPComment?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i12$WorkPlaceCommentEntity_To__i13$GPComment(
          (model as _i12.WorkPlaceCommentEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i12.WorkPlaceReactionEntity>() ||
            sourceTypeOf == _typeOf<_i12.WorkPlaceReactionEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.GPReaction>() ||
            targetTypeOf == _typeOf<_i8.GPReaction?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i12$WorkPlaceReactionEntity_To__i8$GPReaction(
          (model as _i12.WorkPlaceReactionEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity>() ||
            sourceTypeOf == _typeOf<_i3.WorkPlaceCommunityMemberEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.GPCommentAs>() ||
            targetTypeOf == _typeOf<_i13.GPCommentAs?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$WorkPlaceCommunityMemberEntity_To__i13$GPCommentAs(
          (model as _i3.WorkPlaceCommunityMemberEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i14.GPUploadImageResponseModel>() ||
            sourceTypeOf == _typeOf<_i14.GPUploadImageResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i15.UploadResponseEntity>() ||
            targetTypeOf == _typeOf<_i15.UploadResponseEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i14$GPUploadImageResponseModel_To__i15$UploadResponseEntity(
          (model as _i14.GPUploadImageResponseModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i16.GPUploadFileResponseModel>() ||
            sourceTypeOf == _typeOf<_i16.GPUploadFileResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i15.UploadResponseEntity>() ||
            targetTypeOf == _typeOf<_i15.UploadResponseEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i16$GPUploadFileResponseModel_To__i15$UploadResponseEntity(
          (model as _i16.GPUploadFileResponseModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.UploadResponseEntity>() ||
            sourceTypeOf == _typeOf<_i15.UploadResponseEntity?>()) &&
        (targetTypeOf == _typeOf<_i17.GPUploadResponse>() ||
            targetTypeOf == _typeOf<_i17.GPUploadResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$UploadResponseEntity_To__i17$GPUploadResponse(
          (model as _i15.UploadResponseEntity?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  /// {@macro AutoMapprInterface:useSafeMapping}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i4.GPUser _map__i3$WorkPlaceCommunityMemberEntity_To__i4$GPUser(
      _i3.WorkPlaceCommunityMemberEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceCommunityMemberEntity → GPUser failed because WorkPlaceCommunityMemberEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceCommunityMemberEntity, GPUser> to handle null values during mapping.');
    }
    return _i4.GPUser(
      name: model.name,
      id: model.gpUserId,
    );
  }

  _i6.GPGroup _map__i5$WorkPlaceGroupEntity_To__i6$GPGroup(
      _i5.WorkPlaceGroupEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceGroupEntity → GPGroup failed because WorkPlaceGroupEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceGroupEntity, GPGroup> to handle null values during mapping.');
    }
    return _i6.GPGroup(
      name: model.name,
      description: model.description,
      privacy: _i19.GapoEntityMapper.mapToGPGroupPrivacy(model),
      discoverability: _i1.GPGroupDiscoverability.visible,
      wpGroupId: model.id,
      cover: _i19.GapoEntityMapper.mapToGPGroupCover(model),
      createdAt: _i19.GapoEntityMapper.mapToGPGroupCreateAt(model),
      previewMembers: _i19.GapoEntityMapper.mapToGPGroupPreviewMembers(model),
      owner: _i19.GapoEntityMapper.mapToGPGroupOwner(model),
    );
  }

  _i8.GPPost _map__i7$WorkPlaceFeedEntity_To__i8$GPPost(
      _i7.WorkPlaceFeedEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceFeedEntity → GPPost failed because WorkPlaceFeedEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceFeedEntity, GPPost> to handle null values during mapping.');
    }
    return _i8.GPPost(
      content: model.message,
      contentRtf: 1,
      media: _i19.GapoEntityMapper.mapToPostMedia(model),
      privacy: _i19.GapoEntityMapper.mapToPostPrivacy(model),
      target: _i19.GapoEntityMapper.mapToPostTarget(model),
      userId: _i19.GapoEntityMapper.mapToPostUserId(model),
      createdAt: _i19.GapoEntityMapper.mapToPostCreateAt(model),
      comments: _i19.GapoEntityMapper.mapToPostComments(model),
      reactions: _i19.GapoEntityMapper.mapToPostReactions(model),
      seen: _i19.GapoEntityMapper.mapToPostSeen(model),
    );
  }

  _i8.GPPostMention
      _map__i3$WorkPlaceCommunityMemberEntity_To__i8$GPPostMention(
          _i3.WorkPlaceCommunityMemberEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceCommunityMemberEntity → GPPostMention failed because WorkPlaceCommunityMemberEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceCommunityMemberEntity, GPPostMention> to handle null values during mapping.');
    }
    return _i8.GPPostMention(
      id: model.gpUserId,
      type: _i1.GPMentionType.tag,
    );
  }

  _i10.GPThread _map__i9$WorkPlaceConversationEntity_To__i10$GPThread(
      _i9.WorkPlaceConversationEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceConversationEntity → GPThread failed because WorkPlaceConversationEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceConversationEntity, GPThread> to handle null values during mapping.');
    }
    return _i10.GPThread(
      userId: _i19.GapoEntityMapper.mapToThreadUserId(model),
      threadId: model.id,
      name: _i19.GapoEntityMapper.mapToThreadName(model),
      participantIds: _i19.GapoEntityMapper.mapToThreadParticipantIds(model),
      type: _i19.GapoEntityMapper.mapToThreadType(model),
      isRead: _i19.GapoEntityMapper.mapToThreadIsRead(model),
    );
  }

  _i11.GPMessage _map__i9$WorkPlaceMessagesEntity_To__i11$GPMessage(
      _i9.WorkPlaceMessagesEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceMessagesEntity → GPMessage failed because WorkPlaceMessagesEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceMessagesEntity, GPMessage> to handle null values during mapping.');
    }
    return _i11.GPMessage(
      threadId: _i19.GapoEntityMapper.mapToMessageThreadId(model),
      type: _i19.GapoEntityMapper.mapToMessageType(model),
      from: _i19.GapoEntityMapper.mapToMessageFrom(model),
      text: model.message,
      media: _i19.GapoEntityMapper.mapToMessageMedia(model),
      createdAt: _i19.GapoEntityMapper.mapToMessageCreateAt(model),
      isMarkdownText: 0,
    );
  }

  _i13.GPCommentMention
      _map__i12$WorkPlaceMessageTagsEntity_To__i13$GPCommentMention(
          _i12.WorkPlaceMessageTagsEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceMessageTagsEntity → GPCommentMention failed because WorkPlaceMessageTagsEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceMessageTagsEntity, GPCommentMention> to handle null values during mapping.');
    }
    return _i13.GPCommentMention(
      offset: model.offset,
      length: model.length,
      mentionId: _i19.GapoEntityMapper.mapToMentionId(model),
    );
  }

  _i13.GPComment _map__i12$WorkPlaceCommentEntity_To__i13$GPComment(
      _i12.WorkPlaceCommentEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceCommentEntity → GPComment failed because WorkPlaceCommentEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceCommentEntity, GPComment> to handle null values during mapping.');
    }
    return _i13.GPComment(
      commentAs: _i19.GapoEntityMapper.mapToCommentAs(model),
      text: _i19.GapoEntityMapper.mapToCommentText(model),
      type: _i19.GapoEntityMapper.mapToCommentType(model),
      medias: _i19.GapoEntityMapper.mapToCommentMedias(model),
      status: _i1.GPCommentStatus.approved,
      targetType: r'post',
      dataSource: 5,
      createdAt: _i19.GapoEntityMapper.mapToCommentCreateAt(model),
      reactions: _i19.GapoEntityMapper.mapToCommentReactions(model),
      replies: _i19.GapoEntityMapper.mapToCommentReplies(model),
    );
  }

  _i8.GPReaction _map__i12$WorkPlaceReactionEntity_To__i8$GPReaction(
      _i12.WorkPlaceReactionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceReactionEntity → GPReaction failed because WorkPlaceReactionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceReactionEntity, GPReaction> to handle null values during mapping.');
    }
    return _i8.GPReaction(
      userId: model.gpUserId,
      type: _i19.GapoEntityMapper.mapToReactionType(model),
    );
  }

  _i13.GPCommentAs _map__i3$WorkPlaceCommunityMemberEntity_To__i13$GPCommentAs(
      _i3.WorkPlaceCommunityMemberEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceCommunityMemberEntity → GPCommentAs failed because WorkPlaceCommunityMemberEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceCommunityMemberEntity, GPCommentAs> to handle null values during mapping.');
    }
    return _i13.GPCommentAs(
      authorId: model.gpUserId,
      authorType: _i1.GPCommentAsAuthorType.user,
    );
  }

  _i15.UploadResponseEntity
      _map__i14$GPUploadImageResponseModel_To__i15$UploadResponseEntity(
          _i14.GPUploadImageResponseModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPUploadImageResponseModel → UploadResponseEntity failed because GPUploadImageResponseModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPUploadImageResponseModel, UploadResponseEntity> to handle null values during mapping.');
    }
    return _i15.UploadResponseEntity(
      id: model.id,
      name: model.name,
      userId: model.userId,
      size: model.size,
      fileType: model.fileType,
      type: model.type,
      url: null,
      src: model.src,
      fileLink: model.fileLink,
      quality: model.quality,
      source: model.source,
      category: model.category,
      width: model.width,
      height: model.height,
    );
  }

  _i15.UploadResponseEntity
      _map__i16$GPUploadFileResponseModel_To__i15$UploadResponseEntity(
          _i16.GPUploadFileResponseModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPUploadFileResponseModel → UploadResponseEntity failed because GPUploadFileResponseModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPUploadFileResponseModel, UploadResponseEntity> to handle null values during mapping.');
    }
    return _i15.UploadResponseEntity(
      id: model.id,
      name: model.name,
      userId: model.userId,
      size: model.size,
      fileType: model.fileType,
      type: model.type,
      url: null,
      thumbUrl: null,
      src: model.src,
      fileLink: model.fileLink,
      quality: model.quality,
      source: model.source,
    );
  }

  _i17.GPUploadResponse _map__i15$UploadResponseEntity_To__i17$GPUploadResponse(
      _i15.UploadResponseEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping UploadResponseEntity → GPUploadResponse failed because UploadResponseEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<UploadResponseEntity, GPUploadResponse> to handle null values during mapping.');
    }
    return _i17.GPUploadResponse(
      id: model.id,
      name: model.name,
      userId: model.userId,
      size: model.size,
      fileType: model.fileType,
      type: model.type,
      url: null,
      thumbUrl: null,
      src: model.src,
      fileLink: model.fileLink,
      quality: model.quality,
      source: model.source,
      category: model.category,
      width: model.width,
      height: model.height,
    );
  }
}
