// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i1;

import '../../data/model/facebook/community_response.dart' as _i2;
import '../../data/model/workplace/base/workplace_user.dart' as _i6;
import '../../data/model/workplace/comment/message_tags.dart' as _i9;
import '../../data/model/workplace/comment/workplace_reactions.dart' as _i7;
import '../../data/model/workplace/conversation/workplace_conversation_attachment_response.dart'
    as _i19;
import '../../data/model/workplace/conversation/workplace_conversations_response.dart'
    as _i17;
import '../../data/model/workplace/group/workplace_feeds_response.dart' as _i13;
import '../../data/model/workplace/group/workplace_group_response.dart' as _i15;
import '../../data/model/workplace/post/workplace_post_attachments_response.dart'
    as _i11;
import '../../data/model/workplace/post/workplace_post_comments_response.dart'
    as _i10;
import '../../domain/entity/gapo/gpuser.dart' as _i5;
import '../../domain/entity/workplace/community/workplace_community.entity.dart'
    as _i3;
import '../../domain/entity/workplace/feed/workplace_feed.entity.dart' as _i14;
import '../../domain/entity/workplace/group/workplace_group.entity.dart'
    as _i16;
import '../../domain/entity/workplace/other/workplace_attachment.entity.dart'
    as _i12;
import '../../domain/entity/workplace/other/workplace_comment.entity.dart'
    as _i8;
import '../../domain/entity/workplace/thread/workplace_conversation_attachment.entity.dart'
    as _i20;
import '../../domain/entity/workplace/thread/workplace_conversations.entity.dart'
    as _i18;
import '../../domain/entity/workplace/user/workplace_community_member.entity.dart'
    as _i4;
import 'workplace_entity_mapper.dart' as _i21;

/// {@template package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
/// Available mappings:
/// - `FaceBookCommunityResponse` → `WorkPlaceCommunityEntity`.
/// - `WorkPlaceCommunityMemberEntity` → `GPUser`.
/// - `WorkPlaceUser` → `WorkPlaceCommunityMemberEntity`.
/// - `WorkPlaceReaction` → `WorkPlaceReactionEntity`.
/// - `WorkPlaceMessageTags` → `WorkPlaceMessageTagsEntity`.
/// - `WorkPlaceCommentsResponse` → `WorkPlaceCommentEntity`.
/// - `WorkPlacePostAttachmentsResponse` → `WorkPlaceAttachmentEntity`.
/// - `WorkPlaceFeedsResponse` → `WorkPlaceFeedEntity`.
/// - `WorkPlaceGroupResponse` → `WorkPlaceGroupEntity`.
/// - `AttachmentTarget` → `AttachmentTargetEntity`.
/// - `AttachmentMedia` → `AttachmentMediaEntity`.
/// - `AttachmentMediaImage` → `AttachmentMediaImageEntity`.
/// - `WorkPlaceUserConversationsResponse` → `WorkPlaceConversationEntity`.
/// - `Messages` → `WorkPlaceMessagesEntity`.
/// - `WorkPlaceConversationAttachmentsResponse` → `WorkPlaceConversationAttachmentsEntity`.
/// - `AttachmentImageData` → `AttachmentImageDataEntity`.
/// - `AttachmentVideoData` → `AttachmentVideoDataEntity`.
/// - `GroupCover` → `GroupCoverEntity`.
/// {@endtemplate}
class $WorkPlaceEntityMapper implements _i1.AutoMapprInterface {
  const $WorkPlaceEntityMapper();

  Type _typeOf<T>() => T;

  List<_i1.AutoMapprInterface> get _delegates => const [];

  /// {@macro AutoMapprInterface:canConvert}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.FaceBookCommunityResponse>() ||
            sourceTypeOf == _typeOf<_i2.FaceBookCommunityResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.WorkPlaceCommunityEntity>() ||
            targetTypeOf == _typeOf<_i3.WorkPlaceCommunityEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.WorkPlaceCommunityMemberEntity>() ||
            sourceTypeOf == _typeOf<_i4.WorkPlaceCommunityMemberEntity?>()) &&
        (targetTypeOf == _typeOf<_i5.GPUser>() ||
            targetTypeOf == _typeOf<_i5.GPUser?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i6.WorkPlaceUser>() ||
            sourceTypeOf == _typeOf<_i6.WorkPlaceUser?>()) &&
        (targetTypeOf == _typeOf<_i4.WorkPlaceCommunityMemberEntity>() ||
            targetTypeOf == _typeOf<_i4.WorkPlaceCommunityMemberEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i7.WorkPlaceReaction>() ||
            sourceTypeOf == _typeOf<_i7.WorkPlaceReaction?>()) &&
        (targetTypeOf == _typeOf<_i8.WorkPlaceReactionEntity>() ||
            targetTypeOf == _typeOf<_i8.WorkPlaceReactionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.WorkPlaceMessageTags>() ||
            sourceTypeOf == _typeOf<_i9.WorkPlaceMessageTags?>()) &&
        (targetTypeOf == _typeOf<_i8.WorkPlaceMessageTagsEntity>() ||
            targetTypeOf == _typeOf<_i8.WorkPlaceMessageTagsEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i10.WorkPlaceCommentsResponse>() ||
            sourceTypeOf == _typeOf<_i10.WorkPlaceCommentsResponse?>()) &&
        (targetTypeOf == _typeOf<_i8.WorkPlaceCommentEntity>() ||
            targetTypeOf == _typeOf<_i8.WorkPlaceCommentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i11.WorkPlacePostAttachmentsResponse>() ||
            sourceTypeOf ==
                _typeOf<_i11.WorkPlacePostAttachmentsResponse?>()) &&
        (targetTypeOf == _typeOf<_i12.WorkPlaceAttachmentEntity>() ||
            targetTypeOf == _typeOf<_i12.WorkPlaceAttachmentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i13.WorkPlaceFeedsResponse>() ||
            sourceTypeOf == _typeOf<_i13.WorkPlaceFeedsResponse?>()) &&
        (targetTypeOf == _typeOf<_i14.WorkPlaceFeedEntity>() ||
            targetTypeOf == _typeOf<_i14.WorkPlaceFeedEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.WorkPlaceGroupResponse>() ||
            sourceTypeOf == _typeOf<_i15.WorkPlaceGroupResponse?>()) &&
        (targetTypeOf == _typeOf<_i16.WorkPlaceGroupEntity>() ||
            targetTypeOf == _typeOf<_i16.WorkPlaceGroupEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i11.AttachmentTarget>() ||
            sourceTypeOf == _typeOf<_i11.AttachmentTarget?>()) &&
        (targetTypeOf == _typeOf<_i12.AttachmentTargetEntity>() ||
            targetTypeOf == _typeOf<_i12.AttachmentTargetEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i11.AttachmentMedia>() ||
            sourceTypeOf == _typeOf<_i11.AttachmentMedia?>()) &&
        (targetTypeOf == _typeOf<_i12.AttachmentMediaEntity>() ||
            targetTypeOf == _typeOf<_i12.AttachmentMediaEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i11.AttachmentMediaImage>() ||
            sourceTypeOf == _typeOf<_i11.AttachmentMediaImage?>()) &&
        (targetTypeOf == _typeOf<_i12.AttachmentMediaImageEntity>() ||
            targetTypeOf == _typeOf<_i12.AttachmentMediaImageEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i17.WorkPlaceUserConversationsResponse>() ||
            sourceTypeOf ==
                _typeOf<_i17.WorkPlaceUserConversationsResponse?>()) &&
        (targetTypeOf == _typeOf<_i18.WorkPlaceConversationEntity>() ||
            targetTypeOf == _typeOf<_i18.WorkPlaceConversationEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i17.Messages>() ||
            sourceTypeOf == _typeOf<_i17.Messages?>()) &&
        (targetTypeOf == _typeOf<_i18.WorkPlaceMessagesEntity>() ||
            targetTypeOf == _typeOf<_i18.WorkPlaceMessagesEntity?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i19.WorkPlaceConversationAttachmentsResponse>() ||
            sourceTypeOf ==
                _typeOf<_i19.WorkPlaceConversationAttachmentsResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i20.WorkPlaceConversationAttachmentsEntity>() ||
            targetTypeOf ==
                _typeOf<_i20.WorkPlaceConversationAttachmentsEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i19.AttachmentImageData>() ||
            sourceTypeOf == _typeOf<_i19.AttachmentImageData?>()) &&
        (targetTypeOf == _typeOf<_i20.AttachmentImageDataEntity>() ||
            targetTypeOf == _typeOf<_i20.AttachmentImageDataEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i19.AttachmentVideoData>() ||
            sourceTypeOf == _typeOf<_i19.AttachmentVideoData?>()) &&
        (targetTypeOf == _typeOf<_i20.AttachmentVideoDataEntity>() ||
            targetTypeOf == _typeOf<_i20.AttachmentVideoDataEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.GroupCover>() ||
            sourceTypeOf == _typeOf<_i15.GroupCover?>()) &&
        (targetTypeOf == _typeOf<_i16.GroupCoverEntity>() ||
            targetTypeOf == _typeOf<_i16.GroupCoverEntity?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  /// {@macro AutoMapprInterface:convert}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// {@macro AutoMapprInterface:tryConvert}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  /// {@macro AutoMapprInterface:convertIterable}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// {@macro AutoMapprInterface:convertList}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// {@macro AutoMapprInterface:convertSet}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.FaceBookCommunityResponse>() ||
            sourceTypeOf == _typeOf<_i2.FaceBookCommunityResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.WorkPlaceCommunityEntity>() ||
            targetTypeOf == _typeOf<_i3.WorkPlaceCommunityEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$FaceBookCommunityResponse_To__i3$WorkPlaceCommunityEntity(
          (model as _i2.FaceBookCommunityResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.WorkPlaceCommunityMemberEntity>() ||
            sourceTypeOf == _typeOf<_i4.WorkPlaceCommunityMemberEntity?>()) &&
        (targetTypeOf == _typeOf<_i5.GPUser>() ||
            targetTypeOf == _typeOf<_i5.GPUser?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$WorkPlaceCommunityMemberEntity_To__i5$GPUser(
          (model as _i4.WorkPlaceCommunityMemberEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i6.WorkPlaceUser>() ||
            sourceTypeOf == _typeOf<_i6.WorkPlaceUser?>()) &&
        (targetTypeOf == _typeOf<_i4.WorkPlaceCommunityMemberEntity>() ||
            targetTypeOf == _typeOf<_i4.WorkPlaceCommunityMemberEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i6$WorkPlaceUser_To__i4$WorkPlaceCommunityMemberEntity(
          (model as _i6.WorkPlaceUser?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i7.WorkPlaceReaction>() ||
            sourceTypeOf == _typeOf<_i7.WorkPlaceReaction?>()) &&
        (targetTypeOf == _typeOf<_i8.WorkPlaceReactionEntity>() ||
            targetTypeOf == _typeOf<_i8.WorkPlaceReactionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i7$WorkPlaceReaction_To__i8$WorkPlaceReactionEntity(
          (model as _i7.WorkPlaceReaction?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.WorkPlaceMessageTags>() ||
            sourceTypeOf == _typeOf<_i9.WorkPlaceMessageTags?>()) &&
        (targetTypeOf == _typeOf<_i8.WorkPlaceMessageTagsEntity>() ||
            targetTypeOf == _typeOf<_i8.WorkPlaceMessageTagsEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$WorkPlaceMessageTags_To__i8$WorkPlaceMessageTagsEntity(
          (model as _i9.WorkPlaceMessageTags?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i10.WorkPlaceCommentsResponse>() ||
            sourceTypeOf == _typeOf<_i10.WorkPlaceCommentsResponse?>()) &&
        (targetTypeOf == _typeOf<_i8.WorkPlaceCommentEntity>() ||
            targetTypeOf == _typeOf<_i8.WorkPlaceCommentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i10$WorkPlaceCommentsResponse_To__i8$WorkPlaceCommentEntity(
          (model as _i10.WorkPlaceCommentsResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i11.WorkPlacePostAttachmentsResponse>() ||
            sourceTypeOf ==
                _typeOf<_i11.WorkPlacePostAttachmentsResponse?>()) &&
        (targetTypeOf == _typeOf<_i12.WorkPlaceAttachmentEntity>() ||
            targetTypeOf == _typeOf<_i12.WorkPlaceAttachmentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i11$WorkPlacePostAttachmentsResponse_To__i12$WorkPlaceAttachmentEntity(
          (model as _i11.WorkPlacePostAttachmentsResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i13.WorkPlaceFeedsResponse>() ||
            sourceTypeOf == _typeOf<_i13.WorkPlaceFeedsResponse?>()) &&
        (targetTypeOf == _typeOf<_i14.WorkPlaceFeedEntity>() ||
            targetTypeOf == _typeOf<_i14.WorkPlaceFeedEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i13$WorkPlaceFeedsResponse_To__i14$WorkPlaceFeedEntity(
          (model as _i13.WorkPlaceFeedsResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.WorkPlaceGroupResponse>() ||
            sourceTypeOf == _typeOf<_i15.WorkPlaceGroupResponse?>()) &&
        (targetTypeOf == _typeOf<_i16.WorkPlaceGroupEntity>() ||
            targetTypeOf == _typeOf<_i16.WorkPlaceGroupEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$WorkPlaceGroupResponse_To__i16$WorkPlaceGroupEntity(
          (model as _i15.WorkPlaceGroupResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i11.AttachmentTarget>() ||
            sourceTypeOf == _typeOf<_i11.AttachmentTarget?>()) &&
        (targetTypeOf == _typeOf<_i12.AttachmentTargetEntity>() ||
            targetTypeOf == _typeOf<_i12.AttachmentTargetEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i11$AttachmentTarget_To__i12$AttachmentTargetEntity(
          (model as _i11.AttachmentTarget?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i11.AttachmentMedia>() ||
            sourceTypeOf == _typeOf<_i11.AttachmentMedia?>()) &&
        (targetTypeOf == _typeOf<_i12.AttachmentMediaEntity>() ||
            targetTypeOf == _typeOf<_i12.AttachmentMediaEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i11$AttachmentMedia_To__i12$AttachmentMediaEntity(
          (model as _i11.AttachmentMedia?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i11.AttachmentMediaImage>() ||
            sourceTypeOf == _typeOf<_i11.AttachmentMediaImage?>()) &&
        (targetTypeOf == _typeOf<_i12.AttachmentMediaImageEntity>() ||
            targetTypeOf == _typeOf<_i12.AttachmentMediaImageEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i11$AttachmentMediaImage_To__i12$AttachmentMediaImageEntity(
          (model as _i11.AttachmentMediaImage?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i17.WorkPlaceUserConversationsResponse>() ||
            sourceTypeOf ==
                _typeOf<_i17.WorkPlaceUserConversationsResponse?>()) &&
        (targetTypeOf == _typeOf<_i18.WorkPlaceConversationEntity>() ||
            targetTypeOf == _typeOf<_i18.WorkPlaceConversationEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i17$WorkPlaceUserConversationsResponse_To__i18$WorkPlaceConversationEntity(
          (model as _i17.WorkPlaceUserConversationsResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i17.Messages>() ||
            sourceTypeOf == _typeOf<_i17.Messages?>()) &&
        (targetTypeOf == _typeOf<_i18.WorkPlaceMessagesEntity>() ||
            targetTypeOf == _typeOf<_i18.WorkPlaceMessagesEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i17$Messages_To__i18$WorkPlaceMessagesEntity(
          (model as _i17.Messages?)) as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i19.WorkPlaceConversationAttachmentsResponse>() ||
            sourceTypeOf ==
                _typeOf<_i19.WorkPlaceConversationAttachmentsResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i20.WorkPlaceConversationAttachmentsEntity>() ||
            targetTypeOf ==
                _typeOf<_i20.WorkPlaceConversationAttachmentsEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i19$WorkPlaceConversationAttachmentsResponse_To__i20$WorkPlaceConversationAttachmentsEntity(
          (model as _i19.WorkPlaceConversationAttachmentsResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i19.AttachmentImageData>() ||
            sourceTypeOf == _typeOf<_i19.AttachmentImageData?>()) &&
        (targetTypeOf == _typeOf<_i20.AttachmentImageDataEntity>() ||
            targetTypeOf == _typeOf<_i20.AttachmentImageDataEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i19$AttachmentImageData_To__i20$AttachmentImageDataEntity(
          (model as _i19.AttachmentImageData?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i19.AttachmentVideoData>() ||
            sourceTypeOf == _typeOf<_i19.AttachmentVideoData?>()) &&
        (targetTypeOf == _typeOf<_i20.AttachmentVideoDataEntity>() ||
            targetTypeOf == _typeOf<_i20.AttachmentVideoDataEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i19$AttachmentVideoData_To__i20$AttachmentVideoDataEntity(
          (model as _i19.AttachmentVideoData?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.GroupCover>() ||
            sourceTypeOf == _typeOf<_i15.GroupCover?>()) &&
        (targetTypeOf == _typeOf<_i16.GroupCoverEntity>() ||
            targetTypeOf == _typeOf<_i16.GroupCoverEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$GroupCover_To__i16$GroupCoverEntity(
          (model as _i15.GroupCover?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  /// {@macro AutoMapprInterface:useSafeMapping}
  /// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i3.WorkPlaceCommunityEntity
      _map__i2$FaceBookCommunityResponse_To__i3$WorkPlaceCommunityEntity(
          _i2.FaceBookCommunityResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping FaceBookCommunityResponse → WorkPlaceCommunityEntity failed because FaceBookCommunityResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<FaceBookCommunityResponse, WorkPlaceCommunityEntity> to handle null values during mapping.');
    }
    return _i3.WorkPlaceCommunityEntity(
      id: model.id,
      name: model.name,
    );
  }

  _i5.GPUser _map__i4$WorkPlaceCommunityMemberEntity_To__i5$GPUser(
      _i4.WorkPlaceCommunityMemberEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceCommunityMemberEntity → GPUser failed because WorkPlaceCommunityMemberEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceCommunityMemberEntity, GPUser> to handle null values during mapping.');
    }
    return _i5.GPUser(
      name: model.name,
      id: _i21.WorkPlaceEntityMapper.mapToGPUserId(model),
    );
  }

  _i4.WorkPlaceCommunityMemberEntity
      _map__i6$WorkPlaceUser_To__i4$WorkPlaceCommunityMemberEntity(
          _i6.WorkPlaceUser? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceUser → WorkPlaceCommunityMemberEntity failed because WorkPlaceUser was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceUser, WorkPlaceCommunityMemberEntity> to handle null values during mapping.');
    }
    return _i4.WorkPlaceCommunityMemberEntity(
      id: model.id,
      name: model.name,
      administrator: model.administrator,
      email: model.email,
      organization: model.organization,
      division: model.division,
      department: model.department,
      primaryPhone: model.primaryPhone,
      picture: _i21.WorkPlaceEntityMapper.mapToGPUserPicture(model),
      active: model.active,
      cover: _i21.WorkPlaceEntityMapper.mapToGPUserCover(model),
    );
  }

  _i8.WorkPlaceReactionEntity
      _map__i7$WorkPlaceReaction_To__i8$WorkPlaceReactionEntity(
          _i7.WorkPlaceReaction? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceReaction → WorkPlaceReactionEntity failed because WorkPlaceReaction was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceReaction, WorkPlaceReactionEntity> to handle null values during mapping.');
    }
    return _i8.WorkPlaceReactionEntity(
      userId: model.userId,
      username: model.username,
      type: model.type,
    );
  }

  _i8.WorkPlaceMessageTagsEntity
      _map__i9$WorkPlaceMessageTags_To__i8$WorkPlaceMessageTagsEntity(
          _i9.WorkPlaceMessageTags? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceMessageTags → WorkPlaceMessageTagsEntity failed because WorkPlaceMessageTags was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceMessageTags, WorkPlaceMessageTagsEntity> to handle null values during mapping.');
    }
    return _i8.WorkPlaceMessageTagsEntity(
      id: model.id,
      name: model.name,
      length: model.length,
      offset: model.offset,
      type: model.type,
    );
  }

  _i8.WorkPlaceCommentEntity
      _map__i10$WorkPlaceCommentsResponse_To__i8$WorkPlaceCommentEntity(
          _i10.WorkPlaceCommentsResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceCommentsResponse → WorkPlaceCommentEntity failed because WorkPlaceCommentsResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceCommentsResponse, WorkPlaceCommentEntity> to handle null values during mapping.');
    }
    return _i8.WorkPlaceCommentEntity(
      id: model.id,
      message: model.message,
      createdTime: model.createdTime,
      from:
          _map__i6$WorkPlaceUser_To__i4$WorkPlaceCommunityMemberEntity_Nullable(
              model.from),
      attachment:
          _map__i11$WorkPlacePostAttachmentsResponse_To__i12$WorkPlaceAttachmentEntity_Nullable(
              model.attachment),
      likeCount: model.likeCount,
      commentCount: model.commentCount,
      reactions: _i21.WorkPlaceEntityMapper.mapReactions(model),
      messageTags: model.messageTags
          ?.map<_i8.WorkPlaceMessageTagsEntity>((value) =>
              _map__i9$WorkPlaceMessageTags_To__i8$WorkPlaceMessageTagsEntity(
                  value))
          .toList(),
      likes: _i21.WorkPlaceEntityMapper.mapReactions(model),
    );
  }

  _i12.WorkPlaceAttachmentEntity
      _map__i11$WorkPlacePostAttachmentsResponse_To__i12$WorkPlaceAttachmentEntity(
          _i11.WorkPlacePostAttachmentsResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlacePostAttachmentsResponse → WorkPlaceAttachmentEntity failed because WorkPlacePostAttachmentsResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlacePostAttachmentsResponse, WorkPlaceAttachmentEntity> to handle null values during mapping.');
    }
    return _i12.WorkPlaceAttachmentEntity(
      target: _i21.WorkPlaceEntityMapper.mapToWorkPlaceAttachmentTarget(model),
      title: model.title,
      type: model.type,
      url: model.url,
      media: _i21.WorkPlaceEntityMapper.mapToWorkPlaceAttachmentMedia(model),
    );
  }

  _i14.WorkPlaceFeedEntity
      _map__i13$WorkPlaceFeedsResponse_To__i14$WorkPlaceFeedEntity(
          _i13.WorkPlaceFeedsResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceFeedsResponse → WorkPlaceFeedEntity failed because WorkPlaceFeedsResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceFeedsResponse, WorkPlaceFeedEntity> to handle null values during mapping.');
    }
    return _i14.WorkPlaceFeedEntity(
      id: model.id,
      message: model.message,
      updatedTime: model.updatedTime,
      formatting: model.formatting,
      createdTime: model.createdTime,
      attachments:
          _i21.WorkPlaceEntityMapper.mapToWorkPlaceAttachmentEntity(model),
      comments: _i21.WorkPlaceEntityMapper.mapToWorkPlaceCommentEntity(model),
      to: _i21.WorkPlaceEntityMapper.mapToListWorkPlaceUserEntity(model),
      from: _i21.WorkPlaceEntityMapper.mapToWorkPlaceUserEntity(model),
    );
  }

  _i16.WorkPlaceGroupEntity
      _map__i15$WorkPlaceGroupResponse_To__i16$WorkPlaceGroupEntity(
          _i15.WorkPlaceGroupResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceGroupResponse → WorkPlaceGroupEntity failed because WorkPlaceGroupResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceGroupResponse, WorkPlaceGroupEntity> to handle null values during mapping.');
    }
    return _i16.WorkPlaceGroupEntity(
      id: model.id,
      name: model.name,
      privacy: model.privacy,
      createdTime: model.createdTime,
      updatedTime: model.updatedTime,
      archived: model.archived,
      postRequiresAdminApproval: model.postRequiresAdminApproval,
      cover: _i21.WorkPlaceEntityMapper.mapToGroupCoverEntity(model),
      icon: model.icon,
      description: model.description,
      owner:
          _map__i6$WorkPlaceUser_To__i4$WorkPlaceCommunityMemberEntity_Nullable(
              model.owner),
      admins: _i21.WorkPlaceEntityMapper.mapToAdminWorkPlaceUserEntity(model),
      members: _i21.WorkPlaceEntityMapper.mapToMemberWorkPlaceUserEntity(model),
    );
  }

  _i12.AttachmentTargetEntity
      _map__i11$AttachmentTarget_To__i12$AttachmentTargetEntity(
          _i11.AttachmentTarget? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AttachmentTarget → AttachmentTargetEntity failed because AttachmentTarget was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AttachmentTarget, AttachmentTargetEntity> to handle null values during mapping.');
    }
    return _i12.AttachmentTargetEntity(
      id: model.id,
      url: model.url,
    );
  }

  _i12.AttachmentMediaEntity
      _map__i11$AttachmentMedia_To__i12$AttachmentMediaEntity(
          _i11.AttachmentMedia? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AttachmentMedia → AttachmentMediaEntity failed because AttachmentMedia was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AttachmentMedia, AttachmentMediaEntity> to handle null values during mapping.');
    }
    return _i12.AttachmentMediaEntity(
      image:
          _i21.WorkPlaceEntityMapper.mapToWorkPlaceAttachmentMediaImage(model),
      source: model.source,
    );
  }

  _i12.AttachmentMediaImageEntity
      _map__i11$AttachmentMediaImage_To__i12$AttachmentMediaImageEntity(
          _i11.AttachmentMediaImage? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AttachmentMediaImage → AttachmentMediaImageEntity failed because AttachmentMediaImage was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AttachmentMediaImage, AttachmentMediaImageEntity> to handle null values during mapping.');
    }
    return _i12.AttachmentMediaImageEntity(
      height: model.height,
      width: model.width,
      src: model.src,
    );
  }

  _i18.WorkPlaceConversationEntity
      _map__i17$WorkPlaceUserConversationsResponse_To__i18$WorkPlaceConversationEntity(
          _i17.WorkPlaceUserConversationsResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceUserConversationsResponse → WorkPlaceConversationEntity failed because WorkPlaceUserConversationsResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceUserConversationsResponse, WorkPlaceConversationEntity> to handle null values during mapping.');
    }
    return _i18.WorkPlaceConversationEntity(
      id: model.id,
      name: model.name,
      messages: _i21.WorkPlaceEntityMapper.mapToWorkPlaceMessagesEntity(model),
      participants: _i21.WorkPlaceEntityMapper.mapToParticipants(model),
      link: model.link,
    )..threadId = model.id;
  }

  _i18.WorkPlaceMessagesEntity
      _map__i17$Messages_To__i18$WorkPlaceMessagesEntity(_i17.Messages? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Messages → WorkPlaceMessagesEntity failed because Messages was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Messages, WorkPlaceMessagesEntity> to handle null values during mapping.');
    }
    return _i18.WorkPlaceMessagesEntity(
      id: model.id,
      message: model.message,
      from: _i21.WorkPlaceEntityMapper.mapToWorkPlaceUserEntityMessage(model),
      to: _i21.WorkPlaceEntityMapper.mapToListWorkPlaceUserEntityMessage(model),
      attachments: _i21.WorkPlaceEntityMapper
          .mapToWorkPlaceConversationAttachmentsEntity(model),
      createdTime: model.createdTime,
      sticker: _i21.WorkPlaceEntityMapper.mapToSticker(model),
    );
  }

  _i20.WorkPlaceConversationAttachmentsEntity
      _map__i19$WorkPlaceConversationAttachmentsResponse_To__i20$WorkPlaceConversationAttachmentsEntity(
          _i19.WorkPlaceConversationAttachmentsResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkPlaceConversationAttachmentsResponse → WorkPlaceConversationAttachmentsEntity failed because WorkPlaceConversationAttachmentsResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkPlaceConversationAttachmentsResponse, WorkPlaceConversationAttachmentsEntity> to handle null values during mapping.');
    }
    return _i20.WorkPlaceConversationAttachmentsEntity(
      id: model.id,
      mimeType: model.mimeType,
      name: model.name,
      size: model.size,
      imageData:
          _i21.WorkPlaceEntityMapper.mapToWorkPlaceAttachmentImageDataEntity(
              model),
      fileUrl: model.fileUrl,
      videoData:
          _i21.WorkPlaceEntityMapper.mapToWorkPlaceAttachmentVideoDataEntity(
              model),
    );
  }

  _i20.AttachmentImageDataEntity
      _map__i19$AttachmentImageData_To__i20$AttachmentImageDataEntity(
          _i19.AttachmentImageData? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AttachmentImageData → AttachmentImageDataEntity failed because AttachmentImageData was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AttachmentImageData, AttachmentImageDataEntity> to handle null values during mapping.');
    }
    return _i20.AttachmentImageDataEntity(
      height: model.height,
      width: model.width,
      url: model.url,
      imageType: model.imageType,
    );
  }

  _i20.AttachmentVideoDataEntity
      _map__i19$AttachmentVideoData_To__i20$AttachmentVideoDataEntity(
          _i19.AttachmentVideoData? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AttachmentVideoData → AttachmentVideoDataEntity failed because AttachmentVideoData was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AttachmentVideoData, AttachmentVideoDataEntity> to handle null values during mapping.');
    }
    return _i20.AttachmentVideoDataEntity(
      height: model.height,
      width: model.width,
      url: model.url,
      length: model.length,
      videoType: model.videoType,
    );
  }

  _i16.GroupCoverEntity _map__i15$GroupCover_To__i16$GroupCoverEntity(
      _i15.GroupCover? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GroupCover → GroupCoverEntity failed because GroupCover was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GroupCover, GroupCoverEntity> to handle null values during mapping.');
    }
    return _i16.GroupCoverEntity(
      id: model.id,
      source: model.source,
      coverId: model.coverId,
    );
  }

  _i4.WorkPlaceCommunityMemberEntity?
      _map__i6$WorkPlaceUser_To__i4$WorkPlaceCommunityMemberEntity_Nullable(
          _i6.WorkPlaceUser? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i4.WorkPlaceCommunityMemberEntity(
      id: model.id,
      name: model.name,
      administrator: model.administrator,
      email: model.email,
      organization: model.organization,
      division: model.division,
      department: model.department,
      primaryPhone: model.primaryPhone,
      picture: _i21.WorkPlaceEntityMapper.mapToGPUserPicture(model),
      active: model.active,
      cover: _i21.WorkPlaceEntityMapper.mapToGPUserCover(model),
    );
  }

  _i12.WorkPlaceAttachmentEntity?
      _map__i11$WorkPlacePostAttachmentsResponse_To__i12$WorkPlaceAttachmentEntity_Nullable(
          _i11.WorkPlacePostAttachmentsResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i12.WorkPlaceAttachmentEntity(
      target: _i21.WorkPlaceEntityMapper.mapToWorkPlaceAttachmentTarget(model),
      title: model.title,
      type: model.type,
      url: model.url,
      media: _i21.WorkPlaceEntityMapper.mapToWorkPlaceAttachmentMedia(model),
    );
  }
}
