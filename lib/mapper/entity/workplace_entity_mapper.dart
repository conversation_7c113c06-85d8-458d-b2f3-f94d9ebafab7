/*
 * Created Date: Tuesday, 11th June 2024, 08:46:28
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 26th June 2024 11:54:01
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs
// ignore_for_file: use_if_null_to_convert_nulls_to_bools

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/mapper/mapper.dart';

/// {@macro package:gp_fbwp_crawler/mapper/entity/workplace_entity_mapper.dart}
@AutoMappr(
  [
    MapType<FaceBookCommunityResponse, WorkPlaceCommunityEntity>(),
    MapType<WorkPlaceCommunityMemberEntity, GPUser>(fields: [
      Field(
        'id',
        custom: WorkPlaceEntityMapper.mapToGPUserId,
      ),
    ]),
    MapType<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(fields: [
      Field('picture', custom: WorkPlaceEntityMapper.mapToGPUserPicture),
      Field('cover', custom: WorkPlaceEntityMapper.mapToGPUserCover),
    ]),
    MapType<WorkPlaceReaction, WorkPlaceReactionEntity>(),
    MapType<WorkPlaceMessageTags, WorkPlaceMessageTagsEntity>(),
    MapType<WorkPlaceCommentsResponse, WorkPlaceCommentEntity>(fields: [
      Field('reactions', custom: WorkPlaceEntityMapper.mapReactions),
      Field('likes', custom: WorkPlaceEntityMapper.mapReactions),
    ]),
    MapType<WorkPlacePostAttachmentsResponse, WorkPlaceAttachmentEntity>(
        fields: [
          Field('target',
              custom: WorkPlaceEntityMapper.mapToWorkPlaceAttachmentTarget),
          Field(
            'subAttachments',
            ignore: true,
            // custom: WorkPlaceEntityMapper.mapToWorkPlaceSubAttachments,
          ),
          Field('media',
              custom: WorkPlaceEntityMapper.mapToWorkPlaceAttachmentMedia),
        ]),
    MapType<WorkPlaceFeedsResponse, WorkPlaceFeedEntity>(fields: [
      Field('attachments',
          custom: WorkPlaceEntityMapper.mapToWorkPlaceAttachmentEntity),
      Field('comments',
          custom: WorkPlaceEntityMapper.mapToWorkPlaceCommentEntity),
      Field('from', custom: WorkPlaceEntityMapper.mapToWorkPlaceUserEntity),
      Field('to', custom: WorkPlaceEntityMapper.mapToListWorkPlaceUserEntity),
    ]),
    MapType<WorkPlaceGroupResponse, WorkPlaceGroupEntity>(fields: [
      Field('admins',
          custom: WorkPlaceEntityMapper.mapToAdminWorkPlaceUserEntity),
      Field('members',
          custom: WorkPlaceEntityMapper.mapToMemberWorkPlaceUserEntity),
      Field('cover', custom: WorkPlaceEntityMapper.mapToGroupCoverEntity),
    ]),
    MapType<AttachmentTarget, AttachmentTargetEntity>(),
    // MapType<SubAttachments, SubAttachmentsEntity>(fields: [
    //   Field('data',
    //       custom: WorkPlaceEntityMapper.mapToWorkPlaceSubAttachmentsData)
    // ]),
    MapType<AttachmentMedia, AttachmentMediaEntity>(fields: [
      Field('image',
          custom: WorkPlaceEntityMapper.mapToWorkPlaceAttachmentMediaImage)
    ]),
    MapType<AttachmentMediaImage, AttachmentMediaImageEntity>(),
    MapType<WorkPlaceUserConversationsResponse, WorkPlaceConversationEntity>(
        fields: [
          Field('messages',
              custom: WorkPlaceEntityMapper.mapToWorkPlaceMessagesEntity),
          Field('threadId', from: 'id'),
          Field('participants',
              custom: WorkPlaceEntityMapper.mapToParticipants),
        ]),
    MapType<Messages, WorkPlaceMessagesEntity>(fields: [
      Field('attachments',
          custom: WorkPlaceEntityMapper
              .mapToWorkPlaceConversationAttachmentsEntity),
      Field('from',
          custom: WorkPlaceEntityMapper.mapToWorkPlaceUserEntityMessage),
      Field('to',
          custom: WorkPlaceEntityMapper.mapToListWorkPlaceUserEntityMessage),
      Field('sticker', custom: WorkPlaceEntityMapper.mapToSticker),
    ]),
    MapType<WorkPlaceConversationAttachmentsResponse,
        WorkPlaceConversationAttachmentsEntity>(fields: [
      Field(
        'imageData',
        custom: WorkPlaceEntityMapper.mapToWorkPlaceAttachmentImageDataEntity,
      ),
      Field(
        'videoData',
        custom: WorkPlaceEntityMapper.mapToWorkPlaceAttachmentVideoDataEntity,
      )
    ]),
    MapType<AttachmentImageData, AttachmentImageDataEntity>(),
    MapType<AttachmentVideoData, AttachmentVideoDataEntity>(),
    MapType<GroupCover, GroupCoverEntity>(),
  ],
  includes: [],
)
class WorkPlaceEntityMapper extends $WorkPlaceEntityMapper {
  const WorkPlaceEntityMapper();

  static int mapToGPUserId(WorkPlaceCommunityMemberEntity input) {
    return int.parse(input.id);
  }

  static String? mapToGPUserPicture(WorkPlaceUser input) {
    if (input.picture == null) return null;
    return input.picture!.data.url;
  }

  static String? mapToGPUserCover(WorkPlaceUser input) {
    if (input.cover == null) return null;
    return input.cover!.source;
  }

  static List<WorkPlaceAttachmentEntity>? mapToWorkPlaceAttachmentEntity(
      WorkPlaceFeedsResponse input) {
    if (input.attachments == null) return null;

    List<WorkPlaceAttachmentEntity> flatternAttachments = [];

    const mapper = WorkPlaceEntityMapper();

    for (var element in input.attachments!.data) {
      flatternAttachments.add(
        mapper.convert<WorkPlacePostAttachmentsResponse,
            WorkPlaceAttachmentEntity>(element),
      );

      if (element.hasSubAttachment) {
        final subAttachments = mapper.convertList<
            WorkPlacePostAttachmentsResponse,
            WorkPlaceAttachmentEntity>(element.subAttachments!.data!);

        flatternAttachments.addAll(subAttachments);
      }
    }

    flatternAttachments.removeWhere((element) => element.url == null);

    return flatternAttachments;
  }

  static List<WorkPlaceCommentEntity>? mapToWorkPlaceCommentEntity(
      WorkPlaceFeedsResponse? input) {
    if (input?.comments == null) return null;
    return input?.comments?.data
        .map((e) => const WorkPlaceEntityMapper()
            .convert<WorkPlaceCommentsResponse, WorkPlaceCommentEntity>(e))
        .toList();
  }

  static AttachmentTargetEntity? mapToWorkPlaceAttachmentTarget(
      WorkPlacePostAttachmentsResponse input) {
    if (input.target == null) return null;
    return const WorkPlaceEntityMapper()
        .convert<AttachmentTarget, AttachmentTargetEntity>(input.target);
  }

  // static SubAttachmentsEntity? mapToWorkPlaceSubAttachments(
  //     WorkPlacePostAttachmentsResponse input) {
  //   if (input.subAttachments == null) return null;
  //   return const WorkPlaceEntityMapper()
  //       .convert<SubAttachments, SubAttachmentsEntity>(input.subAttachments);
  // }

  static AttachmentMediaEntity? mapToWorkPlaceAttachmentMedia(
      WorkPlacePostAttachmentsResponse? input) {
    if (input?.media == null) return null;
    return const WorkPlaceEntityMapper()
        .convert<AttachmentMedia, AttachmentMediaEntity>(input?.media);
  }

  static List<WorkPlaceAttachmentEntity>? mapToWorkPlaceSubAttachmentsData(
      SubAttachments input) {
    if (input.data == null) return null;
    return input.data
        ?.map((e) => const WorkPlaceEntityMapper().convert<
            WorkPlacePostAttachmentsResponse, WorkPlaceAttachmentEntity>(e))
        .toList();
  }

  static AttachmentMediaImageEntity? mapToWorkPlaceAttachmentMediaImage(
    AttachmentMedia input,
  ) {
    if (input.image == null) return null;
    return const WorkPlaceEntityMapper()
        .convert<AttachmentMediaImage, AttachmentMediaImageEntity>(input.image);
  }

  static WorkPlaceCommunityMemberEntity? mapToWorkPlaceUserEntity(
    WorkPlaceFeedsResponse input,
  ) {
    if (input.from == null) return null;
    return const WorkPlaceEntityMapper()
        .convert<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(input.from);
  }

  static List<WorkPlaceCommunityMemberEntity>? mapToListWorkPlaceUserEntity(
    WorkPlaceFeedsResponse input,
  ) {
    if (input.to == null) return null;
    return const WorkPlaceEntityMapper()
        .convertList<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(
            input.to!.data);
  }

  static List<WorkPlaceMessagesEntity>? mapToWorkPlaceMessagesEntity(
      WorkPlaceUserConversationsResponse input) {
    if (input.messages == null) return null;

    return const WorkPlaceEntityMapper()
        .convertList<Messages, WorkPlaceMessagesEntity>(input.messages!.data);
  }

  static List<WorkPlaceCommunityMemberEntity>? mapToParticipants(
      WorkPlaceUserConversationsResponse input) {
    if (input.participants == null) return null;

    return const WorkPlaceEntityMapper()
        .convertList<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(
            input.participants!.data);
  }

  static WorkPlaceCommunityMemberEntity? mapToWorkPlaceUserEntityMessage(
      Messages input) {
    if (input.from == null) return null;
    return const WorkPlaceEntityMapper()
        .convert<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(input.from);
  }

  static List<WorkPlaceCommunityMemberEntity>?
      mapToListWorkPlaceUserEntityMessage(Messages input) {
    if (input.to == null) return null;
    return const WorkPlaceEntityMapper()
        .convertList<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(
            input.to!.data);
  }

  static List<WorkPlaceConversationAttachmentsEntity>?
      mapToWorkPlaceConversationAttachmentsEntity(Messages input) {
    if (input.attachments == null) return null;

    final data = const WorkPlaceEntityMapper().convertList<
        WorkPlaceConversationAttachmentsResponse,
        WorkPlaceConversationAttachmentsEntity>(input.attachments!.data);
    return data;
  }

  static WorkPlaceStickerEntity? mapToSticker(Messages input) {
    if (input.sticker == null) return null;
    final data = WorkPlaceStickerEntity(url: input.sticker);
    return data;
  }

  static AttachmentImageDataEntity? mapToWorkPlaceAttachmentImageDataEntity(
      WorkPlaceConversationAttachmentsResponse input) {
    if (input.imageData == null) return null;
    return const WorkPlaceEntityMapper()
        .convert<AttachmentImageData, AttachmentImageDataEntity>(
            input.imageData);
  }

  static AttachmentVideoDataEntity? mapToWorkPlaceAttachmentVideoDataEntity(
      WorkPlaceConversationAttachmentsResponse input) {
    if (input.videoData == null) return null;
    return const WorkPlaceEntityMapper()
        .convert<AttachmentVideoData, AttachmentVideoDataEntity>(
            input.videoData);
  }

  static List<WorkPlaceCommunityMemberEntity>? mapToAdminWorkPlaceUserEntity(
      WorkPlaceGroupResponse input) {
    if (input.admins == null) return null;

    return const WorkPlaceEntityMapper()
        .convertList<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(
            input.admins!.data);
  }

  static List<WorkPlaceCommunityMemberEntity>? mapToMemberWorkPlaceUserEntity(
      WorkPlaceGroupResponse input) {
    if (input.members == null) return null;

    return const WorkPlaceEntityMapper()
        .convertList<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(
            input.members!.data);
  }

  static GroupCoverEntity? mapToGroupCoverEntity(WorkPlaceGroupResponse input) {
    if (input.cover == null) return null;

    return const WorkPlaceEntityMapper()
        .convert<GroupCover, GroupCoverEntity>(input.cover);
  }

  static List<WorkPlaceReactionEntity>? mapReactions(
      WorkPlaceCommentsResponse input) {
    if (input.reactions == null) return null;

    return const WorkPlaceEntityMapper()
        .convertList<WorkPlaceReaction, WorkPlaceReactionEntity>(
            input.reactions!.data);
  }
}
