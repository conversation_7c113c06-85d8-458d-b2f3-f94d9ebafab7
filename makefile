format_check:
	dart format --set-exit-if-changed .
	dart run pubspec_dependency_sorter

gen:
	fluttergen
	dart run flutter_launcher_icons -f project_configs/flutter_launcher_icons-dev.yaml
	dart run package_rename_plus --path=project_configs/package_rename_config-dev.yaml

prepare_uat:
	fluttergen
	dart run flutter_launcher_icons -f project_configs/flutter_launcher_icons-uat.yaml
	dart run package_rename_plus --path=project_configs/package_rename_config-uat.yaml

prepare_prod:
	fluttergen
	dart run flutter_launcher_icons -f project_configs/flutter_launcher_icons-prod.yaml
	dart run package_rename_plus --path=project_configs/package_rename_config-prod.yaml

build_runner:
	dart run build_runner build --delete-conflicting-outputs
