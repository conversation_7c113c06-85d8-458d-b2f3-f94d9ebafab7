package_rename_config:
  android:
    app_name: Gapo Crawler Dev # (String) The display name of the android app
    package_name: vn.gapowork.crawler # (String) The package name of the android app
    override_old_package: # (Optional) (String) Use this to delete the old folder structure of MainActivity or to use the existing code with the new package name
    lang: # (Optional) (String) The android development language {kotlin(default) or java}

  ios:
    app_name: Gapo Crawler Dev # (String) The display name of the ios app
    bundle_name: vn.gapowork.crawler # (String) The bundle name of the ios app
    override_old_package: # (String) Use this to replace the old bundle identifier with the new bundle identifier
    package_name: # (String) The product bundle identifier of the ios app

  linux:
    app_name: Gapo Crawler Dev # (String) The window title of the linux app
    package_name: vn.gapowork.crawler # (String) The application id of the linux app
    exe_name: GapoCrawler # (String) The executable name (binary name) of the linux app

  macos:
    app_name: Gapo Crawler Dev # (String) The product name of the macos app
    package_name: vn.gapowork.crawler # (String) The product bundle identifier of the macos app
    copyright_notice: Copyright (c) 2021 - 2024 GAPO # (String) The product copyright of the macos app

  web:
    app_name: Gapo Crawler Dev # (String) The title and display name of the web app and PWA
    description: GapoCrawler # (String) The description of the web app and PWA

  windows:
    app_name: Gapo Crawler Dev VietinBank # (String) The window title & software name of the windows app
    organization: GapoXVietinBank # (String) The organization name (company name) of the windows app
    copyright_notice: Copyright (c) 2021 - 2024 GAPO # (String) The legal copyright of the windows app
    exe_name: GapoCrawleVietinBank # (String) The executable name (binary name) of the windows app
