#!/bin/bash

database_name=$1

# Check if at least one argument is provided
if [ "$#" -eq 0 ]; then
    echo "Warning: No arguments provided. Please provide the database name."
    exit 1  # Exit the script with error code
else
    echo "Database name: $@"
fi

DATA_DIR="data"
DESTINATION_DIR="$HOME/Library/Containers/vn.gapowork.crawler/Data/Documents/multi-client"

if [[ "$OSTYPE" == "msys" ]]; then
    DESTINATION_DIR="$HOME/Documents/multi-client"
fi

copy_directory() {
    local source_dir=$1
    local destination_dir=$2

    if [ -d "$source_dir" ]; then
        cp -r "$source_dir"/* "$destination_dir"
        echo "Directory '$source_dir' copied to '$destination_dir'."
    else
        echo "Source directory '$source_dir' does not exist."
    fi
}

rename_directory() {
    local old_dir=$1
    local new_dir=$2

    if [ -d "$old_dir" ]; then
        mv "$old_dir" "$new_dir"
        echo "Directory renamed from '$old_dir' to '$new_dir'."
    else
        echo "Directory '$old_dir' does not exist."
    fi
}

git pull

copy_directory "$DATA_DIR" "$DESTINATION_DIR"
rename_directory "$DESTINATION_DIR/base" "$DESTINATION_DIR/$database_name"

echo "Copied directory '$DATA_DIR' to '$DESTINATION_DIR'"