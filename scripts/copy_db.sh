#!/bin/bash

DATABASE_NAME=$1

# Check if at least one argument is provided
if [ "$#" -eq 0 ]; then
    echo "Warning: No arguments provided. Please provide the database name."
    exit 1  # Exit the script with error code
else
    echo "Database name: $@"
fi

DATA_DIR="$HOME/Library/Containers/vn.gapowork.crawler/Data/Documents/multi-client"
DESTINATION_DIR="data"

if [[ "$OSTYPE" == "msys" ]]; then
    DATA_DIR="$HOME/Documents/multi-client"
fi

read_env_value() {
    local key=$1
    local env_file=".env"

    if [ -f "$env_file" ]; then
        # Use grep to find the key and cut to extract the value
        value=$(grep "^$key=" "$env_file" | cut -d '=' -f2)
        echo "$value"
    else
        echo ".env file does not exist."
    fi
}

COMPUTER_ID=$(read_env_value "COMPUTER_ID")

copy_file() {
    local source_dir=$1
    local destination_dir=$2

    if [ -d "$source_dir" ]; then
        cp "$source_dir"/* "$destination_dir"
        echo "Directory '$source_dir' copied to '$destination_dir'."
    else
        echo "Source directory '$source_dir' does not exist."
    fi
}

copy_file "$DATA_DIR/$DATABASE_NAME" "$DESTINATION_DIR/$COMPUTER_ID"
echo "Copied directory '$DATA_DIR' to '$DESTINATION_DIR'"

git pull

git add data/*

git commit -m "multi client: client data"

git push
