#!/bin/bash

DATA_DIR="$HOME/Library/Containers/vn.gapowork.crawler/Data/Documents/multi-client/"
DESTINATION_DIR="data"

DATABASE_NAME=$1

# Check if at least one argument is provided
if [ "$#" -eq 0 ]; then
    echo "Warning: No arguments provided. Please provide the database name."
    exit 1  # Exit the script with error code
else
    echo "Database name: $@"
fi

if [[ "$OSTYPE" == "msys" ]]; then
    DATA_DIR="$HOME/Documents/multi-client"
fi

copy_directory() {
    local source_dir=$1
    local destination_dir=$2

    if [ -d "$source_dir" ]; then
        rsync -av --exclude="$DATABASE_NAME" "$source_dir/" "$destination_dir"
        echo "Directory '$source_dir' copied to '$destination_dir'."
    else
        echo "Source directory '$source_dir' does not exist."
    fi
}

copy_directory "$DATA_DIR" "$DESTINATION_DIR"
echo "Copied directory '$DATA_DIR' to '$DESTINATION_DIR'"