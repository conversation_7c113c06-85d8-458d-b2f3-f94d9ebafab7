import 'dart:io';

void main() async {
  print('Testing data copy functionality...');
  
  // <PERSON><PERSON><PERSON> tra thư mục data có tồn tại không
  final dataDir = Directory('data');
  print('Current directory: ${Directory.current.path}');
  print('Data directory exists: ${await dataDir.exists()}');
  
  if (await dataDir.exists()) {
    print('Contents of data directory:');
    await for (final entity in dataDir.list(recursive: true)) {
      if (entity is File) {
        print('  File: ${entity.path}');
      } else if (entity is Directory) {
        print('  Directory: ${entity.path}');
      }
    }
  }
  
  // Test copy một file đơn giản
  await testSimpleCopy();
}

Future<void> testSimpleCopy() async {
  try {
    print('\n--- Testing simple file copy ---');
    
    // Tạo thư mục test
    final testDir = Directory('test_output');
    if (await testDir.exists()) {
      await testDir.delete(recursive: true);
    }
    await testDir.create();
    
    // Copy file computer_ids.txt nếu tồn tại
    final sourceFile = File('data${Platform.pathSeparator}computer_ids.txt');
    if (await sourceFile.exists()) {
      final destFile = File('test_output${Platform.pathSeparator}computer_ids.txt');
      await sourceFile.copy(destFile.path);
      print('Successfully copied computer_ids.txt');
      print('Source: ${sourceFile.path}');
      print('Destination: ${destFile.path}');
    } else {
      print('Source file computer_ids.txt does not exist');
    }
    
    // Copy thư mục base nếu tồn tại
    final baseDir = Directory('data${Platform.pathSeparator}base');
    if (await baseDir.exists()) {
      final destBaseDir = Directory('test_output${Platform.pathSeparator}base');
      await destBaseDir.create();
      
      await for (final entity in baseDir.list()) {
        if (entity is File) {
          final fileName = entity.uri.pathSegments.last;
          final destFile = File('${destBaseDir.path}${Platform.pathSeparator}$fileName');
          await entity.copy(destFile.path);
          print('Copied: $fileName');
        }
      }
    }
    
    print('Test completed successfully!');
    
  } catch (e) {
    print('Error during test: $e');
  }
}
